<?xml version="1.0" encoding="UTF-8"?>
<!-- Additional Maven configuration for enhanced reporting -->
<project>
    <build>
        <plugins>
            <!-- Cucumber Reports Plugin -->
            <plugin>
                <groupId>net.masterthought</groupId>
                <artifactId>maven-cucumber-reporting</artifactId>
                <version>5.7.5</version>
                <executions>
                    <execution>
                        <id>execution</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <projectName>Logistics QA Automation</projectName>
                            <outputDirectory>${project.build.directory}/reports</outputDirectory>
                            <inputDirectory>${project.build.directory}</inputDirectory>
                            <jsonFiles>
                                <param>**/*.json</param>
                            </jsonFiles>
                            <checkBuildResult>false</checkBuildResult>
                            <buildNumber>${env.BUILD_NUMBER}</buildNumber>
                            <parallelTesting>false</parallelTesting>
                            <mergeFeaturesById>false</mergeFeaturesById>
                            <mergeFeaturesWithRetest>false</mergeFeaturesWithRetest>
                            <trendsStatsFile>${project.build.directory}/reports/trends.json</trendsStatsFile>
                            <classifications>
                                <classification>
                                    <key>Environment</key>
                                    <value>${tier}</value>
                                </classification>
                                <classification>
                                    <key>Branch</key>
                                    <value>${env.BRANCH_NAME}</value>
                                </classification>
                                <classification>
                                    <key>Build</key>
                                    <value>${env.BUILD_NUMBER}</value>
                                </classification>
                            </classifications>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
