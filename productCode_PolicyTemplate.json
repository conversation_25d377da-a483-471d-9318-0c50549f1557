{"templateJson": "[\n  {\n    \"clientId\": \"803517a8-c42a-44c3-b54b-fccafaec1ad3\",\n    \"cells\": [\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"ea61effc-4a76-4471-85f9-a154d018e305\",\n            \"cellName\": \"transactionType\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Transaction type\",\n                \"placeholder\": \"Select transaction type\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"transactionType\"]\n                },\n                \"options\": [\n                  {\n                    \"id\": \"New\",\n                    \"label\": \"New Business\",\n                    \"name\": \"New\"\n                  },\n                  {\n                    \"id\": \"Renewal\",\n                    \"label\": \"Renewal\",\n                    \"name\": \"Renewal\"\n                  }\n                ],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"transactionType\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"1b41573f-2941-4b04-b3d4-16cd9b74585a\",\n            \"cellName\": \"status\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Status\",\n                \"placeholder\": \"Select status\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"policyStatus\"]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": [\n                      {\n                        \"key\": \"transactionType\",\n                        \"value\": \"\",\n                        \"mandatory\": true\n                      }\n                    ]\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"submissionPolicyStatus\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"mapKeyFromData\": [\n                    {\n                      \"mapDataFrom\": \"transactionType\",\n                      \"mapType\": \"arrayItem\",\n                      \"mapDataTo\": \"metaDataFilters|0|value\",\n                      \"mapDataIn\": \"body\"\n                    }\n                  ],\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"1b41573f-2941-4b04-b3d4-16cd9b74585a\",\n            \"cellName\": \"definedStatus\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Defined status\",\n                \"placeholder\": \"Select status\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"definedStatus\"]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"customClass\": \"wrapText\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": [\n                      {\n                        \"key\": \"transactionType\",\n                        \"value\": \"\",\n                        \"mandatory\": true\n                      },\n                      {\n                        \"key\": \"submissionPolicyStatus\",\n                        \"value\": \"\",\n                        \"mandatory\": true\n                      }\n                    ]\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"definedStatus\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"mapKeyFromData\": [\n                    {\n                      \"mapDataFrom\": \"transactionType\",\n                      \"mapType\": \"arrayItem\",\n                      \"mapDataTo\": \"metaDataFilters|0|value\",\n                      \"mapDataIn\": \"body\"\n                    },\n                    {\n                      \"mapDataFrom\": \"policyStatus\",\n                      \"mapType\": \"arrayItem\",\n                      \"mapDataTo\": \"metaDataFilters|1|value\",\n                      \"mapDataIn\": \"body\"\n                    }\n                  ],\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"091dd312-a82e-423c-b2f8-21243e48772b\",\n            \"cellName\": \"policyInceptionDate\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDateWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Policy inception\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"policyInceptionDate\"]\n                },\n                \"disableDate\": [\n                  {\n                    \"type\": \"isAfter\",\n                    \"key\": \"policyExpirationDate\"\n                  }\n                ]\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"7e9070d8-0683-4227-bcdf-0746c16a4bf8\",\n            \"cellName\": \"policyExpirationDate\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDateWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Policy expiration\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"policyExpirationDate\"]\n                },\n                \"disableDate\": [\n                  {\n                    \"type\": \"isBefore\",\n                    \"key\": \"policyInceptionDate\"\n                  }\n                ]\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"029b9916-0e47-4f95-bba9-3e6d95e6578a\",\n            \"cellName\": \"underwriterName\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Underwriter\",\n                \"placeholder\": \"Select underwriter\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"underwriterName\"],\n                  \"otherNameInSourcedSystemParams\": [\n                    {\n                      \"key\": \"underwriterEmail\",\n                      \"valueKeyToSave\": \"id\"\n                    },\n                    {\n                      \"key\": \"assignedUser.firstName\",\n                      \"valueKeyToSave\": \"firstName\"\n                    },\n                    {\n                      \"key\": \"assignedUser.lastName\",\n                      \"valueKeyToSave\": \"lastName\"\n                    },\n                    {\n                      \"key\": \"assignedUser.userRoles\",\n                      \"valueKeyToSave\": \"roles\"\n                    },\n                    {\n                      \"key\": \"assignedUser.userId\",\n                      \"valueKeyToSave\": \"userId\"\n                    }\n                  ]\n                },\n                \"keyToSave\": \"name\",\n                \"options\": [],\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/access-management/api/v1/users\",\n                  \"method\": \"GET\",\n                  \"requestBody\": {},\n                  \"queryParams\": {\n                    \"page\": 0,\n                    \"size\": 100,\n                    \"roles\": \"SUBMISSION_UW\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": false\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchKey\": \"startWith\",\n                    \"searchPlace\": \"query\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"result\",\n                    \"labelMapper\": {\n                      \"id\": \"email\",\n                      \"name\": [\"firstName\", \"lastName\"],\n                      \"firstName\": \"firstName\",\n                      \"lastName\": \"lastName\",\n                      \"roles\": \"roles\",\n                      \"userId\": \"userId\"\n                    }\n                  }\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"2168cd66-a81c-44d2-9f76-a0e2724359dd\",\n            \"cellName\": \"underwriterEmail\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Underwriter email\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"underwriterEmail\"]\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"20a9ee1a-9625-4cc9-a97a-ec4eb1f64543\",\n            \"cellName\": \"transactionDate\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDateWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Transaction/Process date\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"transactionDate\"]\n                },\n                \"disabled\": true\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"d71f25d0-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"submissionNumber\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Submission/Customer number\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"submissionNumber\"]\n                },\n                \"conditionalDisabled\": {\n                  \"status\": \"QUOTED\"\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"priorPolicyNumber\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Prior policy number\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"priorPolicyNumber\"]\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": false,\n          \"containerConfig\": {\n            \"title\": \"Submission Information\",\n            \"labelTooltip\": null,\n            \"parentContainerId\": \"submission_information\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 6,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"meta\": {\n          \"cellConfig\": {\n            \"col\": 6,\n            \"style\": {},\n            \"visibleCondition\": null\n          },\n          \"componentConfig\": null,\n          \"containerConfig\": {\n            \"title\": \"Broker Information\",\n            \"labelTooltip\": null\n          },\n          \"isWrapperRequired\": false\n        },\n        \"cells\": [\n          {\n            \"id\": \"d71f25d0-583a-4dfb-a4bf-1423423434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"async\": true,\n                \"label\": \"Broker name (company)\",\n                \"asyncType\": \"gql\",\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"brokerInfo.name\"],\n                  \"otherNameInSourcedSystemParams\": [\n                    {\n                      \"key\": \"brokerageId\",\n                      \"valueKeyToSave\": \"id\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.senderName\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerId\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.id\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.team\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.senderEmail\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.street\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.city\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.state\",\n                      \"valueToSave\": \"\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.zipCode\",\n                      \"valueToSave\": \"\"\n                    }\n                  ]\n                },\n                \"keyToSave\": \"name\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/graphql\",\n                  \"method\": \"POST\",\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchKey\": \"input.searchText\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"pageKey\": \"input.page\",\n                    \"sizeKey\": \"input.size\",\n                    \"defaultSize\": 20\n                  },\n                  \"queryParams\": {},\n                  \"requestBody\": {\n                    \"query\": \"query SearchBrokerage($input: SearchBrokerageInput!) { searchBrokerage(input: $input) { totalElements items { officeCount brokerage { id name brokerCode businessType createdAt  __typename }  __typename } pageableInfo { page size __typename } __typename } }\",\n                    \"variables\": {\n                      \"input\": {\n                        \"searchText\": \"\",\n                        \"page\": 0,\n                        \"size\": 30\n                      }\n                    }\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"data.searchBrokerage.items\",\n                    \"labelMapper\": {\n                      \"id\": \"brokerage.id\",\n                      \"name\": \"brokerage.name\"\n                    },\n                    \"totalItemsKey\": \"data.searchBrokerage.totalElements\"\n                  }\n                },\n                \"dropdownPlaceholder\": \"Select Broker\"\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.name\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"123123-3123123-4dfb-a4bf-1423423434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker name (sender)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"brokerInfo.senderName\"],\n                  \"otherNameInSourcedSystemParams\": [\n                    {\n                      \"key\": \"brokerId\",\n                      \"valueKeyToSave\": \"id\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.id\",\n                      \"valueKeyToSave\": \"id\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.team\",\n                      \"valueKeyToSave\": \"team\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.senderEmail\",\n                      \"valueKeyToSave\": \"senderEmail\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.street\",\n                      \"valueKeyToSave\": \"addressLine1\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.city\",\n                      \"valueKeyToSave\": \"city\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.state\",\n                      \"valueKeyToSave\": \"state\"\n                    },\n                    {\n                      \"key\": \"brokerInfo.mailingAddress.zipCode\",\n                      \"valueKeyToSave\": \"zipCode\"\n                    },\n                    {\n                      \"key\": \"officeId\",\n                      \"valueKeyToSave\": \"officeId\"\n                    }\n                  ]\n                },\n                \"initialAsyncValidation\": {\n                  \"enabled\": true,\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/graphql\",\n                  \"method\": \"POST\",\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchKey\": \"input.searchName\"\n                  },\n                  \"mapKeyFromData\": [\n                    {\n                      \"mapDataFrom\": \"brokerInfo.name\",\n                      \"mapType\": \"arrayString\",\n                      \"mapDataTo\": \"input.brokerageNames\",\n                      \"mapDataIn\": \"body\"\n                    }\n                  ],\n                  \"requestBody\": {\n                    \"query\": \"query SearchBroker($input: SearchBrokerInput!) { searchBroker(input: $input) { totalElements pageableInfo { page size } items { broker { id } } } }\",\n                    \"variables\": {\n                      \"input\": {\n                        \"searchText\": \"\",\n                        \"page\": 0,\n                        \"size\": 30\n                      }\n                    }\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"data.searchBroker.items\",\n                    \"labelMapper\": {\n                      \"id\": \"broker.id\"\n                    },\n                    \"totalItemsKey\": \"data.searchBroker.totalElements\"\n                  },\n                  \"invalidErrorMessage\": \"Does not match with the selected brokerage\"\n                },\n                \"async\": true,\n                \"asyncType\": \"gql\",\n                \"keyToSave\": \"name\",\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/graphql\",\n                  \"method\": \"POST\",\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchKey\": \"input.searchText\"\n                  },\n                  \"mapKeyFromData\": [\n                    {\n                      \"mapDataFrom\": \"brokerInfo.name\",\n                      \"mapType\": \"arrayString\",\n                      \"mapDataTo\": \"input.brokerageNames\",\n                      \"mapDataIn\": \"body\"\n                    }\n                  ],\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"pageKey\": \"input.page\",\n                    \"sizeKey\": \"input.size\",\n                    \"defaultSize\": 10\n                  },\n                  \"queryParams\": {},\n                  \"requestBody\": {\n                    \"query\": \" query SearchBroker($input: SearchBrokerInput!) { searchBroker(input: $input) { totalElements pageableInfo { page size } items { brokerageId brokerageName officeId officeCity officeState officeAddressLine1 officeAddressLine2 officeZipCode broker { id firstName lastName emailAddress team  address { addressLine1 city zipcode state } } } } }\",\n                    \"variables\": {\n                      \"input\": {\n                        \"page\": 0,\n                        \"size\": 10,\n                        \"sortInput\": \"firstName\",\n                        \"sortOrder\": \"asc\"\n                      }\n                    }\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"data.searchBroker.items\",\n                    \"labelMapper\": {\n                      \"id\": \"broker.id\",\n                      \"name\": [\"broker.firstName\", \"broker.lastName\"],\n                      \"team\": \"broker.team\",\n                      \"senderEmail\": \"broker.emailAddress\",\n                      \"addressLine1\": [\"officeAddressLine1\", \"officeAddressLine2\"],\n                      \"city\": \"officeCity\",\n                      \"state\": \"officeState\",\n                      \"zipCode\": \"officeZipCode\",\n                      \"officeId\": \"officeId\"\n                    },\n                    \"totalItemsKey\": \"data.searchBroker.totalElements\"\n                  }\n                },\n                \"dropdownPlaceholder\": \"Select Broker\"\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.senderName\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"d71f25d0-3123123-4dfb-a4bf-1423423434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker team\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"brokerInfo.team\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.team\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"123123-3123123-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker email\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"brokerInfo.senderEmail\"]\n                },\n                \"validations\": {\n                  \"email\": true\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.senderEmail\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"123123-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker mailing street address\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"brokerInfo.mailingAddress.street\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.mailingAddress.street\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker mailing city\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"brokerInfo.mailingAddress.city\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.mailingAddress.city\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf-22123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker mailing state\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"brokerInfo.mailingAddress.state\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.mailingAddress.state\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf-21321\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker mailing zip code\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"brokerInfo.mailingAddress.zipCode\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"brokerInfo.mailingAddress.zipCode\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          }\n        ],\n        \"cellType\": \"container\",\n        \"parentId\": \"19fb1367-99c4-42c0-9f47-fbe6e51a4f98\",\n        \"component\": \"TemplateCard\"\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.firstName\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"First named insured\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.firstName\"]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.dba\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"DBA\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.dba\"]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.otherNamedInsureds\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Other named insured(s)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.otherNamedInsureds\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8,\n                \"valueFormat\": \"Array\"\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.owner\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Named insured - owner\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.owner\"]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.generalContractor\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Named insured - General Contractor/Construction manager\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.generalContractor\"]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.street1\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Mailing street address 1\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.street1\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.street2\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Mailing street address 2\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.street2\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.city\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Mailing city\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.city\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.state\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Mailing state\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.state\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.zipCode\",\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Mailing zipcode\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.zipCode\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": false,\n          \"containerConfig\": {\n            \"title\": \"Insured Information\",\n            \"labelTooltip\": null\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"descriptionOfOperations\",\n            \"component\": \"BaseTextArea\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"\",\n                \"name\": \"Description of operations\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"descriptionOfOperations\"]\n                }\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"cellName\": \"riskState\",\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Risk state\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"riskState\"]\n                },\n                \"placeholder\": \"Select risk state\",\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"isMultiple\": true,\n                \"checkbox\": true,\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"riskStates\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 100\n                  },\n                  \"search\": {\n                    \"enabled\": false\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": [\"metadata.abbreviation\", \"dataValue\"]\n                    },\n                    \"concatString\": \" - \",\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf987e27bke2\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"New Jersey Transaction Number\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"newJerseyTransactionNumber\"\n                  ]\n                },\n                \"conditionalHidden\": {\n                  \"riskState\": {\n                    \"type\": \"not_contains\",\n                    \"matchKey\": \"\",\n                    \"data\": \"NJ\"\n                  }\n                },\n                \"validations\": {\n                  \"required\": true\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.newJerseyTransactionNumber\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": false,\n          \"containerConfig\": {\n            \"title\": \"Description of Operations\",\n            \"labelTooltip\": null,\n            \"parentContainerId\": \"description_of_operations\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"cellType\": \"container\",\n            \"component\": \"TemplateCard\",\n            \"cellName\": \"scheduleOfValues\",\n            \"cells\": [\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"cellName\": \"scheduleOfValues\",\n                \"component\": \"BaseInputWithLabel\",\n                \"meta\": {\n                  \"containerConfig\": null,\n                  \"componentConfig\": {\n                    \"label\": \"Schedule of values\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"scheduleOfValues\"]\n                    },\n                    \"inputType\": \"currency\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              }\n            ],\n            \"meta\": {\n              \"componentConfig\": null,\n              \"isWrapperRequired\": false,\n              \"containerConfig\": {\n                \"title\": \"Location/Project details\",\n                \"outerPadding\": \"none\",\n                \"labelTooltip\": null,\n                \"shadow\": false\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {\n                  \"padding\": \"0 24px 10px\",\n                  \"borderBottom\": \"1px solid #CBEDFD\"\n                }\n              }\n            }\n          },\n          {\n            \"cellType\": \"container\",\n            \"component\": \"TemplateCardWithNavList\",\n            \"cellName\": \"projectDetails\",\n            \"cells\": [\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"projectDetails.name\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Project name\",\n                    \"name\": \"Project name\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"name\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"projectDetails.street\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Project street address\",\n                    \"name\": \"Project street address\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"street\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"projectDetails.city\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Project city\",\n                    \"name\": \"Project city\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"city\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"projectDetails.state\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Project state\",\n                    \"name\": \"Project state\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"state\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"projectDetails.zipCode\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Project zip code\",\n                    \"name\": \"Project zip code\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"zipCode\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              }\n            ],\n            \"meta\": {\n              \"componentConfig\": null,\n              \"isWrapperRequired\": true,\n              \"containerConfig\": {\n                \"title\": \"\",\n                \"labelTooltip\": null,\n                \"blockLabel\": \"Project(s)\",\n                \"navLabel\": \"name\",\n                \"blockId\": \"project_details\",\n                \"addNewLabel\": \"Add\",\n                \"uniqueIdentifier\": \"id\",\n                \"showDeleteModal\": true,\n                \"deleteModalMessage\": \"Are you sure you want to delete the {_inputLabel_} form the Project(s)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"projectDetails\"],\n                  \"defaultMap\": {\n                    \"name\": \"\",\n                    \"street\": \"\",\n                    \"city\": \"\",\n                    \"state\": \"\",\n                    \"zipCode\": \"\",\n                    \"description\": \"\"\n                  }\n                },\n                \"shadow\": false,\n                \"outerPadding\": \"none\"\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {\n                  \"padding\": \"0 24px\"\n                }\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": false,\n          \"containerConfig\": {\n            \"title\": null,\n            \"labelTooltip\": null,\n            \"outerPadding\": \"none\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {\n              \"padding\": \"24px 0\"\n            }\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"mka61effc-4a76-4471-85f5657-a154d018e300\",\n            \"cellName\": \"exposureDetails.eachOccurrenceLimitPrimary\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Each occurrence limit\",\n                \"name\": \"Each occurrence limit Primary\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.eachOccurrenceLimitPrimary\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"eachOccurancePrimary\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ea61effc-4a76-4471-85f9-a154d018e899\",\n            \"cellName\": \"exposureDetails.generalAggregateLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"General aggregate limit (other than Products/Completed operations)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.generalAggregateLimit\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"generalAggregateLimit\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ea61effc-4a76-4471-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.productsCompletedOperationsAggregateLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Products/Completed operations aggregate limit\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.productsCompletedOperationsAggregateLimit\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"productsCompletedOperationsAggregateLimit\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ea61effc-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.personalAdvertisingInjuryLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Personal and advertising injury limit\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.personalAdvertisingInjuryLimit\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"personalAndAdvertisingInjuryLimit\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ea61effd-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.damageToPremisesRented\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Damage to premises rented to you\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.damageToPremisesRented\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"damageToPermisesRentedToYou\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"da61effd-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.medicalPaymentsLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Medical payments limit\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.medicalPaymentsLimit\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"medicalPaymentLimit\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"da61effd-4a76-aa70-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.eachOccurrenceLimitExcess\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Each occurrence limit\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.eachOccurrenceLimitExcess\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"eachOccuranceExcess\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"da61effh-4a76-aa70-85fq-a154d018e899\",\n            \"cellName\": \"exposureDetails.otherAggregateLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Other aggregate limit\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.otherAggregateLimit\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"otherAggregateLimit\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"da61effh-4a76-aa70-85fq-a154d018e89vg\",\n            \"cellName\": \"exposureDetails.productsCompletedOperationsAggregate\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Products-Completed operations aggregate\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.productsCompletedOperationsAggregate\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"productsCompletedOperationsAggregate\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"pa61effh-4a76-aa70-85fq-a154d018e89vg\",\n            \"cellName\": \"exposureDetails.policyAggregateLimit\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Policy aggregate limit (auto)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.policyAggregateLimit\"\n                  ]\n                },\n                \"inputType\": \"currency\",\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ba61effd-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"deductibleDetails.retentionType\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Retention type\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"deductibleDetails.retentionType\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"retentionType\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ba61ecfv-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"deductibleDetails.deductibleAmount\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Deductible amount\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"deductibleDetails.deductibleAmount\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"deductibleAmount\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"ba61effv-4a76-aa71-85fq-a154d018e899\",\n            \"cellName\": \"deductibleDetails.sirAmount\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"SIR amount\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"deductibleDetails.sirAmount\"]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"SIR Amount\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"pa61evfh-4a76-aa70-85fq-a154d018e89vg\",\n            \"cellName\": \"exposureDetails.numberOfGeneralAggregateReinstatements\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Number of general aggregate reinstatements\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.numberOfGeneralAggregateReinstatements\"\n                  ]\n                },\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"generalAggregateReinstatements\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"pa61evfh-4a76-aa70-85fq-bvdfvhkjfdjkn\",\n            \"cellName\": \"exposureDetails.attachmentTotalLimitsInclGL\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Attachment (Total limits including GL)\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"exposureDetails.attachmentTotalLimitsInclGL\"\n                  ]\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": true,\n          \"containerConfig\": {\n            \"title\": \"Policy Limits Deductible SIR\",\n            \"labelTooltip\": null,\n            \"parentContainerId\": \"submission_information\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"cellType\": \"container\",\n            \"component\": \"TemplateCardWithGroupList\",\n            \"cellName\": \"rateDetails\",\n            \"cells\": [\n              {\n                \"id\": \"ea61effc-4a76-4471-85f9-a154d018e305\",\n                \"cellName\": \"rateDetails.exposureBasis\",\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseDropdownWithLabel\",\n                \"meta\": {\n                  \"containerConfig\": null,\n                  \"componentConfig\": {\n                    \"label\": \"Exposure basis\",\n                    \"placeholder\": \"Select exposure basis\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"rateDetails\",\n                        \"exposureBasis\"\n                      ]\n                    },\n                    \"dataType\": \"list\",\n                    \"keyToSave\": \"name\",\n                    \"options\": [],\n                    \"requireSearch\": true,\n                    \"ajaxSearch\": true,\n                    \"async\": true,\n                    \"asyncType\": \"rest\",\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                      \"method\": \"POST\",\n                      \"requestBody\": {\n                        \"metaDataFilters\": []\n                      },\n                      \"queryParams\": {\n                        \"entityType\": \"exposureBasis\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"paginationPlace\": \"query\",\n                        \"pageKey\": \"page\",\n                        \"sizeKey\": \"size\",\n                        \"defaultSize\": 20\n                      },\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchPlace\": \"query\",\n                        \"searchKey\": \"searchText\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"content\",\n                        \"labelMapper\": {\n                          \"id\": \"dataValue\",\n                          \"name\": \"dataValue\"\n                        },\n                        \"totalItemsKey\": \"totalElements\"\n                      }\n                    }\n                  },\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"rateDetails.rateByExposureBasis\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Rate by exposure basis\",\n                    \"name\": \"Rate by exposure basis\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"rateDetails\",\n                        \"rateByExposureBasis\"\n                      ]\n                    },\n                    \"inputType\": \"currency\",\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\"\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"rateDetails.exposureBasisDetailed\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Exposure basis detailed\",\n                    \"name\": \"Exposure basis detailed\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"rateDetails\",\n                        \"exposureBasisDetailed\"\n                      ]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\"\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"rateDetails.exposureAmountByExposureBasis\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Exposure amount by exposure basis\",\n                    \"name\": \"Exposure amount by exposure basis\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"rateDetails\",\n                        \"exposureAmountByExposureBasis\"\n                      ]\n                    },\n                    \"inputType\": \"currency\",\n                    \"placeholder\": \"Enter here\",\n                    \"dataType\": \"list\"\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              }\n            ],\n            \"meta\": {\n              \"componentConfig\": null,\n              \"isWrapperRequired\": true,\n              \"containerConfig\": {\n                \"title\": null,\n                \"blockId\": \"policy-ratings\",\n                \"labelTooltip\": null,\n                \"uniqueIdentifier\": \"id\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"rateDetails\"],\n                  \"defaultMap\": {\n                    \"exposureAmountByExposureBasis\": \"\",\n                    \"exposureBasis\": \"\",\n                    \"exposureBasisDetailed\": \"\",\n                    \"rateByExposureBasis\": \"\"\n                  }\n                },\n                \"shadow\": false,\n                \"outerPadding\": \"none\"\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {\n                  \"borderBottom\": \"1px solid #CBEDFD\"\n                }\n              }\n            }\n          },\n          {\n            \"cellType\": \"container\",\n            \"cells\": [\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"premiumDetails.isoClassificationCode\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"ISO Classification Code\",\n                    \"name\": \"ISO Classification Code\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"premiumDetails.isoClassificationCode\"\n                      ]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"premiumDetails.totalWrittenPremium\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Total written premium\",\n                    \"name\": \"Total written premium\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"premiumDetails.totalWrittenPremium\"\n                      ]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"inputType\": \"currency\",\n                    \"gridSpaceFromRight\": 8,\n                    \"decimal\": 0\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"premiumDetails.triaPremium\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"TRIA premium\",\n                    \"name\": \"TRIA premium\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"premiumDetails.triaPremium\"\n                      ]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"inputType\": \"currency\",\n                    \"gridSpaceFromRight\": 8,\n                    \"conditionalValidations\": {\n                      \"premiumDetails.isTriaPremiumEnabled\": {\n                        \"validations\": {\n                          \"required\": false\n                        },\n                        \"value\": false\n                      }\n                    },\n                    \"conditionalDisabled\": {\n                      \"premiumDetails.isTriaPremiumEnabled\": false\n                    },\n                    \"isEnableToggleOn\": \"premiumDetails.isTriaPremiumEnabled\"\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseInputWithLabel\",\n                \"cellName\": \"premiumDetails.totalAutos\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Total number of autos\",\n                    \"name\": \"Total number of autos\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"premiumDetails.totalAutos\"]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"inputType\": \"number\",\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseDropdownWithLabel\",\n                \"cellName\": \"premiumDetails.audit\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Audit\",\n                    \"name\": \"Audit\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"premiumDetails.audit\"]\n                    },\n                    \"placeholder\": \"Select Audit\",\n                    \"options\": [\n                      {\n                        \"id\": \"Yes\",\n                        \"name\": \"Yes\"\n                      },\n                      {\n                        \"id\": \"No\",\n                        \"name\": \"No\"\n                      }\n                    ],\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"cellName\": \"carrier\",\n                \"component\": \"BaseDropdownWithLabel\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Carrier\",\n                    \"name\": \"Carrier\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"carrier\"]\n                    },\n                    \"placeholder\": \"Select Carrier\",\n                    \"options\": [],\n                    \"requireSearch\": true,\n                    \"ajaxSearch\": true,\n                    \"async\": true,\n                    \"asyncType\": \"rest\",\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                      \"method\": \"POST\",\n                      \"requestBody\": {\n                        \"metaDataFilters\": []\n                      },\n                      \"queryParams\": {\n                        \"entityType\": \"Carrier\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"paginationPlace\": \"query\",\n                        \"pageKey\": \"page\",\n                        \"sizeKey\": \"size\",\n                        \"defaultSize\": 20\n                      },\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchPlace\": \"query\",\n                        \"searchKey\": \"searchText\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"content\",\n                        \"labelMapper\": {\n                          \"id\": \"dataValue\",\n                          \"name\": \"dataValue\"\n                        },\n                        \"totalItemsKey\": \"totalElements\"\n                      }\n                    },\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"cellName\": \"premiumDetails.treaty\",\n                \"component\": \"BaseDropdownWithLabel\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Treaty\",\n                    \"name\": \"Treaty\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"premiumDetails.treaty\"]\n                    },\n                    \"requireSearch\": true,\n                    \"ajaxSearch\": true,\n                    \"async\": true,\n                    \"asyncType\": \"rest\",\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                      \"method\": \"POST\",\n                      \"requestBody\": {\n                        \"metaDataFilters\": []\n                      },\n                      \"queryParams\": {\n                        \"entityType\": \"Treaty\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"paginationPlace\": \"query\",\n                        \"pageKey\": \"page\",\n                        \"sizeKey\": \"size\",\n                        \"defaultSize\": 20\n                      },\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchPlace\": \"query\",\n                        \"searchKey\": \"searchText\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"content\",\n                        \"labelMapper\": {\n                          \"id\": \"dataValue\",\n                          \"name\": \"dataValue\"\n                        },\n                        \"totalItemsKey\": \"totalElements\"\n                      }\n                    },\n                    \"gridSpaceFromRight\": 8\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              }\n            ],\n            \"component\": null,\n            \"meta\": {\n              \"componentConfig\": null,\n              \"containerConfig\": null,\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"cellType\": \"container\",\n            \"component\": \"TemplateCardWithGroupList\",\n            \"cellName\": \"premiumDetails.feeDetails\",\n            \"cells\": [\n              {\n                \"id\": \"ea61effc-4a76-4471-85f9-a154d018e305\",\n                \"cellName\": \"premiumDetails.feeDetails.feeDescription\",\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"component\": \"BaseDropdownWithLabel\",\n                \"meta\": {\n                  \"containerConfig\": null,\n                  \"componentConfig\": {\n                    \"label\": \"Fee description\",\n                    \"placeholder\": \"Select\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"premiumDetails.feeDetails\",\n                        \"feeDescription\"\n                      ]\n                    },\n                    \"dataType\": \"list\",\n                    \"keyToSave\": \"name\",\n                    \"options\": [],\n                    \"requireSearch\": true,\n                    \"ajaxSearch\": true,\n                    \"async\": true,\n                    \"asyncType\": \"rest\",\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                      \"method\": \"POST\",\n                      \"requestBody\": {\n                        \"metaDataFilters\": []\n                      },\n                      \"queryParams\": {\n                        \"entityType\": \"feeDescription\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"paginationPlace\": \"query\",\n                        \"pageKey\": \"page\",\n                        \"sizeKey\": \"size\",\n                        \"defaultSize\": 20\n                      },\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchPlace\": \"query\",\n                        \"searchKey\": \"searchText\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"content\",\n                        \"labelMapper\": {\n                          \"id\": \"dataValue\",\n                          \"name\": \"dataValue\"\n                        },\n                        \"totalItemsKey\": \"totalElements\"\n                      }\n                    }\n                  },\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              },\n              {\n                \"cellType\": \"component\",\n                \"cells\": null,\n                \"cellName\": \"premiumDetails.feeDetails.feeAmount\",\n                \"component\": \"BaseInputWithLabel\",\n                \"meta\": {\n                  \"componentConfig\": {\n                    \"label\": \"Fee amounts\",\n                    \"name\": \"Fee amounts\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"premiumDetails.feeDetails\",\n                        \"feeAmount\"\n                      ]\n                    },\n                    \"placeholder\": \"Enter here\",\n                    \"inputType\": \"currency\",\n                    \"dataType\": \"list\",\n                    \"decimal\": 0\n                  },\n                  \"containerConfig\": null,\n                  \"cellConfig\": {\n                    \"visibleCondition\": null,\n                    \"col\": 12,\n                    \"style\": {}\n                  }\n                }\n              }\n            ],\n            \"meta\": {\n              \"componentConfig\": null,\n              \"isWrapperRequired\": true,\n              \"containerConfig\": {\n                \"title\": null,\n                \"blockId\": \"fee-details\",\n                \"labelTooltip\": null,\n                \"uniqueIdentifier\": \"id\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"premiumDetails.feeDetails\"],\n                  \"defaultMap\": {\n                    \"feeAmount\": \"\",\n                    \"feeDescription\": \"\"\n                  }\n                },\n                \"shadow\": false,\n                \"outerPadding\": \"none\"\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": true,\n          \"containerConfig\": {\n            \"title\": \"Policy Ratings\",\n            \"shadow\": true,\n            \"outerPadding\": \"none\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {\n              \"padding\": \"24px 24px 0\"\n            }\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"9b1f25d0-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.commissionDetails.percentage\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Commission %\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.commissionDetails.percentage\"\n                  ]\n                },\n                \"validations\": {\n                  \"max_value\": 100\n                },\n                \"inputType\": \"percentage\",\n                \"gridSpaceFromRight\": 8,\n                \"decimal\": 2\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"9b1f25d0-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.commissionDetails.amount\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Commission $\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.commissionDetails.amount\"\n                  ]\n                },\n                \"disabled\": true,\n                \"inputType\": \"currency\",\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": true,\n          \"containerConfig\": {\n            \"title\": \"Broker commission\",\n            \"labelTooltip\": null\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"9b1f77878-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.premiumReturnType\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseDropdownWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Premium return type\",\n                \"placeholder\": \"Select premium return type\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.premiumReturnType\"\n                  ]\n                },\n                \"options\": [\n                  {\n                    \"id\": \"Flat Rate\",\n                    \"label\": \"Flat rate\",\n                    \"name\": \"Flat Rate\"\n                  },\n                  {\n                    \"id\": \"Short Rate\",\n                    \"label\": \"Short rate\",\n                    \"name\": \"Short Rate\"\n                  },\n                  {\n                    \"id\": \"Pro Rata\",\n                    \"label\": \"Pro rata\",\n                    \"name\": \"Pro Rata\"\n                  }\n                ],\n                \"validations\": {\n                  \"required\": true\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"9b1f785d0-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.totalTechnicalPremium\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Total technical premium\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.totalTechnicalPremium\"\n                  ]\n                },\n                \"inputType\": \"currency\",\n                \"gridSpaceFromRight\": 8,\n                \"decimal\": 0\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"9b1f26780-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.soldToTechnicalPercentage\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Sold to technical %\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.soldToTechnicalPercentage\"\n                  ]\n                },\n                \"disabled\": true,\n                \"validations\": {\n                  \"max_value\": 100\n                },\n                \"inputType\": \"number\",\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          },\n          {\n            \"id\": \"9b1f23443564-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"premiumDetails.underWriterDebitCreditFactor\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"Underwriter debit/credit factor\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"premiumDetails.underWriterDebitCreditFactor\"\n                  ]\n                },\n                \"inputType\": \"number\",\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": true,\n          \"containerConfig\": {\n            \"title\": \"Underwriting rating\",\n            \"labelTooltip\": null\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"9b1f785d0-583a-4dfb-a4bf-1a79b80f2d41\",\n            \"cellName\": \"nyftzClass\",\n            \"cellType\": \"component\",\n            \"cells\": null,\n            \"component\": \"BaseInputWithLabel\",\n            \"meta\": {\n              \"containerConfig\": null,\n              \"componentConfig\": {\n                \"label\": \"NYFTZ Class\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"nyftzClass\"]\n                },\n                \"disabled\": true,\n                \"gridSpaceFromRight\": 8\n              },\n              \"cellConfig\": {\n                \"visibleCondition\": null,\n                \"col\": 12,\n                \"style\": {}\n              }\n            }\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": true,\n          \"containerConfig\": {\n            \"title\": \"NYFTZ\",\n            \"labelTooltip\": null\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      },\n      {\n        \"cellType\": \"container\",\n        \"component\": \"TemplateCard\",\n        \"cells\": [\n          {\n            \"id\": \"123123-23232-4dfb-a4bf-1423423434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"name\": \"Filing done by\",\n                \"label\": \"Filing done by\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"surplusLineInfo.filingDoneBy\"]\n                },\n                \"options\": [\n                  {\n                    \"label\": \"Inside broker\",\n                    \"value\": \"Inside broker\"\n                  },\n                  {\n                    \"label\": \"Outside broker\",\n                    \"value\": \"Outside broker\"\n                  }\n                ],\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.filingDoneBy\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseRadioWithLabel\"\n          },\n          {\n            \"id\": \"123123-3123123-4dfb-a4bf-1423423434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Broker name\",\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"surplusLineInfo.brokerName\"],\n                  \"otherNameInSourcedSystemParams\": [\n                    {\n                      \"key\": \"surplusLineInfo.surplusBrokerageName\",\n                      \"valueKeyToSave\": \"brokerageName\"\n                    },\n                    {\n                      \"key\": \"surplusLineInfo.mailingAddress.address\",\n                      \"valueKeyToSave\": \"addressLine1\"\n                    },\n                    {\n                      \"key\": \"surplusLineInfo.mailingAddress.city\",\n                      \"valueKeyToSave\": \"city\"\n                    },\n                    {\n                      \"key\": \"surplusLineInfo.mailingAddress.state\",\n                      \"valueKeyToSave\": \"state\"\n                    },\n                    {\n                      \"key\": \"surplusLineInfo.mailingAddress.zipCode\",\n                      \"valueKeyToSave\": \"zipCode\"\n                    }\n                  ]\n                },\n                \"async\": true,\n                \"asyncType\": \"gql\",\n                \"keyToSave\": \"name\",\n                \"labelKey\": \"name\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/graphql\",\n                  \"method\": \"POST\",\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchKey\": \"input.searchName\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"pageKey\": \"input.page\",\n                    \"sizeKey\": \"input.size\",\n                    \"defaultSize\": 10\n                  },\n                  \"queryParams\": {},\n                  \"requestBody\": {\n                    \"query\": \" query SearchBroker($input: SearchBrokerInput!) { searchBroker(input: $input) { totalElements pageableInfo { page size } items { brokerageId brokerageName officeId officeCity officeName broker { id firstName lastName emailAddress team  address { addressLine1 city zipcode state } } } } }\",\n                    \"variables\": {\n                      \"input\": {\n                        \"page\": 0,\n                        \"size\": 10\n                      }\n                    }\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"data.searchBroker.items\",\n                    \"labelMapper\": {\n                      \"id\": \"broker.id\",\n                      \"label\": [\"broker.firstName\", \"broker.lastName\", \"brokerageName\", \"officeName\"],\n                      \"name\": [\"broker.firstName\", \"broker.lastName\"],\n                      \"senderEmail\": \"broker.emailAddress\",\n                      \"addressLine1\": \"broker.address.addressLine1\",\n                      \"city\": \"broker.address.city\",\n                      \"state\": \"broker.address.state\",\n                      \"zipCode\": \"broker.address.zipcode\",\n                      \"brokerageName\": \"brokerageName\"\n                    },\n                    \"totalItemsKey\": \"data.searchBroker.totalElements\",\n                    \"concatString\": [\" \", \" - \", \", \"]\n                  }\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"dropdownPlaceholder\": \"Select Broker\",\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.brokerName\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"2121344557g3123-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines brokerage name\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.surplusBrokerageName\"\n                  ]\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.surplusBrokerageName\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"123123-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines address\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.mailingAddress.address\"\n                  ]\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.mailingAddress.address\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines state\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.mailingAddress.state\"\n                  ]\n                },\n                \"placeholder\": \"Select state\",\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"requestBody\": {\n                    \"metaDataFilters\": [\n                      {\n                        \"key\": \"Country\",\n                        \"value\": \"US\",\n                        \"mandatory\": true\n                      }\n                    ]\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"State\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.mailingAddress.state\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"31assd-323-4dfb-a4bf-123434\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines city\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.mailingAddress.city\"\n                  ]\n                },\n                \"placeholder\": \"Select city\",\n                \"options\": [],\n                \"requireSearch\": true,\n                \"ajaxSearch\": true,\n                \"async\": true,\n                \"asyncType\": \"rest\",\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"mapKeyFromData\": [\n                    {\n                      \"mapDataFrom\": \"surplusLineInfo.mailingAddress.state\",\n                      \"mapType\": \"arrayItem\",\n                      \"mapDataTo\": \"metaDataFilters|1|value\",\n                      \"mapDataIn\": \"body\"\n                    }\n                  ],\n                  \"requestBody\": {\n                    \"metaDataFilters\": [\n                      {\n                        \"key\": \"Country\",\n                        \"value\": \"US\",\n                        \"mandatory\": true\n                      },\n                      {\n                        \"key\": \"State\",\n                        \"value\": \"\",\n                        \"mandatory\": true\n                      }\n                    ]\n                  },\n                  \"queryParams\": {\n                    \"entityType\": \"City\"\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"paginationPlace\": \"query\",\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 20\n                  },\n                  \"search\": {\n                    \"enabled\": true,\n                    \"searchPlace\": \"query\",\n                    \"searchKey\": \"searchText\"\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": \"dataValue\"\n                    },\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.mailingAddress.city\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf-21321\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines zipcode\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.mailingAddress.zipCode\"\n                  ]\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.mailingAddress.zipCode\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf-21321\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Surplus lines licence number\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"surplusLineInfo.surplusLicenceNumber\"\n                  ]\n                },\n                \"conditionalDisabled\": {\n                  \"surplusLineInfo.filingDoneBy\": \"Inside broker\"\n                },\n                \"gridSpaceFromRight\": 8\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.surplusLicenceNumber\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          }\n        ],\n        \"meta\": {\n          \"componentConfig\": null,\n          \"isWrapperRequired\": false,\n          \"containerConfig\": {\n            \"title\": \"Surplus Lines Information\",\n            \"labelTooltip\": null,\n            \"parentContainerId\": \"surplus_information\"\n          },\n          \"cellConfig\": {\n            \"visibleCondition\": null,\n            \"col\": 12,\n            \"style\": {}\n          }\n        }\n      }\n    ]\n  }\n]", "validation": "{\"type\": { \"enum\": [\"CONSTRUCTION\"], \"type\": \"string\", \"isRequired\": true },\n    \"state\": { \"type\": \"string\", \"isRequired\": true },\n    \"status\": { \"type\": \"string\", \"isRequired\": true },\n    \"carrier\": { \"type\": \"string\", \"isRequired\": true },\n    \"clientId\": { \"type\": \"string\", \"isRequired\": true },\n    \"riskState\": { \"type\": \"string\", \"isRequired\": false },\n    \"assigneeId\": { \"type\": \"string\", \"isRequired\": false },\n    \"brokerInfo\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"id\": { \"type\": \"string\", \"isRequired\": true },\n        \"name\": { \"type\": \"string\", \"isRequired\": true },\n        \"team\": { \"type\": \"string\", \"isRequired\": true },\n        \"senderName\": { \"type\": \"string\", \"isRequired\": true },\n        \"senderEmail\": {\n          \"type\": \"string\",\n          \"format\": \"email\",\n          \"isRequired\": true\n        },\n        \"mailingAddress\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": { \"type\": \"string\", \"isRequired\": true },\n            \"state\": { \"type\": \"string\", \"isRequired\": true },\n            \"street\": { \"type\": \"string\", \"isRequired\": true },\n            \"zipCode\": { \"type\": \"string\", \"isRequired\": true }\n          }\n        }\n      }\n    },\n    \"nyftzClass\": { \"type\": \"string\", \"isRequired\": false },\n    \"insuredInfo\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"id\": { \"type\": \"string\", \"isRequired\": true },\n        \"dba\": { \"type\": \"string\", \"isRequired\": false },\n        \"owner\": { \"type\": \"string\", \"isRequired\": false },\n        \"firstName\": { \"type\": \"string\", \"isRequired\": true },\n        \"mailingAddress\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"city\": { \"type\": \"string\", \"isRequired\": true },\n            \"state\": { \"type\": \"string\", \"isRequired\": true },\n            \"street1\": { \"type\": \"string\", \"isRequired\": true },\n            \"street2\": { \"type\": \"string\", \"isRequired\": false },\n            \"zipCode\": { \"type\": \"string\", \"isRequired\": true }\n          }\n        },\n        \"generalContractor\": { \"type\": \"string\", \"isRequired\": false },\n        \"otherNamedInsureds\": {\n          \"type\": \"array\",\n          \"items\": { \"type\": \"string\" },\n          \"isRequired\": true\n        }\n      }\n    },\n    \"productName\": { \"type\": \"string\", \"isRequired\": true },\n    \"productType\": { \"type\": \"string\", \"isRequired\": true },\n    \"rateDetails\": {\n      \"type\": \"array\",\n      \"items\": {\n        \"type\": \"object\",\n        \"properties\": {\n          \"exposureBasis\": { \"type\": \"string\", \"isRequired\": false },\n          \"rateByExposureBasis\": { \"type\": \"string\", \"isRequired\": false },\n          \"exposureBasisDetailed\": { \"type\": \"string\", \"isRequired\": false },\n          \"exposureAmountByExposureBasis\": {\n            \"type\": \"string\",\n            \"isRequired\": false\n          }\n        }\n      }\n    },\n    \"underwriterName\": { \"type\": \"string\", \"isRequired\": true },\n    \"submissionId\": { \"type\": \"string\", \"isRequired\": true },\n    \"definedStatus\": { \"type\": \"string\", \"isRequired\": false },\n    \"policyCurrency\": { \"type\": \"string\", \"isRequired\": true },\n    \"premiumDetails\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"audit\": { \"type\": \"string\", \"isRequired\": false },\n        \"totalAutos\": { \"type\": \"string\", \"isRequired\": false },\n        \"triaPremium\": { \"type\": \"string\", \"isRequired\": true },\n        \"feeDetails\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"feeDescription\": {\n              \"type\": \"array\",\n              \"items\": {\n                \"type\": \"string\"\n              },\n              \"isRequired\": true\n            },\n            \"feeAmounts\": {\n              \"type\": \"array\",\n              \"items\": { \"type\": \"string\" },\n              \"isRequired\": true\n            },\n            \"feeAmount\": {\n              \"type\": \"array\",\n              \"items\": { \"type\": \"string\" },\n              \"isRequired\": true\n            }\n          }\n        },\n        \"premiumDueDate\": {\n          \"type\": \"string\",\n          \"format\": \"LocalDate\",\n          \"isRequired\": false\n        },\n        \"commissionDetails\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"type\": { \"type\": \"string\", \"isRequired\": false },\n            \"amount\": { \"type\": \"string\", \"isRequired\": true },\n            \"percentage\": { \"type\": \"string\", \"isRequired\": true }\n          }\n        },\n        \"totalWrittenPremium\": { \"type\": \"string\", \"isRequired\": true },\n        \"isoClassificationCode\": { \"type\": \"string\", \"isRequired\": false },\n        \"totalTechnicalPremium\": { \"type\": \"string\", \"isRequired\": false },\n        \"soldToTechnicalPercentage\": { \"type\": \"string\", \"isRequired\": false },\n        \"underWriterDebitCreditFactor\": {\n          \"type\": \"string\",\n          \"isRequired\": false\n        }\n      }\n    },\n    \"exposureDetails\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"otherAggregateLimit\": { \"type\": \"string\", \"isRequired\": false },\n        \"medicalPaymentsLimit\": { \"type\": \"string\", \"isRequired\": false },\n        \"policyAggregateLimit\": { \"type\": \"string\", \"isRequired\": false },\n        \"generalAggregateLimit\": { \"type\": \"string\", \"isRequired\": false },\n        \"damageToPremisesRented\": { \"type\": \"string\", \"isRequired\": false },\n        \"eachOccurrenceLimitExcess\": { \"type\": \"string\", \"isRequired\": false },\n        \"eachOccurrenceLimitPrimary\": { \"type\": \"string\", \"isRequired\": false },\n        \"personalAdvertisingInjuryLimit\": {\n          \"type\": \"string\",\n          \"isRequired\": false\n        },\n        \"productsCompletedOperationsAggregate\": {\n          \"type\": \"string\",\n          \"isRequired\": false\n        },\n        \"numberOfGeneralAggregateReinstatements\": {\n          \"type\": \"string\",\n          \"isRequired\": false\n        },\n        \"productsCompletedOperationsAggregateLimit\": {\n          \"type\": \"string\",\n          \"isRequired\": false\n        }\n      }\n    },\n    \"transactionDate\": {\n      \"type\": \"string\",\n      \"format\": \"LocalDate\",\n      \"isRequired\": false\n    },\n    \"transactionType\": {\n      \"enum\": [\"New Business\", \"Renewal\"],\n      \"type\": \"string\",\n      \"isRequired\": true\n    },\n    \"scheduleOfValues\": { \"type\": \"string\", \"isRequired\": true },\n    \"submissionNumber\": { \"type\": \"string\", \"isRequired\": true },\n    \"underwriterEmail\": {\n      \"type\": \"string\",\n      \"format\": \"email\",\n      \"isRequired\": true\n    },\n    \"deductibleDetails\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"sirAmount\": { \"type\": \"string\", \"isRequired\": false },\n        \"retentionType\": { \"type\": \"string\", \"isRequired\": false },\n        \"deductibleAmount\": { \"type\": \"string\", \"isRequired\": false }\n      }\n    },\n    \"priorPolicyNumber\": { \"type\": \"string\", \"isRequired\": false },\n    \"policyInceptionDate\": {\n      \"type\": \"string\",\n      \"format\": \"LocalDate\",\n      \"isRequired\": true\n    },\n    \"policyExpirationDate\": {\n      \"type\": \"string\",\n      \"format\": \"LocalDate\",\n      \"isRequired\": true\n    },\n    \"descriptionOfOperations\": { \"type\": \"string\", \"isRequired\": true }\n  }", "hiddenCells": ["definedStatus", "priorPolicyNumber", "exposureDetails.policyAggregateLimit", "nyftzClass", "scheduleOfValues", "exposureDetails.eachOccurrenceLimitExcess", "exposureDetails.otherAggregateLimit", "exposureDetails.productsCompletedOperationsAggregate"]}