name: QA Automation Tests

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 6 AM UTC
    - cron: '0 6 * * *'
  workflow_dispatch:
    inputs:
      test_suite:
        description: 'Test Suite to Run'
        required: true
        default: 'all'
        type: choice
        options:
        - all
        - smoke
        - regression
        - calculatePremium
        - policySearch
        - detailedValidation
      environment:
        description: 'Environment'
        required: true
        default: 'LOGISTICS-DEV'
        type: choice
        options:
        - LOGISTICS-DEV
        - LOGISTICS-TEST
        - LOGISTICS-STAGING

env:
  MAVEN_OPTS: -Xmx1024m
  JAVA_VERSION: '11'

jobs:
  qa-automation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'
        
    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2
        
    - name: Set environment variables
      run: |
        echo "TEST_ENVIRONMENT=${{ github.event.inputs.environment || 'LOGISTICS-DEV' }}" >> $GITHUB_ENV
        echo "TEST_SUITE=${{ github.event.inputs.test_suite || 'all' }}" >> $GITHUB_ENV
        echo "BUILD_NUMBER=${{ github.run_number }}" >> $GITHUB_ENV
        echo "BRANCH_NAME=${{ github.ref_name }}" >> $GITHUB_ENV
        
    - name: Run Maven Tests
      run: |
        case "${{ env.TEST_SUITE }}" in
          "all")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dcucumber.filter.tags="@Logistics"
            ;;
          "smoke")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dcucumber.filter.tags="@smoke"
            ;;
          "regression")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dcucumber.filter.tags="@regression"
            ;;
          "calculatePremium")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dtest=CalculatePremiumTestRunner
            ;;
          "policySearch")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dtest=PolicySearchTestRunner
            ;;
          "detailedValidation")
            mvn clean test -Dtier=${{ env.TEST_ENVIRONMENT }} -Dtest=PolicyDetailedValidationTestRunner
            ;;
        esac
      continue-on-error: true
      
    - name: Generate Test Report
      if: always()
      run: |
        # Create reports directory if it doesn't exist
        mkdir -p target/reports
        
        # Generate HTML report from Cucumber JSON
        if [ -f target/cucumber.json ]; then
          # Install Node.js for report generation
          curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
          sudo apt-get install -y nodejs
          
          # Install cucumber-html-reporter
          npm install -g cucumber-html-reporter
          
          # Generate HTML report
          node -e "
          const reporter = require('cucumber-html-reporter');
          const options = {
            theme: 'bootstrap',
            jsonFile: 'target/cucumber.json',
            output: 'target/reports/cucumber-report.html',
            reportSuiteAsScenarios: true,
            scenarioTimestamp: true,
            launchReport: false,
            metadata: {
              'App Version': '1.0.0',
              'Test Environment': '${{ env.TEST_ENVIRONMENT }}',
              'Browser': 'API Tests',
              'Platform': 'Ubuntu',
              'Parallel': 'Scenarios',
              'Executed': 'GitHub Actions'
            }
          };
          reporter.generate(options);
          "
        fi
        
    - name: Parse Test Results
      if: always()
      id: test-results
      run: |
        # Parse Cucumber JSON results
        if [ -f target/cucumber.json ]; then
          python3 << 'EOF'
        import json
        import os
        
        try:
            with open('target/cucumber.json', 'r') as f:
                data = json.load(f)
            
            total_scenarios = 0
            passed_scenarios = 0
            failed_scenarios = 0
            skipped_scenarios = 0
            total_features = len(data)
            passed_features = 0
            
            for feature in data:
                feature_passed = True
                for element in feature.get('elements', []):
                    if element.get('type') == 'scenario':
                        total_scenarios += 1
                        scenario_status = 'passed'
                        
                        for step in element.get('steps', []):
                            step_result = step.get('result', {})
                            if step_result.get('status') == 'failed':
                                scenario_status = 'failed'
                                feature_passed = False
                                break
                            elif step_result.get('status') == 'skipped':
                                if scenario_status != 'failed':
                                    scenario_status = 'skipped'
                        
                        if scenario_status == 'passed':
                            passed_scenarios += 1
                        elif scenario_status == 'failed':
                            failed_scenarios += 1
                        else:
                            skipped_scenarios += 1
                
                if feature_passed and total_scenarios > 0:
                    passed_features += 1
            
            pass_percentage = round((passed_scenarios / total_scenarios * 100), 2) if total_scenarios > 0 else 0
            feature_pass_percentage = round((passed_features / total_features * 100), 2) if total_features > 0 else 0
            
            # Write to GitHub output
            with open(os.environ['GITHUB_OUTPUT'], 'a') as f:
                f.write(f"total_scenarios={total_scenarios}\n")
                f.write(f"passed_scenarios={passed_scenarios}\n")
                f.write(f"failed_scenarios={failed_scenarios}\n")
                f.write(f"skipped_scenarios={skipped_scenarios}\n")
                f.write(f"total_features={total_features}\n")
                f.write(f"passed_features={passed_features}\n")
                f.write(f"pass_percentage={pass_percentage}\n")
                f.write(f"feature_pass_percentage={feature_pass_percentage}\n")
                
            print(f"Total Scenarios: {total_scenarios}")
            print(f"Passed: {passed_scenarios}")
            print(f"Failed: {failed_scenarios}")
            print(f"Pass Rate: {pass_percentage}%")
            
        except Exception as e:
            print(f"Error parsing results: {e}")
            # Set default values
            with open(os.environ['GITHUB_OUTPUT'], 'a') as f:
                f.write("total_scenarios=0\n")
                f.write("passed_scenarios=0\n")
                f.write("failed_scenarios=0\n")
                f.write("skipped_scenarios=0\n")
                f.write("total_features=0\n")
                f.write("passed_features=0\n")
                f.write("pass_percentage=0\n")
                f.write("feature_pass_percentage=0\n")
        EOF
        else
          echo "No cucumber.json found, setting default values"
          echo "total_scenarios=0" >> $GITHUB_OUTPUT
          echo "passed_scenarios=0" >> $GITHUB_OUTPUT
          echo "failed_scenarios=0" >> $GITHUB_OUTPUT
          echo "skipped_scenarios=0" >> $GITHUB_OUTPUT
          echo "total_features=0" >> $GITHUB_OUTPUT
          echo "passed_features=0" >> $GITHUB_OUTPUT
          echo "pass_percentage=0" >> $GITHUB_OUTPUT
          echo "feature_pass_percentage=0" >> $GITHUB_OUTPUT
        fi
        
    - name: Upload Test Reports
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: test-reports-${{ github.run_number }}
        path: |
          target/reports/
          target/cucumber.json
          target/cucumber-html-report.html
          target/surefire-reports/
        retention-days: 30
        
    - name: Deploy Report to GitHub Pages
      if: always() && github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: target/reports
        destination_dir: reports/${{ github.run_number }}
        
    - name: Send Slack Notification
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        custom_payload: |
          {
            "text": "QA Automation Test Results",
            "attachments": [
              {
                "color": "${{ steps.test-results.outputs.pass_percentage >= 80 && 'good' || steps.test-results.outputs.pass_percentage >= 60 && 'warning' || 'danger' }}",
                "blocks": [
                  {
                    "type": "header",
                    "text": {
                      "type": "plain_text",
                      "text": "🧪 QA Automation Results - ${{ env.TEST_SUITE }} Suite"
                    }
                  },
                  {
                    "type": "section",
                    "fields": [
                      {
                        "type": "mrkdwn",
                        "text": "*Environment:* ${{ env.TEST_ENVIRONMENT }}"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*Branch:* ${{ env.BRANCH_NAME }}"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*Build:* #${{ env.BUILD_NUMBER }}"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*Trigger:* ${{ github.event_name }}"
                      }
                    ]
                  },
                  {
                    "type": "section",
                    "text": {
                      "type": "mrkdwn",
                      "text": "*📊 Test Summary*"
                    }
                  },
                  {
                    "type": "section",
                    "fields": [
                      {
                        "type": "mrkdwn",
                        "text": "*Features:* ${{ steps.test-results.outputs.passed_features }}/${{ steps.test-results.outputs.total_features }} (${{ steps.test-results.outputs.feature_pass_percentage }}%)"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*Scenarios:* ${{ steps.test-results.outputs.passed_scenarios }}/${{ steps.test-results.outputs.total_scenarios }} (${{ steps.test-results.outputs.pass_percentage }}%)"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*✅ Passed:* ${{ steps.test-results.outputs.passed_scenarios }}"
                      },
                      {
                        "type": "mrkdwn",
                        "text": "*❌ Failed:* ${{ steps.test-results.outputs.failed_scenarios }}"
                      }
                    ]
                  },
                  {
                    "type": "actions",
                    "elements": [
                      {
                        "type": "button",
                        "text": {
                          "type": "plain_text",
                          "text": "📋 View Full Report"
                        },
                        "url": "https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/reports/${{ github.run_number }}/cucumber-report.html"
                      },
                      {
                        "type": "button",
                        "text": {
                          "type": "plain_text",
                          "text": "🔗 GitHub Actions"
                        },
                        "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                      }
                    ]
                  }
                ]
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
