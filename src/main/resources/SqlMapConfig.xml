<?xml version = "1.0" encoding = "UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <typeAliases>
        <typeAlias alias = "EmailEvent" type = "dbmapper.EmailEvent"/>
        <typeAlias alias = "SubmissionInfo"  type = "dbmapper.SubmissionInfo"/>

    </typeAliases>

    <environments default = "QA">
        <environment id = "QA">
            <transactionManager type = "JDBC"/>
            <dataSource type ="POOLED">
                <property name = "driver" value = "org.postgresql.Driver"/>
                <property name = "url" value = "******************************************************************************************************"/>
                <property name = "username" value = "${username}"/>
                <property name = "password" value = "${password}"/>
            </dataSource>
        </environment>

        <environment id = "STAGE">
            <transactionManager type = "JDBC"/>
            <dataSource type ="POOLED">
                <property name = "driver" value = "com.mysql.cj.jdbc.Driver"/>
                <property name = "url" value = "*********************************************************************************************************"/>
                <property name = "username" value = "${username}"/>
                <property name = "password" value = "${password}"/>
            </dataSource>
        </environment>

        <environment id = "DEV">
            <transactionManager type = "JDBC"/>
            <dataSource type ="POOLED">
                <property name = "driver" value = "com.mysql.cj.jdbc.Driver"/>
                <property name = "url" value = "*******************************************************************************************************"/>
                <property name = "username" value = "${username}"/>
                <property name = "password" value = "${password}"/>
            </dataSource>
        </environment>
    </environments>


    <mappers>
        <mapper resource = "EmailEvent.xml"/>
        <mapper resource = "SubmissionInfo.xml"/>

    </mappers>

</configuration>


