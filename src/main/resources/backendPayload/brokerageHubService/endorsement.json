{"transactionDescription": "Monetray", "endorsementType": "MONETARY", "endorsementNumber": "1", "endorsementSequence": "1", "endorsementEffectiveDate": "2024-06-16", "endorsementStatus": "DRAFT", "updatedPolicyInfo": {"_id": {"timestamp": 1718522284, "date": "2024-06-16T07:18:04.000+00:00"}, "submissionId": "704f0eea-a304-47f9-b4c5-c7735b81f2bd", "submissionNumber": "95", "status": "IN_FORCE", "state": "IN_FORCE", "assigneeId": "6aa760cc-4cec-464d-b2dc-8d11a97f1cac", "comment": null, "sourceId": null, "referenceId": null, "fleetSize": 0, "type": "CONSTRUCTION", "productName": "Excess Liability - Annual Construction", "receivedDate": "2024-06-07T12:02:38.273426Z", "updatedAt": "2024-06-16T07:23:08.698+00:00", "blockedBySubmissionId": null, "isBlocked": false, "transactionType": "New Business", "productType": "XANN", "policyStatus": "Submitted", "definedStatus": "Active", "carrier": "Texas Insurance Company", "policyInceptionDate": "2023-01-06", "policyExpirationDate": "2024-01-06", "underwriterName": "backend auto", "underwriterEmail": "<EMAIL>", "transactionDate": "2024-06-07", "priorPolicyNumber": "N/A", "policyCurrency": "USD", "policyTerm": null, "policyNumber": "JTI23XANN-58-0", "agencyName": "Brown & Riding", "insuredName": "API AUTOMATIONS TEST <PERSON>", "duplicatedBy": "b08767fd-8e50-40db-b9b2-9e9b50ff6881", "brokerInfo": {"id": "8828f159-d76a-439a-a665-c22322077b09", "name": "Brown & Riding", "team": "Broker Team", "senderName": "<PERSON>", "senderEmail": "<EMAIL>", "mailingAddress": {"street": "null", "city": "null", "state": "null", "zipCode": "null"}}, "insuredInfo": {"id": "", "firstName": "API AUTOMATIONS TEST <PERSON>", "dba": null, "owner": "<PERSON>", "generalContractor": "Mrs. <PERSON>", "otherNamedInsureds": ["Ms. <PERSON>"], "mailingAddress": {"street1": "Street Willms Walk", "street2": "1255", "city": "Vancetown", "state": "Wyoming", "zipCode": "38241375"}}, "projectDetails": [{"id": "9f1a954a-945b-4c26-9e39-fc40df9fcc5a", "name": "API AUTOMATIONS Shamika <PERSON>y V", "street": "526", "city": "<PERSON><PERSON>", "state": "Louisiana", "zipCode": "94843", "description": null, "productType": null}], "descriptionOfOperations": "Complete rehab of property within last 4 years. New roof, added security, fence around property", "exposureDetails": {"eachOccurrenceLimitPrimary": "100000", "eachOccurrenceLimitExcess": null, "generalAggregateLimit": null, "productsCompletedOperationsAggregateLimit": null, "personalAdvertisingInjuryLimit": null, "damageToPremisesRented": null, "medicalPaymentsLimit": null, "otherAggregateLimit": null, "productsCompletedOperationsAggregate": null, "policyAggregateLimit": null, "numberOfGeneralAggregateReinstatement": 3}, "deductibleDetails": {"retentionType": null, "deductibleAmount": "1000", "sirAmount": "1000"}, "rateDetails": [{"rateByExposureBasis": "10000", "exposureBasis": "10000", "exposureBasisDetailed": "100000", "exposureAmountByExposureBasis": "10000"}], "premiumDetails": {"isoClassificationCode": "90000", "feeDescription": null, "feeDetails": [{"feeDescription": "Engineering Fee", "feeAmount": "100"}], "totalTechnicalPremium": "1", "premiumDueDate": null, "totalWrittenPremium": "122222", "underWriterDebitCreditFactor": 212, "soldToTechnicalPercentage": "0", "triaPremium": "4888.88", "totalAutos": null, "audit": "Yes", "commissionDetails": {"type": null, "percentage": 12, "amount": "14666.64"}, "totalFees": "100", "isTriaPremiumEnabled": true, "premiumReturnType": ""}, "nyftzClass": null, "scheduleOfValues": "123", "riskState": ["FL"], "softBlocked": null, "counterBorReceived": null, "tupdated": "2024-06-16T07:18:03.224258Z", "deleted": false, "tcreated": "2024-06-16T07:17:34.064714Z", "blockedSubmissions": [], "unblockSubmissionRequest": null, "assignedUser": {"userId": "6aa760cc-4cec-464d-b2dc-8d11a97f1cac", "firstName": "backend", "lastName": "auto", "userSites": null, "userRoles": ["SUBMISSION_UW", "SUBMISSION_CLEARANCE", "SUBMISSION_ADMIN"]}, "lossInfo": [], "brokerId": "8828f159-d76a-439a-a665-c22322077b09", "brokerageId": "4c6794b5-fbfe-4049-9e90-939f48856ae5", "insuredId": "5b56ad8b-9861-4ba0-b8d2-62c0d5b5db9a", "insuredNumber": "58", "officeId": "00c99f9c-1d5b-4a23-8b91-c3782e9d0c5f", "submissionDocumentIds": [], "surplusLineInfo": {"mailingAddress": {}, "filingDoneBy": "Inside broker"}, "productState": "Excess", "policyId": "1bcb6048-ec38-4a19-b2b9-ff449249cc23", "createdBy": "6aa760cc-4cec-464d-b2dc-8d11a97f1cac", "createdAt": "2024-06-16T07:23:08.698+00:00", "invoiceId": "25c6ab26-1e54-4c03-9b94-79aab79e9253"}}