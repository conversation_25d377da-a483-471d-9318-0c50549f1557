# Root logger option
log4j.rootLogger=INFO,DEBUG,TRACE,file, stdout,
# Direct log messages to a log file
log4j.appender.file=org.apache.log4j.RollingFileAppender
log4j.appender.file.File=./target/api_smoke_test.log
log4j.appender.file.MaxFileSize=10MB
log4j.appender.file.MaxBackupIndex=10
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n
# Direct log messages to stdout
log4j.logger.dbmapper.EmailEvent=DEBUG
log4j.logger.dbmapper.EmailEvent.getById=TRACE
log4j.logger.EmailEvent.getById=TRACE
log4j.logger.dbmapper.SubmissionInfo.getById=TRACE
log4j.logger.SubmissionInfo.getID=TRACE



log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %-5p %c{1}:%L - %m%n
# Apache HttpClient
log4j.logger.org.apache.http.impl.conn=INFO
log4j.logger.org.apache.http.impl.client=INFO
log4j.logger.org.apache.http.client=INFO
log4j.logger.org.apache.http=INFO
log4j.logger.org.apache.http.wire=INFO