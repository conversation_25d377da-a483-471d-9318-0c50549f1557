{"templateJson": "[\n  {\n    \"cells\": [\n      {\n        \"meta\": {\n          \"cellConfig\": {\n            \"col\": 12,\n            \"style\": {},\n            \"visibleCondition\": null\n          },\n          \"componentConfig\": null,\n          \"containerConfig\": {\n            \"title\": null,\n            \"labelTooltip\": null,\n            \"outerPadding\": \"none\",\n            \"parentContainerId\": \"submission_information\"\n          },\n          \"isWrapperRequired\": false\n        },\n        \"cells\": [\n          {\n            \"id\": \"submission-info-1\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": null,\n              \"containerConfig\": {\n                \"title\": \"Submission information\",\n                \"shadow\": false,\n                \"labelTooltip\": null,\n                \"parentContainerId\": null\n              }\n            },\n            \"cells\": [\n              {\n                \"id\": \"ea61effc-4a76-4471-85f9-a154d018e305\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"async\": true,\n                    \"label\": \"Transaction types\",\n                    \"asyncType\": \"rest\",\n                    \"keyToSave\": \"name\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Select transaction type\",\n                    \"requireSearch\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"transactionType\"]\n                    },\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/reference-data/transcation/type\",\n                      \"method\": \"GET\",\n                      \"search\": {\n                        \"enabled\": false\n                      },\n                      \"pagination\": {\n                        \"enabled\": false\n                      },\n                      \"queryParams\": {\n                        \"page\": 0,\n                        \"size\": 100\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"result\",\n                        \"labelMapper\": {\n                          \"id\": \"id\",\n                          \"name\": \"type\"\n                        }\n                      }\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"transactionType\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDropdownWithLabel\"\n              },\n              {\n                \"id\": \"029b9916-0e47-4f95-bba9-3e6d95e6578a\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"async\": true,\n                    \"label\": \"Underwriter\",\n                    \"options\": [],\n                    \"asyncType\": \"rest\",\n                    \"keyToSave\": \"name\",\n                    \"ajaxSearch\": true,\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Select underwriter\",\n                    \"requireSearch\": true,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"underwriterName\"],\n                      \"otherNameInSourcedSystemParams\": [\n                        {\n                          \"key\": \"assigneeId\",\n                          \"valueKeyToSave\": \"userId\"\n                        },\n                        {\n                          \"key\": \"underwriterEmail\",\n                          \"valueKeyToSave\": \"id\"\n                        },\n                        {\n                          \"key\": \"assignedUser.firstName\",\n                          \"valueKeyToSave\": \"firstName\"\n                        },\n                        {\n                          \"key\": \"assignedUser.lastName\",\n                          \"valueKeyToSave\": \"lastName\"\n                        },\n                        {\n                          \"key\": \"assignedUser.userRoles\",\n                          \"valueKeyToSave\": \"roles\"\n                        },\n                        {\n                          \"key\": \"assignedUser.userId\",\n                          \"valueKeyToSave\": \"userId\"\n                        }\n                      ]\n                    },\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/access-management/api/v1/users\",\n                      \"method\": \"GET\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"startWith\",\n                        \"searchPlace\": \"query\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": false\n                      },\n                      \"queryParams\": {\n                        \"page\": 0,\n                        \"size\": 100,\n                        \"roles\": \"SUBMISSION_UW\",\n                        \"startWith\": \"\"\n                      },\n                      \"requestBody\": {},\n                      \"responseStructure\": {\n                        \"dataKey\": \"result\",\n                        \"labelMapper\": {\n                          \"id\": \"email\",\n                          \"name\": [\"firstName\", \"lastName\"],\n                          \"roles\": \"roles\",\n                          \"userId\": \"userId\",\n                          \"lastName\": \"lastName\",\n                          \"firstName\": \"firstName\"\n                        }\n                      }\n                    },\n                    \"initialValidation\": {\n                      \"enabled\": true,\n                      \"invalidErrorMessage\": \"Does not match existing underwriter list\"\n                    },\n                    \"permissionsForRoles\": [\"SUBMISSION_ADMIN\"]\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"underwriterName\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDropdownWithLabel\"\n              },\n              {\n                \"id\": \"2168cd66-a81c-44d2-9f76-a0e2724359dd\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Underwriter email\",\n                    \"labelOnLeft\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"underwriterEmail\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"underwriterEmail\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"20a9ee1a-9625-4cc9-a97a-ec4eb1f64543\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Transaction/Process date\",\n                    \"disabled\": true,\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"MM-DD-YYYY\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"transactionDate\"]\n                    },\n                    \"permissionsForStates\": [\"RECEIVED\", \"NOT_CLEARED\"]\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"transactionDate\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDateWithLabel\"\n              },\n              {\n                \"id\": \"091dd312-a82e-423c-b2f8-21243e48772b\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Policy inception\",\n                    \"disableDate\": [\n                      {\n                        \"key\": \"policyExpirationDate\",\n                        \"type\": \"isAfter\"\n                      },\n                      {\n                        \"type\": \"daysAfterFromCurrent\",\n                        \"value\": 95,\n                        \"condition\": [\n                          {\n                            \"key\": \"productType\",\n                            \"type\": \"storeCheck\",\n                            \"valueCheck\": {\n                              \"type\": \"includedIn\",\n                              \"isFrom\": [\n                                \"XCAN\",\n                                \"XANN\",\n                                \"XOAN\",\n                                \"EIAN\",\n                                \"XNAG\",\n                                \"PCAN\",\n                                \"PANN\",\n                                \"POAN\",\n                                \"PIAN\",\n                                \"PNAG\",\n                                \"XAUT\"\n                              ]\n                            },\n                            \"dataStoreInfo\": {\n                              \"dataStateKey\": \"policyDetails\",\n                              \"dataStoreModule\": \"policyInfo\"\n                            }\n                          }\n                        ]\n                      }\n                    ],\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"MM-DD-YYYY\",\n                    \"valueFormatter\": {\n                      \"maxDate\": [\"policyExpirationDate\"],\n                      \"nameInSourcedSystemParams\": [\"policyInceptionDate\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"policyInceptionDate\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDateWithLabel\"\n              },\n              {\n                \"id\": \"7e9070d8-0683-4227-bcdf-0746c16a4bf8\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Policy expiration\",\n                    \"disableDate\": [\n                      {\n                        \"key\": \"policyInceptionDate\",\n                        \"type\": \"isBefore\"\n                      }\n                    ],\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"MM-DD-YYYY\",\n                    \"valueFormatter\": {\n                      \"minDate\": [\"policyInceptionDate\"],\n                      \"nameInSourcedSystemParams\": [\"policyExpirationDate\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"policyExpirationDate\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDateWithLabel\"\n              },\n              {\n                \"id\": \"12312DSD-2312ss-4dfb-a4bf-cASA11\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Product Type\",\n                    \"async\": true,\n                    \"label\": \"Product Type\",\n                    \"asyncType\": \"rest\",\n                    \"ajaxSearch\": true,\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Select product Type\",\n                    \"showTooltip\": false,\n                    \"validations\": {\n                      \"required\": true\n                    },\n                    \"requireSearch\": true,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"productType\"],\n                      \"otherNameInSourcedSystemParams\": [\n                        {\n                          \"key\": \"productName\",\n                          \"valueKeyToSave\": \"label\"\n                        }\n                      ]\n                    },\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/reference-data/products/codes\",\n                      \"method\": \"GET\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"searchText\",\n                        \"searchPlace\": \"query\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": false\n                      },\n                      \"queryParams\": {\n                        \"page\": 0,\n                        \"size\": 100,\n                        \"searchText\": \"\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"result\",\n                        \"labelMapper\": {\n                          \"id\": \"asuCode\",\n                          \"name\": \"asuCode\",\n                          \"label\": \"productCode\"\n                        }\n                      }\n                    },\n                    \"permissionsForStates\": [\"RECEIVED\", \"NOT_CLEARED\"]\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"productType\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDropdownWithLabel\"\n              },\n              {\n                \"id\": \"d71f25d0-583a-4dfb-a4bf-1a79b80f2d41\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": [\n                      {\n                        \"key\": \"transactionType\",\n                        \"type\": \"storeCheck\",\n                        \"valueCheck\": {\n                          \"value\": \"Renewal\"\n                        },\n                        \"dataStoreInfo\": {\n                          \"dataStateKey\": \"policyDetails\",\n                          \"dataStoreModule\": \"policyInfo\"\n                        }\n                      }\n                    ]\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Prior policy number\",\n                    \"labelOnLeft\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"priorPolicyNumber\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"priorPolicyNumber\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"d71f25d0-583a-4dfb-a4bf-1a79b80f2d91\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Submission/Customer number\",\n                    \"labelOnLeft\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"formattedSubmissionNumber\"]\n                    },\n                    \"conditionalDisabled\": {\n                      \"state\": \"QUOTED\"\n                    },\n                    \"dataPropertyGetterName\": \"policyInfo/getShowOnlyFieldByKey\",\n                    \"dataUpdateMutationName\": \"policyInfo/updateShowOnlyField\"\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"submissionNumber\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              }\n            ],\n            \"cellName\": \"submission-info-1\",\n            \"cellType\": \"container\",\n            \"parentId\": \"be906b4d-e09e-4a76-961a-1c764b03511b\",\n            \"component\": \"TemplateCard\"\n          },\n          {\n            \"id\": \"broker-information\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {\n                  \"padding\": \"0 24px 24px\"\n                }\n              },\n              \"componentConfig\": null,\n              \"containerConfig\": {\n                \"title\": \"Broker information\",\n                \"shadow\": false,\n                \"labelTooltip\": null,\n                \"outerPadding\": \"none\",\n                \"parentContainerId\": \"submission_information\"\n              }\n            },\n            \"cells\": [\n              {\n                \"id\": \"d71f25d0-583a-4dfb-a4bf-1423423434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"async\": true,\n                    \"label\": \"Brokerage name\",\n                    \"asyncType\": \"rest\",\n                    \"keyToSave\": \"name\",\n                    \"ajaxSearch\": true,\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"requireSearch\": true,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"brokerInfo.name\"],\n                      \"otherNameInSourcedSystemParams\": [\n                        {\n                          \"key\": \"brokerageId\",\n                          \"valueKeyToSave\": \"id\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.senderName\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerId\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.id\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.team\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.senderEmail\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.street\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.city\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.state\",\n                          \"valueToSave\": \"\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.zipCode\",\n                          \"valueToSave\": \"\"\n                        }\n                      ]\n                    },\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/brokerage/search\",\n                      \"method\": \"POST\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"searchText\",\n                        \"searchPlace\": \"body\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"pageKey\": \"pageNumber\",\n                        \"sizeKey\": \"pageSize\",\n                        \"defaultSize\": 20,\n                        \"paginationPlace\": \"query\"\n                      },\n                      \"queryParams\": {\n                        \"sortBy\": \"name\",\n                        \"pageSize\": 20,\n                        \"sortOrder\": \"ASC\",\n                        \"pageNumber\": 0\n                      },\n                      \"requestBody\": {\n                        \"searchText\": \"\"\n                      },\n                      \"responseStructure\": {\n                        \"dataKey\": \"data.items\",\n                        \"labelMapper\": {\n                          \"id\": \"brokerage.brokerageId\",\n                          \"name\": \"brokerage.name\"\n                        },\n                        \"totalItemsKey\": \"data.totalItems\"\n                      }\n                    },\n                    \"dropdownPlaceholder\": \"Select Broker\",\n                    \"initialAsyncValidation\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/brokerage/search\",\n                      \"method\": \"POST\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"searchText\"\n                      },\n                      \"enabled\": true,\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"pageKey\": \"pageNumber\",\n                        \"sizeKey\": \"pageSize\",\n                        \"defaultSize\": 10,\n                        \"paginationPlace\": \"query\"\n                      },\n                      \"queryParams\": {\n                        \"sortBy\": \"name\",\n                        \"pageSize\": 20,\n                        \"sortOrder\": \"ASC\",\n                        \"pageNumber\": 0\n                      },\n                      \"requestBody\": {\n                        \"searchText\": \"\"\n                      },\n                      \"keyToValidate\": \"brokerage.name\",\n                      \"responseStructure\": {\n                        \"dataKey\": \"data.items\",\n                        \"labelMapper\": {\n                          \"id\": \"brokerage.brokerageId\",\n                          \"name\": \"brokerage.name\"\n                        },\n                        \"totalItemsKey\": \"data.totalItems\"\n                      },\n                      \"invalidErrorMessage\": \"Does not match existing broker list\"\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.name\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDropdownWithLabel\"\n              },\n              {\n                \"id\": \"d71f25d0-3123123-4dfb-a4bf-1423423434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker team\",\n                    \"labelOnLeft\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"brokerInfo.team\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.team\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"123123-3123123-4dfb-a4bf-1423423434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"async\": true,\n                    \"label\": \"Broker name (sender)\",\n                    \"asyncType\": \"rest\",\n                    \"keyToSave\": \"name\",\n                    \"ajaxSearch\": true,\n                    \"labelOnLeft\": false,\n                    \"noDataFound\": {\n                      \"template\": [\n                        {\n                          \"id\": \"838831-3123123-4dfb-a4bf-2233\",\n                          \"meta\": {\n                            \"cellConfig\": {\n                              \"col\": 4,\n                              \"style\": {},\n                              \"visibleCondition\": null\n                            },\n                            \"componentConfig\": {\n                              \"text\": \"No broker of this name found in the system\"\n                            },\n                            \"containerConfig\": null\n                          },\n                          \"cells\": null,\n                          \"cellName\": \"noDataFound\",\n                          \"cellType\": \"component\",\n                          \"component\": \"NoDataFound\"\n                        },\n                        {\n                          \"id\": \"99999-3123123-4dfb-a4bf-2233\",\n                          \"meta\": {\n                            \"cellConfig\": {\n                              \"col\": 4,\n                              \"style\": {},\n                              \"visibleCondition\": null\n                            },\n                            \"componentConfig\": {\n                              \"text\": \"Add broker\",\n                              \"uniqueId\": \"add-edit-broker\",\n                              \"modalTemplate\": {\n                                \"office\": {\n                                  \"async\": true,\n                                  \"label\": \"Office\",\n                                  \"asyncType\": \"rest\",\n                                  \"keyToSave\": \"id\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Office\",\n                                  \"validations\": {\n                                    \"required\": true\n                                  },\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\"officeId\"],\n                                    \"otherNameInSourcedSystemParams\": [\n                                      {\n                                        \"key\": \"office.addressLine1\",\n                                        \"valueKeyToSave\": \"addressLine1\"\n                                      },\n                                      {\n                                        \"key\": \"office.addressLine2\",\n                                        \"valueKeyToSave\": \"addressLine2\"\n                                      },\n                                      {\n                                        \"key\": \"office.country\",\n                                        \"valueKeyToSave\": \"country\"\n                                      },\n                                      {\n                                        \"key\": \"office.state\",\n                                        \"valueKeyToSave\": \"state\"\n                                      },\n                                      {\n                                        \"key\": \"office.city\",\n                                        \"valueKeyToSave\": \"city\"\n                                      },\n                                      {\n                                        \"key\": \"office.zipCode\",\n                                        \"valueKeyToSave\": \"zipCode\"\n                                      }\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/office/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"body\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"pageNumber\",\n                                      \"sizeKey\": \"pageSize\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"sortBy\": \"officeName\",\n                                      \"sortOrder\": \"ASC\"\n                                    },\n                                    \"requestBody\": {\n                                      \"brokerageId\": \"\"\n                                    },\n                                    \"mapKeyFromData\": [\n                                      {\n                                        \"mapType\": \"string\",\n                                        \"mapDataIn\": \"body\",\n                                        \"mapDataTo\": \"brokerageId\",\n                                        \"mapDataFrom\": \"brokerageId\"\n                                      }\n                                    ],\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"data.offices\",\n                                      \"labelMapper\": {\n                                        \"id\": \"officeId\",\n                                        \"city\": \"address.city\",\n                                        \"name\": \"officeName\",\n                                        \"state\": \"address.state\",\n                                        \"country\": \"address.country\",\n                                        \"zipCode\": \"address.zipCode\",\n                                        \"addressLine1\": \"address.addressLine1\",\n                                        \"addressLine2\": \"address.addressLine2\"\n                                      },\n                                      \"totalItemsKey\": \"data.totalItems\"\n                                    }\n                                  },\n                                  \"conditionalDisabled\": {\n                                    \"brokerageId\": \"NOT_DEFINED\"\n                                  }\n                                },\n                                \"homeCity\": {\n                                  \"async\": true,\n                                  \"label\": \"Home City\",\n                                  \"asyncType\": \"rest\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Home City\",\n                                  \"validations\": {},\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"address.city\"\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"query\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"page\",\n                                      \"sizeKey\": \"size\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"entityType\": \"City\"\n                                    },\n                                    \"requestBody\": {\n                                      \"metaDataFilters\": [\n                                        {\n                                          \"key\": \"Country\",\n                                          \"value\": \"\",\n                                          \"mandatory\": true\n                                        },\n                                        {\n                                          \"key\": \"State\",\n                                          \"value\": \"\",\n                                          \"mandatory\": true\n                                        }\n                                      ]\n                                    },\n                                    \"mapKeyFromData\": [\n                                      {\n                                        \"mapType\": \"arrayItem\",\n                                        \"mapDataIn\": \"body\",\n                                        \"mapDataTo\": \"metaDataFilters|0|value\",\n                                        \"mapDataFrom\": \"address.country\"\n                                      },\n                                      {\n                                        \"mapType\": \"arrayItem\",\n                                        \"mapDataIn\": \"body\",\n                                        \"mapDataTo\": \"metaDataFilters|1|value\",\n                                        \"mapDataFrom\": \"address.state\"\n                                      }\n                                    ],\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"content\",\n                                      \"labelMapper\": {\n                                        \"id\": \"dataValue\",\n                                        \"name\": \"dataValue\"\n                                      },\n                                      \"totalItemsKey\": \"totalElements\"\n                                    }\n                                  }\n                                },\n                                \"brokerage\": {\n                                  \"async\": true,\n                                  \"label\": \"Brokerage\",\n                                  \"asyncType\": \"rest\",\n                                  \"keyToSave\": \"id\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Brokerage\",\n                                  \"validations\": {\n                                    \"required\": true\n                                  },\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"brokerageId\"\n                                    ],\n                                    \"otherNameInSourcedSystemParams\": [\n                                      {\n                                        \"key\": \"brokerageName\",\n                                        \"valueKeyToSave\": \"name\"\n                                      }\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/brokerage/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"body\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"pageNumber\",\n                                      \"sizeKey\": \"pageSize\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {},\n                                    \"requestBody\": {},\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"data.items\",\n                                      \"labelMapper\": {\n                                        \"id\": \"brokerage.brokerageId\",\n                                        \"name\": \"brokerage.name\"\n                                      },\n                                      \"totalItemsKey\": \"data.totalItems\"\n                                    }\n                                  }\n                                },\n                                \"homeState\": {\n                                  \"async\": true,\n                                  \"label\": \"Home State\",\n                                  \"asyncType\": \"rest\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Home State\",\n                                  \"validations\": {},\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"address.state\"\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"query\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"page\",\n                                      \"sizeKey\": \"size\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"entityType\": \"State\"\n                                    },\n                                    \"requestBody\": {\n                                      \"metaDataFilters\": [\n                                        {\n                                          \"key\": \"Country\",\n                                          \"value\": \"\",\n                                          \"mandatory\": true\n                                        }\n                                      ]\n                                    },\n                                    \"mapKeyFromData\": [\n                                      {\n                                        \"mapType\": \"arrayItem\",\n                                        \"mapDataIn\": \"body\",\n                                        \"mapDataTo\": \"metaDataFilters|0|value\",\n                                        \"mapDataFrom\": \"address.country\"\n                                      }\n                                    ],\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"content\",\n                                      \"labelMapper\": {\n                                        \"id\": \"dataValue\",\n                                        \"name\": \"dataValue\"\n                                      },\n                                      \"totalItemsKey\": \"totalElements\"\n                                    }\n                                  }\n                                },\n                                \"homeCountry\": {\n                                  \"async\": true,\n                                  \"label\": \"Home Country\",\n                                  \"asyncType\": \"rest\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Home Country\",\n                                  \"validations\": {},\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"address.country\"\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"query\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"page\",\n                                      \"sizeKey\": \"size\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"entityType\": \"Country\"\n                                    },\n                                    \"requestBody\": {\n                                      \"metaDataFilters\": []\n                                    },\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"content\",\n                                      \"labelMapper\": {\n                                        \"id\": \"dataValue\",\n                                        \"name\": \"dataValue\"\n                                      },\n                                      \"totalItemsKey\": \"totalElements\"\n                                    }\n                                  }\n                                },\n                                \"surplusLineCity\": {\n                                  \"async\": true,\n                                  \"label\": \"Surplus lines city\",\n                                  \"asyncType\": \"rest\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Surplus lines city\",\n                                  \"validations\": {},\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"surplusLinesCity\"\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"query\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"page\",\n                                      \"sizeKey\": \"size\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"entityType\": \"City\"\n                                    },\n                                    \"requestBody\": {\n                                      \"metaDataFilters\": [\n                                        {\n                                          \"key\": \"Country\",\n                                          \"value\": \"US\",\n                                          \"mandatory\": true\n                                        },\n                                        {\n                                          \"key\": \"State\",\n                                          \"value\": \"\",\n                                          \"mandatory\": true\n                                        }\n                                      ]\n                                    },\n                                    \"mapKeyFromData\": [\n                                      {\n                                        \"mapType\": \"arrayItem\",\n                                        \"mapDataIn\": \"body\",\n                                        \"mapDataTo\": \"metaDataFilters|1|value\",\n                                        \"mapDataFrom\": \"surplusLinesState\"\n                                      }\n                                    ],\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"content\",\n                                      \"labelMapper\": {\n                                        \"id\": \"dataValue\",\n                                        \"name\": \"dataValue\"\n                                      },\n                                      \"totalItemsKey\": \"totalElements\"\n                                    }\n                                  },\n                                  \"conditionalDisabled\": {\n                                    \"filingBroker\": false\n                                  },\n                                  \"conditionalValidations\": {\n                                    \"filingBroker\": {\n                                      \"value\": true,\n                                      \"validations\": {\n                                        \"required\": true\n                                      }\n                                    }\n                                  }\n                                },\n                                \"surplusLinesState\": {\n                                  \"async\": true,\n                                  \"label\": \"Surplus lines state\",\n                                  \"asyncType\": \"rest\",\n                                  \"ajaxSearch\": true,\n                                  \"labelOnLeft\": true,\n                                  \"placeholder\": \"Surplus lines state\",\n                                  \"validations\": {},\n                                  \"requireSearch\": true,\n                                  \"valueFormatter\": {\n                                    \"nameInSourcedSystemParams\": [\n                                      \"surplusLinesState\"\n                                    ]\n                                  },\n                                  \"asyncProperties\": {\n                                    \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                                    \"method\": \"POST\",\n                                    \"search\": {\n                                      \"enabled\": true,\n                                      \"searchKey\": \"searchText\",\n                                      \"searchPlace\": \"query\"\n                                    },\n                                    \"pagination\": {\n                                      \"enabled\": true,\n                                      \"pageKey\": \"page\",\n                                      \"sizeKey\": \"size\",\n                                      \"defaultSize\": 20,\n                                      \"paginationPlace\": \"query\"\n                                    },\n                                    \"queryParams\": {\n                                      \"entityType\": \"State\"\n                                    },\n                                    \"requestBody\": {\n                                      \"metaDataFilters\": [\n                                        {\n                                          \"key\": \"Country\",\n                                          \"value\": \"US\",\n                                          \"mandatory\": true\n                                        }\n                                      ]\n                                    },\n                                    \"mapKeyFromData\": [],\n                                    \"setRequestDataIn\": \"requestBody\",\n                                    \"responseStructure\": {\n                                      \"dataKey\": \"content\",\n                                      \"labelMapper\": {\n                                        \"id\": \"dataValue\",\n                                        \"name\": \"dataValue\"\n                                      },\n                                      \"totalItemsKey\": \"totalElements\"\n                                    }\n                                  },\n                                  \"conditionalDisabled\": {\n                                    \"filingBroker\": false\n                                  },\n                                  \"conditionalValidations\": {\n                                    \"filingBroker\": {\n                                      \"value\": true,\n                                      \"validations\": {\n                                        \"required\": true\n                                      }\n                                    }\n                                  }\n                                }\n                              },\n                              \"modalSubmitApiConfig\": {\n                                \"asyncFor\": \"update\",\n                                \"asyncType\": \"rest\",\n                                \"defaultMessage\": {\n                                  \"errorText\": \"Failed to add broker\",\n                                  \"successText\": \"<p>New broker <b> </b> has been added</p>\",\n                                  \"errorHeading\": \"Error\",\n                                  \"successHeading\": \"Broker added\"\n                                },\n                                \"asyncProperties\": {\n                                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/broker\",\n                                  \"method\": \"POST\",\n                                  \"search\": {\n                                    \"enabled\": false\n                                  },\n                                  \"pagination\": {\n                                    \"enabled\": false\n                                  },\n                                  \"queryParams\": {},\n                                  \"requestBody\": {\n                                    \"team\": \"\",\n                                    \"status\": \"ACTIVE\",\n                                    \"address\": {\n                                      \"city\": \"\",\n                                      \"state\": \"\",\n                                      \"country\": \"\",\n                                      \"zipCode\": \"\",\n                                      \"addressType\": \"\",\n                                      \"addressLine1\": \"\",\n                                      \"addressLine2\": \"\"\n                                    },\n                                    \"jobTitle\": \"\",\n                                    \"lastName\": \"\",\n                                    \"officeId\": \"\",\n                                    \"firstName\": \"\",\n                                    \"workPhone\": \"\",\n                                    \"brokerageId\": \"\",\n                                    \"legalEntity\": \"\",\n                                    \"mobilePhone\": \"\",\n                                    \"emailAddress\": \"\",\n                                    \"filingBroker\": false,\n                                    \"brokerageName\": \"\",\n                                    \"surplusLinesCity\": \"\",\n                                    \"surplusLinesState\": \"\",\n                                    \"surplusLinesAddress\": \"\",\n                                    \"surplusLinesZipCode\": \"\",\n                                    \"surplusLinesBrokerageName\": \"\",\n                                    \"surplusLinesLicenseNumber\": \"\"\n                                  },\n                                  \"mapKeyFromData\": [\n                                    {\n                                      \"mapType\": \"object\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"address\",\n                                      \"mapDataFrom\": \"address\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"brokerageId\",\n                                      \"mapDataFrom\": \"brokerageId\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"emailAddress\",\n                                      \"mapDataFrom\": \"emailAddress\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"filingBroker\",\n                                      \"mapDataFrom\": \"filingBroker\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"firstName\",\n                                      \"mapDataFrom\": \"firstName\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"jobTitle\",\n                                      \"mapDataFrom\": \"jobTitle\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"lastName\",\n                                      \"mapDataFrom\": \"lastName\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"legalEntity\",\n                                      \"mapDataFrom\": \"legalEntity\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"mobilePhone\",\n                                      \"mapDataFrom\": \"mobilePhone\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"officeId\",\n                                      \"mapDataFrom\": \"officeId\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesAddress\",\n                                      \"mapDataFrom\": \"surplusLinesAddress\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesBrokerageName\",\n                                      \"mapDataFrom\": \"surplusLinesBrokerageName\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesLicenseNumber\",\n                                      \"mapDataFrom\": \"surplusLinesLicenseNumber\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesZipCode\",\n                                      \"mapDataFrom\": \"surplusLinesZipCode\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesCity\",\n                                      \"mapDataFrom\": \"surplusLinesCity\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"surplusLinesState\",\n                                      \"mapDataFrom\": \"surplusLinesState\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"team\",\n                                      \"mapDataFrom\": \"team\"\n                                    },\n                                    {\n                                      \"mapType\": \"string\",\n                                      \"mapDataIn\": \"body\",\n                                      \"mapDataTo\": \"workPhone\",\n                                      \"mapDataFrom\": \"workPhone\"\n                                    }\n                                  ],\n                                  \"responseStructure\": {}\n                                }\n                              }\n                            },\n                            \"containerConfig\": null\n                          },\n                          \"cells\": null,\n                          \"cellName\": \"addBroker\",\n                          \"cellType\": \"component\",\n                          \"component\": \"BrokerAddActionWithModal\"\n                        }\n                      ]\n                    },\n                    \"placeholder\": \"Select Broker\",\n                    \"requireSearch\": true,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"brokerInfo.senderName\"],\n                      \"otherNameInSourcedSystemParams\": [\n                        {\n                          \"key\": \"brokerId\",\n                          \"valueKeyToSave\": \"id\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.id\",\n                          \"valueKeyToSave\": \"id\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.team\",\n                          \"valueKeyToSave\": \"team\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.senderEmail\",\n                          \"valueKeyToSave\": \"senderEmail\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.street\",\n                          \"valueKeyToSave\": \"addressLine1\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.city\",\n                          \"valueKeyToSave\": \"city\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.state\",\n                          \"valueKeyToSave\": \"state\"\n                        },\n                        {\n                          \"key\": \"brokerInfo.mailingAddress.zipCode\",\n                          \"valueKeyToSave\": \"zipCode\"\n                        },\n                        {\n                          \"key\": \"officeId\",\n                          \"valueKeyToSave\": \"officeId\"\n                        }\n                      ]\n                    },\n                    \"asyncProperties\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/broker/search\",\n                      \"method\": \"POST\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"searchName\",\n                        \"searchPlace\": \"body\"\n                      },\n                      \"pagination\": {\n                        \"enabled\": true,\n                        \"pageKey\": \"pageNumber\",\n                        \"sizeKey\": \"pageSize\",\n                        \"defaultSize\": 30,\n                        \"paginationPlace\": \"query\"\n                      },\n                      \"queryParams\": {\n                        \"sortBy\": \"firstName\",\n                        \"pageSize\": 30,\n                        \"sortOrder\": \"ASC\",\n                        \"pageNumber\": 0\n                      },\n                      \"requestBody\": {\n                        \"searchName\": \"\",\n                        \"brokerageNames\": [null]\n                      },\n                      \"mapKeyFromData\": [\n                        {\n                          \"mapType\": \"arrayString\",\n                          \"mapDataIn\": \"body\",\n                          \"mapDataTo\": \"brokerageNames\",\n                          \"mapDataFrom\": \"brokerInfo.name\"\n                        }\n                      ],\n                      \"setRequestDataIn\": \"requestBody\",\n                      \"responseStructure\": {\n                        \"dataKey\": \"data.items\",\n                        \"labelMapper\": {\n                          \"id\": \"broker.brokerId\",\n                          \"city\": \"broker.office.address.city\",\n                          \"name\": [\"broker.firstName\", \"broker.lastName\"],\n                          \"team\": \"broker.team\",\n                          \"state\": \"broker.office.address.state\",\n                          \"zipCode\": \"broker.office.address.zipCode\",\n                          \"officeId\": \"broker.officeId\",\n                          \"senderEmail\": \"broker.emailAddress\",\n                          \"addressLine1\": [\n                            \"broker.office.address.addressLine1\",\n                            \"broker.office.address.addressLine2\"\n                          ]\n                        },\n                        \"totalItemsKey\": \"data.totalItems\"\n                      }\n                    },\n                    \"dropdownPlaceholder\": \"Select Broker\",\n                    \"initialAsyncValidation\": {\n                      \"url\": \"https://dev-api.submission.concirrusquest.com/core/brokerage-service/broker/search\",\n                      \"method\": \"POST\",\n                      \"search\": {\n                        \"enabled\": true,\n                        \"searchKey\": \"searchName\",\n                        \"searchPlace\": \"body\"\n                      },\n                      \"enabled\": true,\n                      \"requestBody\": {\n                        \"searchName\": \"\"\n                      },\n                      \"mapKeyFromData\": [\n                        {\n                          \"mapType\": \"arrayString\",\n                          \"mapDataIn\": \"body\",\n                          \"mapDataTo\": \"brokerageNames\",\n                          \"mapDataFrom\": \"brokerInfo.name\"\n                        }\n                      ],\n                      \"responseStructure\": {\n                        \"dataKey\": \"data.items\",\n                        \"labelMapper\": {\n                          \"id\": \"broker.brokerId\"\n                        },\n                        \"totalItemsKey\": \"data.totalItems\"\n                      },\n                      \"invalidErrorMessage\": \"Does not match with the selected brokerage\"\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.senderName\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseDropdownWithLabel\"\n              },\n              {\n                \"id\": \"123123-3123123-4dfb-a4bf-123434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker email\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"validations\": {\n                      \"email\": true\n                    },\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"brokerInfo.senderEmail\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.senderEmail\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"123123-323-4dfb-a4bf-123434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker mailing street address\",\n                    \"labelOnLeft\": false,\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"brokerInfo.mailingAddress.street\"\n                      ]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.mailingAddress.street\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"31assd-323-4dfb-a4bf-123434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker mailing city\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"brokerInfo.mailingAddress.city\"\n                      ]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.mailingAddress.city\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"31assd-322223-4dfb-a4bf-22123434\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker mailing state\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"brokerInfo.mailingAddress.state\"\n                      ]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.mailingAddress.state\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"31assd-322223-4dfb-a4bf-21321\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 4,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Broker mailing zip code\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\n                        \"brokerInfo.mailingAddress.zipCode\"\n                      ]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"brokerInfo.mailingAddress.zipCode\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              }\n            ],\n            \"cellName\": \"broker-information\",\n            \"cellType\": \"container\",\n            \"parentId\": \"19fb1367-99c4-42c0-9f47-fbe6e51a4f98\",\n            \"component\": \"TemplateCard\"\n          }\n        ],\n        \"cellName\": \"submission-container-1\",\n        \"cellType\": \"container\",\n        \"parentId\": \"b4a7c7150-1797-4b45-aed5-972f41582027\",\n        \"component\": \"TemplateCard\"\n      },\n      {\n        \"meta\": {\n          \"cellConfig\": {\n            \"col\": 6,\n            \"style\": {},\n            \"visibleCondition\": null\n          },\n          \"componentConfig\": null,\n          \"containerConfig\": {\n            \"title\": \"Insured Information\",\n            \"labelTooltip\": null\n          },\n          \"isWrapperRequired\": false\n        },\n        \"cells\": [\n          {\n            \"id\": \"31assd-assdd21-4dfb-a4bf-21321\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"First named insured\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.firstName\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.firstName\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-assdd21-4dfb-a4bf-dqwdqw\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"DBA\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.dba\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.dba\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"31assd-assdd21-4dfb-a4bf-12223ddd\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Other named insured(s)\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormat\": \"Array\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.otherNamedInsureds\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.otherNamedInsureds\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"222edww-assdd21-4dfb-a4bf-12223ddd\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Named insured - owner\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.owner\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.owner\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"222edww-assdd21-4dfb-a4bf-12312s1\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Named insured - General Contractor/Construction manager\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"insuredInfo.generalContractor\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.generalContractor\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"222edww-2312ss-4dfb-a4bf-12312s1\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Mailing street address 1\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.street1\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.street1\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"222edww-2312ss-4dfb-a4bf-qweqwed\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Mailing street address 2\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.street2\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.street2\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"wwxs222-2312ss-4dfb-a4bf-qweqwed\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Mailing city\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.city\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.city\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"wwxs222-2312ss-4dfb-a4bf-cASA11\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Mailing state\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.state\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.state\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          },\n          {\n            \"id\": \"2QDQDQW-2312ss-4dfb-a4bf-cASA11\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"Mailing zipcode\",\n                \"labelOnLeft\": false,\n                \"placeholder\": \"Enter here\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\n                    \"insuredInfo.mailingAddress.zipCode\"\n                  ]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"insuredInfo.mailingAddress.zipCode\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          }\n        ],\n        \"cellName\": \"submission-container-2\",\n        \"cellType\": \"container\",\n        \"parentId\": \"bd90ecf6-21e4-4a58-b25a-e5352ec652f5\",\n        \"component\": \"TemplateCard\"\n      },\n      {\n        \"meta\": {\n          \"cellConfig\": {\n            \"col\": 6,\n            \"style\": {\n              \"padding\": \"24px 0\"\n            },\n            \"visibleCondition\": null\n          },\n          \"componentConfig\": null,\n          \"containerConfig\": {\n            \"title\": null,\n            \"labelTooltip\": null,\n            \"outerPadding\": \"none\"\n          },\n          \"isWrapperRequired\": false\n        },\n        \"cells\": [\n          {\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {\n                  \"padding\": \"0 24px 10px\",\n                  \"borderBottom\": \"1px solid #CBEDFD\"\n                },\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": null,\n              \"containerConfig\": {\n                \"title\": \"Location/Project details\",\n                \"shadow\": false,\n                \"labelTooltip\": null,\n                \"outerPadding\": \"none\"\n              },\n              \"isWrapperRequired\": false\n            },\n            \"cells\": [\n              {\n                \"id\": \"029b9916-0e47-4f95-bba9-3e6d9qwe\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"label\": \"Schedule of values\",\n                    \"inputType\": \"currency\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"scheduleOfValues\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"scheduleOfValues\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              }\n            ],\n            \"cellName\": \"scheduleOfValues\",\n            \"cellType\": \"container\",\n            \"parentId\": \"asd221-c0d1-42d8-b67f-3ed36216451d\",\n            \"component\": \"TemplateCard\"\n          },\n          {\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {\n                  \"padding\": \"0 24px\"\n                },\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": null,\n              \"containerConfig\": {\n                \"title\": null,\n                \"shadow\": false,\n                \"blockId\": \"project_details\",\n                \"variant\": \"secondary\",\n                \"navLabel\": \"name\",\n                \"blockLabel\": \"Project(s)\",\n                \"addNewLabel\": \"Add\",\n                \"showAddIcon\": true,\n                \"labelTooltip\": null,\n                \"outerPadding\": \"none\",\n                \"valueFormatter\": {\n                  \"defaultMap\": {\n                    \"city\": \"\",\n                    \"name\": \"\",\n                    \"state\": \"\",\n                    \"street\": \"\",\n                    \"zipCode\": \"\",\n                    \"description\": \"\"\n                  },\n                  \"nameInSourcedSystemParams\": [\"projectDetails\"]\n                },\n                \"showDeleteModal\": true,\n                \"uniqueIdentifier\": \"id\",\n                \"deleteModalMessage\": \"Are you sure you want to delete the {_inputLabel_} form the Project(s)\"\n              },\n              \"isWrapperRequired\": true\n            },\n            \"cells\": [\n              {\n                \"id\": \"5f41bf86-592e-48ff-b9f2-56e127e0f982\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Project name\",\n                    \"label\": \"Project name\",\n                    \"dataType\": \"list\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"name\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"projectDetails.name\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"5f41bf86-592e-48ff-b9f2-56e127e0f983\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Project street address\",\n                    \"label\": \"Project street address\",\n                    \"dataType\": \"list\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"street\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"projectDetails.street\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"5f41bf86-592e-48ff-b9f2-56e127e0f984\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Project city\",\n                    \"label\": \"Project city\",\n                    \"dataType\": \"list\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"city\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"projectDetails.city\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"5f41bf86-592e-48ff-b9f2-56e127e0f985\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Project state\",\n                    \"label\": \"Project state\",\n                    \"dataType\": \"list\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"state\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"projectDetails.state\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              },\n              {\n                \"id\": \"5f41bf86-592e-48ff-b9f2-56e127e0f986\",\n                \"meta\": {\n                  \"cellConfig\": {\n                    \"col\": 12,\n                    \"style\": {},\n                    \"visibleCondition\": null\n                  },\n                  \"componentConfig\": {\n                    \"name\": \"Project zip code\",\n                    \"label\": \"Project zip code\",\n                    \"dataType\": \"list\",\n                    \"labelOnLeft\": false,\n                    \"placeholder\": \"Enter here\",\n                    \"valueFormatter\": {\n                      \"nameInSourcedSystemParams\": [\"projectDetails\", \"zipCode\"]\n                    }\n                  },\n                  \"containerConfig\": null\n                },\n                \"cells\": null,\n                \"cellName\": \"projectDetails.zipCode\",\n                \"cellType\": \"component\",\n                \"component\": \"BaseInputWithLabel\"\n              }\n            ],\n            \"cellName\": \"projectDetails\",\n            \"cellType\": \"container\",\n            \"parentId\": \"576d8a4d-85a6-42f3-befe-3d690ef70f91\",\n            \"component\": \"TemplateCardWithNavList\"\n          }\n        ],\n        \"cellName\": \"submission-container-3\",\n        \"cellType\": \"container\",\n        \"parentId\": \"103753fb-c0d1-42d8-b67f-3ed36216451d\",\n        \"component\": \"TemplateCard\"\n      },\n      {\n        \"meta\": {\n          \"cellConfig\": {\n            \"col\": 12,\n            \"style\": {},\n            \"visibleCondition\": null\n          },\n          \"componentConfig\": null,\n          \"containerConfig\": {\n            \"title\": \"Description of Operations\",\n            \"labelTooltip\": null,\n            \"parentContainerId\": \"description_of_operations\"\n          },\n          \"isWrapperRequired\": false\n        },\n        \"cells\": [\n          {\n            \"id\": \"asdasdasd-2312ss-4dfb-a4bf-12233\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"name\": \"Description of operations\",\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"descriptionOfOperations\"]\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"descriptionOfOperations\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseTextArea\"\n          },\n          {\n            \"id\": \"asdasdasd-2312ss-4dfb-a4bf-dfdadlk\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"async\": true,\n                \"label\": \"Risk state\",\n                \"options\": [],\n                \"checkbox\": true,\n                \"asyncType\": \"rest\",\n                \"ajaxSearch\": true,\n                \"isMultiple\": true,\n                \"labelOnLeft\": true,\n                \"placeholder\": \"Select risk state\",\n                \"requireSearch\": true,\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"riskState\"]\n                },\n                \"asyncProperties\": {\n                  \"url\": \"https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search\",\n                  \"method\": \"POST\",\n                  \"search\": {\n                    \"enabled\": false\n                  },\n                  \"pagination\": {\n                    \"enabled\": true,\n                    \"pageKey\": \"page\",\n                    \"sizeKey\": \"size\",\n                    \"defaultSize\": 100,\n                    \"paginationPlace\": \"query\"\n                  },\n                  \"queryParams\": {\n                    \"page\": 0,\n                    \"size\": 100,\n                    \"entityType\": \"riskStates\"\n                  },\n                  \"requestBody\": {\n                    \"metaDataFilters\": []\n                  },\n                  \"responseStructure\": {\n                    \"dataKey\": \"content\",\n                    \"labelMapper\": {\n                      \"id\": \"dataValue\",\n                      \"name\": [\"metadata.abbreviation\", \"dataValue\"]\n                    },\n                    \"concatString\": \" - \",\n                    \"totalItemsKey\": \"totalElements\"\n                  }\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"riskState\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseDropdownWithLabel\"\n          },\n          {\n            \"id\": \"31assd-322223-4dfb-a4bf987e27bke2\",\n            \"meta\": {\n              \"cellConfig\": {\n                \"col\": 12,\n                \"style\": {},\n                \"visibleCondition\": null\n              },\n              \"componentConfig\": {\n                \"label\": \"New Jersey Transaction Number\",\n                \"labelOnLeft\": true,\n                \"validations\": {\n                  \"required\": true\n                },\n                \"valueFormatter\": {\n                  \"nameInSourcedSystemParams\": [\"newJerseyTransactionNumber\"]\n                },\n                \"conditionalHidden\": {\n                  \"riskState\": {\n                    \"data\": \"NJ\",\n                    \"type\": \"not_contains\",\n                    \"matchKey\": \"\"\n                  }\n                }\n              },\n              \"containerConfig\": null\n            },\n            \"cells\": null,\n            \"cellName\": \"surplusLineInfo.newJerseyTransactionNumber\",\n            \"cellType\": \"component\",\n            \"component\": \"BaseInputWithLabel\"\n          }\n        ],\n        \"cellName\": \"submission-container-4\",\n        \"cellType\": \"container\",\n        \"parentId\": \"09bc1d04-32ad-4be0-893a-ed8ad389e5cb\",\n        \"component\": \"TemplateCard\"\n      }\n    ],\n    \"clientId\": \"803517a8-c42a-44c3-b54b-fccafaec1ad3\"\n  }\n]\n", "validation": "{\"type\": {\"enum\": [\"CONSTRUCTION\"], \"type\": \"string\", \"isRequired\": true}, \"state\": {\"type\": \"string\", \"isRequired\": true}, \"status\": {\"type\": \"string\", \"isRequired\": true}, \"carrier\": {\"type\": \"string\", \"isRequired\": true}, \"clientId\": {\"type\": \"string\", \"isRequired\": true}, \"riskState\": {\"type\": \"string\", \"isRequired\": false}, \"assigneeId\": {\"type\": \"string\", \"isRequired\": false}, \"brokerInfo\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"isRequired\": true}, \"name\": {\"type\": \"string\", \"isRequired\": true}, \"team\": {\"type\": \"string\", \"isRequired\": false}, \"senderName\": {\"type\": \"string\", \"isRequired\": true}, \"senderEmail\": {\"type\": \"string\", \"format\": \"email\", \"isRequired\": true}, \"mailingAddress\": {\"type\": \"object\", \"properties\": {\"city\": {\"type\": \"string\", \"isRequired\": true}, \"state\": {\"type\": \"string\", \"isRequired\": true}, \"street\": {\"type\": \"string\", \"isRequired\": true}, \"zipCode\": {\"type\": \"string\", \"isRequired\": true}}}}}, \"nyftzClass\": {\"type\": \"string\", \"isRequired\": false}, \"insuredInfo\": {\"type\": \"object\", \"properties\": {\"id\": {\"type\": \"string\", \"isRequired\": true}, \"dba\": {\"type\": \"string\", \"isRequired\": false}, \"owner\": {\"type\": \"string\", \"isRequired\": false}, \"firstName\": {\"type\": \"string\", \"isRequired\": true}, \"mailingAddress\": {\"type\": \"object\", \"properties\": {\"city\": {\"type\": \"string\", \"isRequired\": true}, \"state\": {\"type\": \"string\", \"isRequired\": true}, \"street1\": {\"type\": \"string\", \"isRequired\": true}, \"street2\": {\"type\": \"string\", \"isRequired\": false}, \"zipCode\": {\"type\": \"string\", \"isRequired\": true}}}, \"generalContractor\": {\"type\": \"string\", \"isRequired\": false}, \"otherNamedInsureds\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"isRequired\": false}}}, \"productName\": {\"type\": \"string\", \"isRequired\": true}, \"productType\": {\"type\": \"string\", \"isRequired\": true}, \"rateDetails\": {\"type\": \"array\", \"items\": {\"type\": \"object\", \"properties\": {\"exposureBasis\": {\"type\": \"string\", \"isRequired\": false}, \"rateByExposureBasis\": {\"type\": \"string\", \"isRequired\": false}, \"exposureBasisDetailed\": {\"type\": \"string\", \"isRequired\": false}, \"exposureAmountByExposureBasis\": {\"type\": \"string\", \"isRequired\": false}}}}, \"submissionId\": {\"type\": \"string\", \"isRequired\": true}, \"definedStatus\": {\"type\": \"string\", \"isRequired\": false}, \"policyCurrency\": {\"type\": \"string\", \"isRequired\": true}, \"premiumDetails\": {\"type\": \"object\", \"properties\": {\"audit\": {\"type\": \"string\", \"isRequired\": false}, \"feeDetails\": {\"type\": \"object\", \"properties\": {\"feeAmount\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"isRequired\": false}, \"feeAmounts\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"isRequired\": true}, \"feeDescription\": {\"type\": \"array\", \"items\": {\"type\": \"string\"}, \"isRequired\": false}}}, \"totalAutos\": {\"type\": \"string\", \"isRequired\": false}, \"triaPremium\": {\"type\": \"string\", \"isRequired\": false}, \"premiumDueDate\": {\"type\": \"string\", \"format\": \"LocalDate\", \"isRequired\": false}, \"commissionDetails\": {\"type\": \"object\", \"properties\": {\"type\": {\"type\": \"string\", \"isRequired\": false}, \"amount\": {\"type\": \"string\", \"isRequired\": false}, \"percentage\": {\"type\": \"string\", \"isRequired\": false}}}, \"totalWrittenPremium\": {\"type\": \"string\", \"isRequired\": false}, \"isoClassificationCode\": {\"type\": \"string\", \"isRequired\": false}, \"totalTechnicalPremium\": {\"type\": \"string\", \"isRequired\": false}, \"soldToTechnicalPercentage\": {\"type\": \"string\", \"isRequired\": false}, \"underWriterDebitCreditFactor\": {\"type\": \"string\", \"isRequired\": false}}}, \"exposureDetails\": {\"type\": \"object\", \"properties\": {\"otherAggregateLimit\": {\"type\": \"string\", \"isRequired\": false}, \"medicalPaymentsLimit\": {\"type\": \"string\", \"isRequired\": false}, \"policyAggregateLimit\": {\"type\": \"string\", \"isRequired\": false}, \"generalAggregateLimit\": {\"type\": \"string\", \"isRequired\": false}, \"damageToPremisesRented\": {\"type\": \"string\", \"isRequired\": false}, \"eachOccurrenceLimitExcess\": {\"type\": \"string\", \"isRequired\": false}, \"eachOccurrenceLimitPrimary\": {\"type\": \"string\", \"isRequired\": false}, \"personalAdvertisingInjuryLimit\": {\"type\": \"string\", \"isRequired\": false}, \"productsCompletedOperationsAggregate\": {\"type\": \"string\", \"isRequired\": false}, \"numberOfGeneralAggregateReinstatements\": {\"type\": \"string\", \"isRequired\": false}, \"productsCompletedOperationsAggregateLimit\": {\"type\": \"string\", \"isRequired\": false}}}, \"transactionDate\": {\"type\": \"string\", \"format\": \"LocalDate\", \"isRequired\": false}, \"transactionType\": {\"enum\": [\"New Business\", \"Renewal\"], \"type\": \"string\", \"isRequired\": true}, \"underwriterName\": {\"type\": \"string\", \"isRequired\": true}, \"scheduleOfValues\": {\"type\": \"string\", \"isRequired\": false}, \"submissionNumber\": {\"type\": \"string\", \"isRequired\": true}, \"underwriterEmail\": {\"type\": \"string\", \"format\": \"email\", \"isRequired\": true}, \"deductibleDetails\": {\"type\": \"object\", \"properties\": {\"sirAmount\": {\"type\": \"string\", \"isRequired\": false}, \"retentionType\": {\"type\": \"string\", \"isRequired\": false}, \"deductibleAmount\": {\"type\": \"string\", \"isRequired\": false}}}, \"priorPolicyNumber\": {\"type\": \"string\", \"isRequired\": false}, \"policyInceptionDate\": {\"type\": \"string\", \"format\": \"LocalDate\", \"isRequired\": true}, \"policyExpirationDate\": {\"type\": \"string\", \"format\": \"LocalDate\", \"isRequired\": true}, \"descriptionOfOperations\": {\"type\": \"string\", \"isRequired\": false}}", "hiddenCells": ["definedStatus", "priorPolicyNumber", "nyftzClass", "nyftzClass", "scheduleOfValues", "exposureDetails.eachOccurrenceLimitPrimary", "exposureDetails.generalAggregateLimit", "exposureDetails.productsCompletedOperationsAggregateLimit", "exposureDetails.personalAdvertisingInjuryLimit", "exposureDetails.damageToPremisesRented", "exposureDetails.medicalPaymentsLimit", "deductibleDetails.retentionType", "deductibleDetails.deductibleAmount", "deductibleDetails.sirAmount", "exposureDetails.numberOfGeneralAggregateReinstatements", "exposureDetails.policyAggregateLimit", "productType"], "lockedCells": ["submissionNumber", "underwriterEmail", "premiumDetails.commissionDetails.amount", "premiumDetails.soldToTechnicalPercentage", "nyftzClass", "totalPremium", "policyNumber"]}