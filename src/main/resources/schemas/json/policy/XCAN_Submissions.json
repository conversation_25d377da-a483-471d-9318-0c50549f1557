[{"clientId": "803517a8-c42a-44c3-b54b-fccafaec1ad3", "templateType": "POLICY_INFO_DETAIL", "data": [{"cells": [{"meta": {"cellConfig": {"col": 6, "style": {}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": "Policy Limits Deductible SIR", "labelTooltip": null}, "isWrapperRequired": true}, "cells": [{"id": "920192-4a76-aa70-85fq-442212", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": [{"key": "policyNumber", "type": "storeCheck", "valueCheck": {"type": "notNull"}, "dataStoreInfo": {"dataStateKey": "policy", "dataStoreModule": "policyCreation"}}]}, "componentConfig": {"label": "Policy Number", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["policyNumber"]}}, "containerConfig": null}, "cells": null, "cellName": "policyNumber", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "mka61effc-4a76-4471-85f5657-a154d018e300", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Each occurrence limit Primary", "async": true, "label": "Each occurrence limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.eachOccurrenceLimitPrimary"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "eachOccurancePrimary", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.eachOccurrenceLimitPrimary", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ea61effc-4a76-4471-85f9-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "General aggregate limit (other than Products/Completed operations)", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.generalAggregateLimit"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "generalAggregateLimit", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.generalAggregateLimit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ea61effc-4a76-4471-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Products/Completed operations aggregate limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.productsCompletedOperationsAggregateLimit"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "productsCompletedOperationsAggregateLimit", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.productsCompletedOperationsAggregateLimit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ea61effc-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Personal and advertising injury limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.personalAdvertisingInjuryLimit"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "personalAndAdvertisingInjuryLimit", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.personalAdvertisingInjuryLimit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ea61effd-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Damage to premises rented to you", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.damageToPremisesRented"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "damageToPermisesRentedToYou", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.damageToPremisesRented", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "da61effd-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Medical payments limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.medicalPaymentsLimit"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "medicalPaymentLimit", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.medicalPaymentsLimit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "da61effd-4a76-aa70-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Each occurrence limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.eachOccurrenceLimitExcess"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "eachOccuranceExcess", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.eachOccurrenceLimitExcess", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "da61effh-4a76-aa70-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Other aggregate limit", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.otherAggregateLimit"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "otherAggregateLimit", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.otherAggregateLimit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "da61effh-4a76-aa70-85fq-a154d018e89vg", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Products-Completed operations aggregate", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.productsCompletedOperationsAggregate"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "productsCompletedOperationsAggregate", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.productsCompletedOperationsAggregate", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "pa61effh-4a76-aa70-85fq-a154d018e89vg", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Policy aggregate limit (auto)", "inputType": "currency", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.policyAggregateLimit"]}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.policyAggregateLimit", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "ba61effd-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Retention type", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["deductibleDetails.retentionType"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "retentionType", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "deductibleDetails.retentionType", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ba61ecfv-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Deductible amount", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["deductibleDetails.deductibleAmount"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "deductibleAmount", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "deductibleDetails.deductibleAmount", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ba61effv-4a76-aa71-85fq-a154d018e899", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "SIR amount", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["deductibleDetails.sirAmount"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "SIR Amount", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "deductibleDetails.sirAmount", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "pa61evfh-4a76-aa70-85fq-a154d018e89vg", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Number of general aggregate reinstatements", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.numberOfGeneralAggregateReinstatements"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "generalAggregateReinstatements", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.numberOfGeneralAggregateReinstatements", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "pa61evfh-4a76-aa70-85fq-bvdfvhkjfdjkn", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Attachment (Total limits including GL)", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["exposureDetails.attachmentTotalLimitsInclGL"]}}, "containerConfig": null}, "cells": null, "cellName": "exposureDetails.attachmentTotalLimitsInclGL", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "submission-container-1", "cellType": "container", "parentId": "804d61c2-0ead-45b2-8fb1-c8ef7b51c364", "component": "TemplateCard"}, {"meta": {"cellConfig": {"col": 6, "style": {}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": "Policy Ratings", "shadow": true}, "isWrapperRequired": true}, "cells": [{"meta": {"cellConfig": {"col": 12, "style": {"borderBottom": "1px solid #CBEDFD"}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": null, "shadow": false, "blockId": "policy-ratings", "variant": "secondary", "navLabel": "exposureBasis", "addNewLabel": "Add", "showAddIcon": true, "labelTooltip": null, "outerPadding": "none", "valueFormatter": {"defaultMap": {"exposureBasis": "", "rateByExposureBasis": "", "exposureBasisDetailed": "", "exposureAmountByExposureBasis": ""}, "nameInSourcedSystemParams": ["rateDetails"]}, "showDeleteModal": true, "uniqueIdentifier": "id", "deleteModalMessage": "Are you sure you want to delete the {_inputLabel_} form the Exposure Basis", "navigationTabVariant": "secondary", "navigationContainerStyles": {"width": "calc(100% + 48px)", "margin-left": "-24px", "padding-right": "20px"}}, "isWrapperRequired": true}, "cells": [{"id": "ea61effc-4a76-4471-85f9-a154d018e305", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Exposure basis", "options": [], "dataType": "list", "asyncType": "rest", "keyToSave": "name", "ajaxSearch": true, "labelOnLeft": false, "placeholder": "Select exposure basis", "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["rateDetails", "exposureBasis"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "exposureBasis", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "rateDetails.exposureBasis", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "ea61effc-4a76-4471-85f9-a15456745674", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Rate by exposure basis", "label": "Rate by exposure basis", "dataType": "list", "inputType": "currency", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["rateDetails", "rateByExposureBasis"]}}, "containerConfig": null}, "cells": null, "cellName": "rateDetails.rateByExposureBasis", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "ea61effc-1231-4471-85f9-a154d018e305", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Exposure basis detailed", "label": "Exposure basis detailed", "dataType": "list", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["rateDetails", "exposureBasisDetailed"]}}, "containerConfig": null}, "cells": null, "cellName": "rateDetails.exposureBasisDetailed", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "etf22yg3v-4a76-4471-85f9-a154d018e305", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Exposure amount by exposure basis", "label": "Exposure amount by exposure basis", "dataType": "list", "inputType": "currency", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["rateDetails", "exposureAmountByExposureBasis"]}}, "containerConfig": null}, "cells": null, "cellName": "rateDetails.exposureAmountByExposureBasis", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "rateDetails", "cellType": "container", "parentId": "34039fd9-bbde-43e5-95f5-0289a85060a1", "component": "TemplateCardWithNavList"}, {"meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": null, "containerConfig": null}, "cells": [{"id": "d9bfdd10-23e5-4cb2-8636-2296d046d17f", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "ISO Classification Code", "label": "ISO Classification Code", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.isoClassificationCode"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.isoClassificationCode", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "isoClassification", "cellType": "container", "parentId": "34039fd9-bbde-43e5-95f5-092384092380", "component": null}, {"id": "585ea99e-8d82-4cd0-bc3a-2079cc8295cf", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Total written premium", "label": "Total written premium", "decimal": 0, "inputType": "currency", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.totalWrittenPremium"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.totalWrittenPremium", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "a4c5b7f5-70d6-487c-8b5a-a61513bda7ce", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "TRIA premium", "label": "TRIA premium", "decimal": 0, "inputType": "currency", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.triaPremium"]}, "isEnableToggleOn": "premiumDetails.isTriaPremiumEnabled", "conditionalDisabled": {"premiumDetails.isTriaPremiumEnabled": false}, "conditionalValidations": {"premiumDetails.isTriaPremiumEnabled": {"value": false, "validations": {"required": false}}}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.triaPremium", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "6614cc09-c367-43f4-b3b1-76ec99ed7b10", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Total number of autos", "label": "Total number of autos", "inputType": "number", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.totalAutos"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.totalAutos", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "50509d17-1340-43e5-8a2b-f9e90ed7a1fd", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Audit", "label": "Audit", "options": [{"id": "Yes", "name": "Yes"}, {"id": "No", "name": "No"}], "labelOnLeft": false, "placeholder": "Select Audit", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.audit"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.audit", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "a78deddb-ee84-4a01-a506-4c090133c512", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Carrier", "async": true, "label": "Carrier", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "placeholder": "Select Carrier", "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["carrier"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "Carrier", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "carrier", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "23874774-6875-4290-a807-c07b1893d630", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Treaty", "async": true, "label": "Treaty", "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.treaty"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "Treaty", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.treaty", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": null, "shadow": false, "blockId": "fee-details", "variant": "secondary", "navLabel": "feeDescription", "baseLabel": "Fee Type", "labelType": "Auto", "addNewLabel": "Add", "showAddIcon": true, "labelTooltip": null, "outerPadding": "none", "valueFormatter": {"defaultMap": {"feeAmount": "", "feeDescription": ""}, "nameInSourcedSystemParams": ["premiumDetails.feeDetails"]}, "showDeleteModal": true, "uniqueIdentifier": "id", "deleteModalMessage": "Are you sure you want to delete the {_inputLabel_} from the Fees", "navigationTabVariant": "secondary"}, "isWrapperRequired": true}, "cells": [{"id": "ea61effc-4a76-4471-85f9-a154d018123423", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Fee description", "options": [], "dataType": "list", "asyncType": "rest", "keyToSave": "name", "ajaxSearch": true, "labelOnLeft": false, "placeholder": "Select", "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.feeDetails", "feeDescription"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "feeDescription", "searchText": ""}, "requestBody": {"metaDataFilters": []}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.feeDetails.feeDescription", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "3ebfc9d0-dc13-4b53-806e-ec10da0d8b20", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Fee amounts", "label": "Fee amounts", "decimal": 0, "dataType": "list", "inputType": "currency", "labelOnLeft": false, "placeholder": "Enter here", "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.feeDetails", "feeAmount"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.feeDetails.feeAmount", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "premiumDetails.feeDetails", "cellType": "container", "parentId": "184f9a52-de94-4ea1-b9a0-27099e1d1e13", "component": "TemplateCardWithNavList"}], "cellName": "submission-container-2", "cellType": "container", "parentId": "e60fa2e3-00fa-444b-9f76-1656c843d9812", "component": "TemplateCard"}, {"id": "broker-commission/underwriter-rating", "meta": {"cellConfig": {"col": 6, "style": {}}, "componentConfig": null, "containerConfig": {"title": null, "shadow": true}}, "cells": [{"meta": {"cellConfig": {"col": 12, "style": {"padding-bottom": "15px"}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": "Broker commission", "shadow": false, "labelTooltip": null, "outerPadding": "none"}, "isWrapperRequired": true}, "cells": [{"id": "921f25d0-583a-9dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Commission %", "decimal": 2, "inputType": "percentage", "labelOnLeft": false, "validations": {"max_value": 100}, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.commissionDetails.percentage"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.commissionDetails.percentage", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "9b1f25d0-583a-4dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Commission $", "disabled": true, "inputType": "currency", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.commissionDetails.amount"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.commissionDetails.amount", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "broker-commision-info", "cellType": "container", "parentId": "7f190df3-87d5-47b4-b8fa-2344234233", "component": "TemplateCard"}, {"meta": {"cellConfig": {"col": 12, "style": {"padding-bottom": "15px"}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": "Underwriter rating", "shadow": false, "labelTooltip": null, "outerPadding": "none"}, "isWrapperRequired": true}, "cells": [{"id": "9b1f77878-583a-4dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Premium return type", "options": [{"id": "Flat Rate", "name": "Flat Rate", "label": "Flat rate"}, {"id": "Short Rate", "name": "Short Rate", "label": "Short rate"}, {"id": "Pro Rata", "name": "Pro Rata", "label": "Pro rata"}], "labelOnLeft": false, "placeholder": "Select premium return type", "validations": {"required": true}, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.premiumReturnType"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.premiumReturnType", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "9b1f785d0-583a-4dfb-a4bf-8877333", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Total technical premium", "decimal": 0, "inputType": "currency", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.totalTechnicalPremium"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.totalTechnicalPremium", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "9b1f26780-583a-4dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Sold to technical %", "disabled": true, "inputType": "number", "labelOnLeft": false, "validations": {"max_value": 100}, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.soldToTechnicalPercentage"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.soldToTechnicalPercentage", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "9b1f23443564-583a-4dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Underwriter debit/credit factor", "inputType": "number", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["premiumDetails.underWriterDebitCreditFactor"]}}, "containerConfig": null}, "cells": null, "cellName": "premiumDetails.underWriterDebitCreditFactor", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "underwriter-rating-container", "cellType": "container", "parentId": "7f190df3-87d5-47b4-lwkem-5daf70393d3e", "component": "TemplateCard"}, {"meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": [{"key": "carrier", "type": "storeCheck", "valueCheck": {"value": "Continental Insurance Company"}, "dataStoreInfo": {"dataStateKey": "policy", "dataStoreModule": "policyCreation"}}]}, "componentConfig": null, "containerConfig": {"title": "NYFTZ", "shadow": false, "labelTooltip": null, "outerPadding": "none"}, "isWrapperRequired": true}, "cells": [{"id": "9b1f785d0-583a-4dfb-a4bf-1a79b80f2d41", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "NYFTZ Class", "disabled": true, "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["nyftzClass"]}}, "containerConfig": null}, "cells": null, "cellName": "nyftzClass", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "nyftzClass-container", "cellType": "container", "parentId": "431112-de94-318718-b9a0-27099e1d1e13", "component": "TemplateCard"}], "cellName": "broker-commission/underwriter-rating", "cellType": "container", "parentId": "e60fa2e3-00fa-444b-9f76-1656c8439862", "component": "TemplateCard"}, {"meta": {"cellConfig": {"col": 6, "style": {}, "visibleCondition": null}, "componentConfig": null, "containerConfig": {"title": "Surplus Lines Information", "labelTooltip": null, "parentContainerId": "surplus_information"}, "isWrapperRequired": false}, "cells": [{"id": "123123-23232-4dfb-a4bf-1423423434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"name": "Filing done by", "label": "Filing done by", "options": [{"label": "Inside broker", "value": "Inside broker"}, {"label": "Outside broker", "value": "Outside broker"}], "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.filingDoneBy"]}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.filingDoneBy", "cellType": "component", "component": "BaseRadioWithLabel"}, {"id": "123123-3123123-4dfb-a4bf-1423423434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Broker name", "labelKey": "name", "asyncType": "rest", "keyToSave": "name", "ajaxSearch": true, "labelOnLeft": false, "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.brokerName"], "otherNameInSourcedSystemParams": [{"key": "surplusLineInfo.surplusBrokerageName", "valueKeyToSave": "brokerageName"}, {"key": "surplusLineInfo.mailingAddress.address", "valueKeyToSave": "addressLine1"}, {"key": "surplusLineInfo.mailingAddress.city", "valueKeyToSave": "city"}, {"key": "surplusLineInfo.mailingAddress.state", "valueKeyToSave": "state"}, {"key": "surplusLineInfo.mailingAddress.zipCode", "valueKeyToSave": "zipCode"}]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/brokerage-service/broker/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchName", "searchPlace": "body"}, "pagination": {"enabled": true, "pageKey": "pageNumber", "sizeKey": "pageSize", "defaultSize": 30, "paginationPlace": "query"}, "queryParams": {}, "requestBody": {"searchName": ""}, "responseStructure": {"dataKey": "data.items", "labelMapper": {"id": "broker.brokerId", "city": "broker.address.city", "name": ["broker.firstName", "broker.last<PERSON>ame"], "label": ["broker.firstName", "broker.last<PERSON>ame", "brokerageName", "broker.office.officeName"], "state": "broker.address.state", "zipCode": "broker.address.zipcode", "senderEmail": "broker.emailAddress", "addressLine1": "broker.address.addressLine1", "brokerageName": "brokerageName"}, "concatString": [" ", " - ", ", "], "totalItemsKey": "data.totalItems"}}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}, "dropdownPlaceholder": "Select Broker"}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.brokerName", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "2121344557g3123-323-4dfb-a4bf-123434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Surplus lines brokerage name", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.surplusBrokerageName"]}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.surplusBrokerageName", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "123123-323-4dfb-a4bf-123434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Surplus lines address", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.mailingAddress.address"]}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.mailingAddress.address", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "31assd-323-4d2ffefb-a4bf-123434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Surplus lines state", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "placeholder": "Select state", "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.mailingAddress.state"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "State", "searchText": ""}, "requestBody": {"metaDataFilters": [{"key": "Country", "value": "US", "mandatory": true}]}, "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.mailingAddress.state", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "31assd-323-4dfb-a4bf-123434", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"async": true, "label": "Surplus lines city", "options": [], "asyncType": "rest", "ajaxSearch": true, "labelOnLeft": false, "placeholder": "Select city", "requireSearch": true, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.mailingAddress.city"]}, "asyncProperties": {"url": "https://dev-api.submission.concirrusquest.com/core/reference-data-service/values/search", "method": "POST", "search": {"enabled": true, "searchKey": "searchText", "searchPlace": "query"}, "pagination": {"enabled": true, "pageKey": "page", "sizeKey": "size", "defaultSize": 20, "paginationPlace": "query"}, "queryParams": {"page": 0, "size": 20, "entityType": "City", "searchText": "", "metaDataFilters|1|value": null}, "requestBody": {"metaDataFilters": [{"key": "Country", "value": "US", "mandatory": true}, {"key": "State", "value": "", "mandatory": true}]}, "mapKeyFromData": [{"mapType": "arrayItem", "mapDataIn": "body", "mapDataTo": "metaDataFilters|1|value", "mapDataFrom": "surplusLineInfo.mailingAddress.state"}], "responseStructure": {"dataKey": "content", "labelMapper": {"id": "dataValue", "name": "dataValue"}, "totalItemsKey": "totalElements"}}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.mailingAddress.city", "cellType": "component", "component": "BaseDropdownWithLabel"}, {"id": "sd32333-322223-4dfb-a4bf-21321", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Surplus lines zipcode", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.mailingAddress.zipCode"]}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.mailingAddress.zipCode", "cellType": "component", "component": "BaseInputWithLabel"}, {"id": "31assd-322223-4dfb-a4bf-21321", "meta": {"cellConfig": {"col": 12, "style": {}, "visibleCondition": null}, "componentConfig": {"label": "Surplus lines licence number", "labelOnLeft": false, "valueFormatter": {"nameInSourcedSystemParams": ["surplusLineInfo.surplusLicenceNumber"]}, "conditionalDisabled": {"surplusLineInfo.filingDoneBy": "Inside broker"}}, "containerConfig": null}, "cells": null, "cellName": "surplusLineInfo.surplusLicenceNumber", "cellType": "component", "component": "BaseInputWithLabel"}], "cellName": "submission-container-4", "cellType": "container", "parentId": "e60fa2e3-00fa-444b-9f76-1656c843224a", "component": "TemplateCard"}], "clientId": "803517a8-c42a-44c3-b54b-fccafaec1ad3"}], "createdAt": **********.209, "updatedAt": **********.962, "deleted": false, "hiddenFields": ["productType", "definedStatus", "insuredInfo.owner", "scheduleOfValues", "insuredInfo.generalContractor", "exposureDetails.policyAggregateLimit", "exposureDetails.eachOccurrenceLimitPrimary", "exposureDetails.generalAggregateLimit", "exposureDetails.productsCompletedOperationsAggregateLimit", "exposureDetails.personalAdvertisingInjuryLimit", "exposureDetails.damageToPremisesRented", "exposureDetails.medicalPaymentsLimit", "deductibleDetails.retentionType", "deductibleDetails.deductibleAmount", "deductibleDetails.sirAmount", "exposureDetails.numberOfGeneralAggregateReinstatements", "nyftzClass", "projectDetails.name", "projectDetails.street", "projectDetails.state", "projectDetails.zipCode", "projectDetails.city"], "state": "REVIEW", "productType": "XCAN", "lockedFields": null}]