package helpers;

import decoders.DecoderManager;
import io.restassured.RestAssured;
import io.restassured.filter.log.LogDetail;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import utils.StringEncrypt;

public class KeycloakLogin {


    public static final Logger logger = LogManager.getLogger(KeycloakLogin.class);
    public static String bearerToken;

    public static String getToken() {
        return bearerToken;
    }

    public void performLogin() {
        logger.info("Performing Keycloak login");
        bearerToken = RestAssured.given().urlEncodingEnabled(true).param("client_id", "ui").
//                param("client_secret", DecoderManager.getDecoderInstance().getKeycloakClientSecret()).
                param("username", PropertiesHelper.getProperty("submission.username")).
                param("password", StringEncrypt.decryptXOR(PropertiesHelper.getProperty("submission.password"))).
                param("grant_type", "password").
                when().post("https://" + DecoderManager.getDecoderInstance().getAuthHost()).
                then().log().ifValidationFails(LogDetail.BODY).statusCode(200).extract().path("access_token");



    }

}
