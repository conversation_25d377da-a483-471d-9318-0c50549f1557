package helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

public class JsonModifier {

    public static void modifyJson(JsonNode jsonNode, String key, String newValue) {
        String[] keys = key.split("\\.");
        JsonNode currentNode = jsonNode;
        for (int i = 0; i < keys.length - 1; i++) {
            currentNode = currentNode.path(keys[i]);
        }
        ((ObjectNode) currentNode).put(keys[keys.length - 1], newValue);
    }
}
