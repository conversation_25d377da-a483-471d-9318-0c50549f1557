package helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import pojo.logistics.PolicySearchRequest;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class PolicySearchHelper {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Creates a valid policy search request with default values
     */
    public static PolicySearchRequest createValidSearchRequest() {
        PolicySearchRequest request = new PolicySearchRequest();
        request.setFreeText("");
        request.setProductCode(Arrays.asList("CAR", "CL", "FFE&O", "FFLL", "IAC", "MTC", "NVOCC"));
        request.setUnderwriters(Collections.emptyList());
        request.setLogisticStatus(Arrays.asList("BOUND", "QUOTED", "REVIEW"));
        return request;
    }
    
    /**
     * Creates a search request with specific product codes
     */
    public static PolicySearchRequest createSearchRequestWithProductCodes(String productCodesString) {
        PolicySearchRequest request = createValidSearchRequest();
        List<String> productCodes = Arrays.asList(productCodesString.split(","));
        // Trim whitespace from each product code
        productCodes.replaceAll(String::trim);
        request.setProductCode(productCodes);
        return request;
    }
    
    /**
     * Creates a search request with specific status filters
     */
    public static PolicySearchRequest createSearchRequestWithStatus(String statusString) {
        PolicySearchRequest request = createValidSearchRequest();
        List<String> statusList = Arrays.asList(statusString.split(","));
        // Trim whitespace from each status
        statusList.replaceAll(String::trim);
        request.setLogisticStatus(statusList);
        return request;
    }
    
    /**
     * Creates a search request with free text search
     */
    public static PolicySearchRequest createSearchRequestWithFreeText(String freeText) {
        PolicySearchRequest request = createValidSearchRequest();
        request.setFreeText(freeText);
        return request;
    }
    
    /**
     * Creates a search request with underwriter filter
     */
    public static PolicySearchRequest createSearchRequestWithUnderwriters(String underwritersString) {
        PolicySearchRequest request = createValidSearchRequest();
        List<String> underwriters = Arrays.asList(underwritersString.split(","));
        // Trim whitespace from each underwriter
        underwriters.replaceAll(String::trim);
        request.setUnderwriters(underwriters);
        return request;
    }
    
    /**
     * Creates an empty search request
     */
    public static PolicySearchRequest createEmptySearchRequest() {
        PolicySearchRequest request = new PolicySearchRequest();
        request.setFreeText("");
        request.setProductCode(Collections.emptyList());
        request.setUnderwriters(Collections.emptyList());
        request.setLogisticStatus(Collections.emptyList());
        return request;
    }
    
    /**
     * Creates a search request with multiple criteria
     */
    public static PolicySearchRequest createSearchRequestWithMultipleCriteria(
            String freeText, String productCodes, String status, String underwriters) {
        PolicySearchRequest request = new PolicySearchRequest();
        
        request.setFreeText(freeText != null ? freeText : "");
        
        if (productCodes != null && !productCodes.isEmpty()) {
            List<String> productCodeList = Arrays.asList(productCodes.split(","));
            productCodeList.replaceAll(String::trim);
            request.setProductCode(productCodeList);
        } else {
            request.setProductCode(Collections.emptyList());
        }
        
        if (status != null && !status.isEmpty()) {
            List<String> statusList = Arrays.asList(status.split(","));
            statusList.replaceAll(String::trim);
            request.setLogisticStatus(statusList);
        } else {
            request.setLogisticStatus(Collections.emptyList());
        }
        
        if (underwriters != null && !underwriters.isEmpty()) {
            List<String> underwritersList = Arrays.asList(underwriters.split(","));
            underwritersList.replaceAll(String::trim);
            request.setUnderwriters(underwritersList);
        } else {
            request.setUnderwriters(Collections.emptyList());
        }
        
        return request;
    }
    
    /**
     * Creates a search request with special characters in free text
     */
    public static PolicySearchRequest createSearchRequestWithSpecialCharacters() {
        PolicySearchRequest request = createValidSearchRequest();
        request.setFreeText("Test@#$%^&*()_+-=[]{}|;':\",./<>?");
        return request;
    }
    
    /**
     * Creates an invalid search request for negative testing
     */
    public static String createInvalidSearchRequest() {
        return "{\n" +
            "  \"invalidField\": \"invalidValue\",\n" +
            "  \"productCode\": \"invalid_format\",\n" +
            "  \"logisticStatus\": 123\n" +
            "}";
    }
    
    /**
     * Converts request object to JSON string
     */
    public static String toJsonString(PolicySearchRequest request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert request to JSON", e);
        }
    }
    
    /**
     * Creates pagination query parameters
     */
    public static java.util.Map<String, String> createPaginationParams(int pageNumber, int pageSize) {
        java.util.Map<String, String> params = new java.util.HashMap<>();
        params.put("pageNumber", String.valueOf(pageNumber));
        params.put("pageSize", String.valueOf(pageSize));
        return params;
    }
    
    /**
     * Creates invalid pagination parameters for negative testing
     */
    public static java.util.Map<String, String> createInvalidPaginationParams() {
        java.util.Map<String, String> params = new java.util.HashMap<>();
        params.put("pageNumber", "-1");
        params.put("pageSize", "0");
        return params;
    }
    
    /**
     * Validates if a product code list contains expected codes
     */
    public static boolean containsProductCodes(List<String> actualCodes, String expectedCodesString) {
        if (actualCodes == null || actualCodes.isEmpty()) {
            return false;
        }
        
        List<String> expectedCodes = Arrays.asList(expectedCodesString.split(","));
        expectedCodes.replaceAll(String::trim);
        
        for (String expectedCode : expectedCodes) {
            if (!actualCodes.contains(expectedCode)) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Validates if a status list contains expected statuses
     */
    public static boolean containsStatuses(List<String> actualStatuses, String expectedStatusesString) {
        if (actualStatuses == null || actualStatuses.isEmpty()) {
            return false;
        }
        
        List<String> expectedStatuses = Arrays.asList(expectedStatusesString.split(","));
        expectedStatuses.replaceAll(String::trim);
        
        for (String expectedStatus : expectedStatuses) {
            if (!actualStatuses.contains(expectedStatus)) {
                return false;
            }
        }
        return true;
    }
}
