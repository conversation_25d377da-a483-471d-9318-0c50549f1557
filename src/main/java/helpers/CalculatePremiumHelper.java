package helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import pojo.logistics.*;
import java.util.Arrays;
import java.util.List;

public class CalculatePremiumHelper {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Creates a valid calculate premium request with default values
     */
    public static CalculatePremiumRequest createValidRequest() {
        CalculatePremiumRequest request = new CalculatePremiumRequest();
        
        // Set basic info
        request.setId(createIdInfo(1747404043L, "2025-05-16T14:00:43.000+00:00"));
        request.setInsuredInfo(createInsuredInfo());
        request.setClassType(Arrays.asList("SI"));
        request.setLob(Arrays.asList("CAR", "WRH"));
        request.setStatus("REVIEW");
        request.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        request.setSubmissionId("458d0928-998f-494f-b665-eaea3b5e1218");
        request.setBusinessType("New Business");
        request.setGfrLast12Months("1000000");
        request.setGfrNext12Months("120000000");
        request.setContainerisedShipments("80");
        request.setMinimumPerCertificate("10");
        request.setRate(0.052);
        request.setLossHistory(createDefaultLossHistory());
        request.setPremiumSummary(createDefaultPremiumSummary());
        // Set broker info
        request.setBrokerInfo(createBrokerInfo());

        return request;
    }
    
    /**
     * Creates a request with specific LOB and class
     */
    public static CalculatePremiumRequest createRequestWithLobAndClass(String lobString, String classType) {
        CalculatePremiumRequest request = createValidRequest();
        
        // Parse LOB string
        List<String> lobList = Arrays.asList(lobString.split(","));
        for (int i = 0; i < lobList.size(); i++) {
            lobList.set(i, lobList.get(i).trim());
        }
        
        request.setLob(lobList);
        request.setClassType(Arrays.asList(classType));
        
        return request;
    }
    
    /**
     * Creates an invalid request for negative testing
     */
    public static String createInvalidRequest() {
        return "{\n" +
            "  \"invalidField\": \"invalidValue\",\n" +
            "  \"missingRequiredFields\": true\n" +
            "}";
    }
    
    /**
     * Converts request object to JSON string
     */
    public static String toJsonString(CalculatePremiumRequest request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert request to JSON", e);
        }
    }
    
    /**
     * Creates ID info object
     */
    private static CalculatePremiumRequest.IdInfo createIdInfo(Long timestamp, String date) {
        CalculatePremiumRequest.IdInfo idInfo = new CalculatePremiumRequest.IdInfo();
        idInfo.setTimestamp(timestamp);
        idInfo.setDate(date);
        return idInfo;
    }
    
    /**
     * Creates insured info object with default values
     */
    private static CalculatePremiumRequest.InsuredInfo createInsuredInfo() {
        CalculatePremiumRequest.InsuredInfo insuredInfo = new CalculatePremiumRequest.InsuredInfo();
        insuredInfo.setId(createIdInfo(1747404042L, "2025-05-16T14:00:42.000+00:00"));
        insuredInfo.setName("");
        insuredInfo.setBusinessName("Test Business");
        insuredInfo.setInsuredId("eeb7d55b-6549-476c-a264-290b4819ed29");
        insuredInfo.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        insuredInfo.setState("AZ");
        insuredInfo.setCompanyAddress("10825 Old Mill Road, Omaha, NE 68154");
        insuredInfo.setCompanyName("Continental Indemnity Company");
        insuredInfo.setEffectiveDate("2025-05-01");
        insuredInfo.setExpiryDate("2025-05-31");
        return insuredInfo;
    }
    
    /**
     * Creates broker info object with default values
     */
    private static CalculatePremiumRequest.BrokerInfo createBrokerInfo() {
        CalculatePremiumRequest.BrokerInfo brokerInfo = new CalculatePremiumRequest.BrokerInfo();
        brokerInfo.setBrokerContact("***********");
        brokerInfo.setBrokerOfRecord("Emet International Insurance LLC");
        brokerInfo.setAccountManager("Oliver Long");
        brokerInfo.setCommission("17");
        brokerInfo.setCarrier("Beazly");
        return brokerInfo;
    }
    
    /**
     * Creates commodity types list with default values
     */
    public static List<CommodityType> createDefaultCommodityTypes() {
        return Arrays.asList(
            new CommodityType("General Merchandise", "10", "generalMerchandise"),
            new CommodityType("Electronic Merchandise", "", "electronicMerchandise"),
            new CommodityType("Goods Under Refrigeration", "", "goodsUnderRefrigeration")
        );
    }
    
    /**
     * Creates geographical areas list with default values
     */
    public static List<GeographicalArea> createDefaultGeographicalAreas() {
        return Arrays.asList(
            new GeographicalArea("US & Canada", "", "e3b41d0c-4909-493f-906b-466d3858605c"),
            new GeographicalArea("Europe", "", "fdf0f10e-c835-473a-b0ab-fc764c8a362a"),
            new GeographicalArea("Asia", "", "2182e51a-cbd5-469f-ad32-39eb24c6d62e")
        );
    }
    
    /**
     * Creates loss history object with default values
     */
    public static LossHistory createDefaultLossHistory() {
        LossHistory lossHistory = new LossHistory();
        lossHistory.setTotalRaterPremium("0");
        lossHistory.setRaterPremiumLossPick("0.00");
        lossHistory.setLossAdjustedPremiumLossPick("0.00");
        
        // Create loss details
        LossHistory.LossDetail lossDetail = new LossHistory.LossDetail();
        lossDetail.setLossAdjustedPremium(0.0);
        lossDetail.setLossPremium(0.0);
        lossDetail.setLossDebitCredit("0");
        
        lossHistory.setShipperInterestCargo(lossDetail);
        lossHistory.setShipperInterestWarehouse(lossDetail);
        lossHistory.setCargoLiabilities(lossDetail);
        lossHistory.setWarehouseLegalLiability(lossDetail);
        lossHistory.setExcessWarehouseOperatorLegalLiability(lossDetail);
        
        lossHistory.setLossHistoryLastFiveYearLosses(Arrays.asList("0", "0", "0", "0", "0"));
        lossHistory.setTotalLossHistoryLastFiveYearLosses("0");
        lossHistory.setAverageLossHistoryLastFiveYearLosses("0");
        
        return lossHistory;
    }
    
    /**
     * Creates premium summary object with default values
     */
    public static PremiumSummary createDefaultPremiumSummary() {
        PremiumSummary premiumSummary = new PremiumSummary();
        premiumSummary.setWarehouseLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setCargoLiabilitiesPremiumSummary(0.0);
        premiumSummary.setTriaPremium("0");
        premiumSummary.setShipperInterestPremiumSummary(0.0);
        premiumSummary.setTotalPremium(0.0);
        premiumSummary.setPartnerNetPremium(0.0);
        premiumSummary.setExcessWarehouseOperatorLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setBlueHavenUWCommission(0.0);
        premiumSummary.setBrokerCommission(0.0);
        return premiumSummary;
    }
}
