package helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import pojo.logistics.*;
import java.util.Arrays;
import java.util.List;

public class CalculatePremiumHelper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Creates a valid calculate premium request with default values
     */
    public static CalculatePremiumRequest createValidRequest() {
        CalculatePremiumRequest request = new CalculatePremiumRequest();

        // Set basic info
        request.setId(createIdInfo(1747404043L, "2025-05-16T14:00:43.000+00:00"));
        request.setInsuredInfo(createInsuredInfo());
        request.setClassType(Arrays.asList("SI"));
        request.setLob(Arrays.asList("CAR", "WRH"));
        request.setStatus("REVIEW");
        request.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        request.setSubmissionId("e5c5b825-e07a-48df-9a38-ac26371e4512");
        request.setBusinessType("New Business");
        request.setGfrLast12Months("1000000");
        request.setGfrNext12Months("120000000");
        request.setContainerisedShipments("80");
        request.setMinimumPerCertificate("10");
        request.setRate(0.052);
        request.setPremium(62890.0);
        request.setLossHistory(createDefaultLossHistory());
        request.setPremiumSummary(createDefaultPremiumSummary());

        // Set broker info
        request.setBrokerInfo(createBrokerInfo());

        // Set receipts and shipper interest fields
        setReceiptsAndShipperInterestFields(request);

        return request;
    }

    /**
     * Creates a request with specific LOB and class
     */
    public static CalculatePremiumRequest createRequestWithLobAndClass(String lobString, String classType) {
        CalculatePremiumRequest request = createValidRequest();

        // Parse LOB string
        List<String> lobList = Arrays.asList(lobString.split(","));
        for (int i = 0; i < lobList.size(); i++) {
            lobList.set(i, lobList.get(i).trim());
        }

        request.setLob(lobList);
        request.setClassType(Arrays.asList(classType));

        return request;
    }

    /**
     * Creates an invalid request for negative testing
     */
    public static String createInvalidRequest() {
        return "{\n" +
            "  \"invalidField\": \"invalidValue\",\n" +
            "  \"missingRequiredFields\": true\n" +
            "}";
    }

    /**
     * Converts request object to JSON string
     */
    public static String toJsonString(CalculatePremiumRequest request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert request to JSON", e);
        }
    }

    /**
     * Creates ID info object
     */
    private static CalculatePremiumRequest.IdInfo createIdInfo(Long timestamp, String date) {
        CalculatePremiumRequest.IdInfo idInfo = new CalculatePremiumRequest.IdInfo();
        idInfo.setTimestamp(timestamp);
        idInfo.setDate(date);
        return idInfo;
    }

    /**
     * Creates insured info object with default values
     */
    private static CalculatePremiumRequest.InsuredInfo createInsuredInfo() {
        CalculatePremiumRequest.InsuredInfo insuredInfo = new CalculatePremiumRequest.InsuredInfo();
        insuredInfo.setId(createIdInfo(1749582447L, "2025-06-10T19:07:27.000+00:00"));
        insuredInfo.setName("");
        insuredInfo.setBusinessName("rates test");
        insuredInfo.setInsuredId("73ca4fa3-cc64-4d46-9a38-8daba1d46d14");
        insuredInfo.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        insuredInfo.setInsuredNumber(233);
        insuredInfo.setIsDeleted(false);
        insuredInfo.setCreatedAt("2025-06-10T19:07:27.433189695");
        insuredInfo.setUpdatedAt("2025-06-10T19:23:16.053+00:00");
        insuredInfo.setInsuredClientId("L22232");
        insuredInfo.setAddress("US mumbai");
        insuredInfo.setCity("Abbeville");
        insuredInfo.setEffectiveDate("2025-06-01");
        insuredInfo.setExpiryDate("2026-06-30");
        insuredInfo.setNoOfEmployees("19239");
        insuredInfo.setRiskManager("shailja");
        insuredInfo.setState("AZ");
        insuredInfo.setYearsOnExperience("Apex Insurance Services");
        insuredInfo.setZipcode("223344");
        insuredInfo.setCompanyAddress("10825 Old Mill Road, Omaha, NE 68154");
        insuredInfo.setCompanyName("Continental Indemnity Company");
        return insuredInfo;
    }

    /**
     * Creates broker info object with default values
     */
    private static CalculatePremiumRequest.BrokerInfo createBrokerInfo() {
        CalculatePremiumRequest.BrokerInfo brokerInfo = new CalculatePremiumRequest.BrokerInfo();
        brokerInfo.setBrokerContact("");
        brokerInfo.setBrokerOfRecord("Emet International Insurance LLC");
        brokerInfo.setAccountManager("Oliver Long");
        brokerInfo.setCommission("12");
        brokerInfo.setCarrier("Beazly");
        return brokerInfo;
    }

    /**
     * Creates commodity types list with default values
     */
    public static List<CommodityType> createDefaultCommodityTypes() {
        return Arrays.asList(
            new CommodityType("General Merchandise", "10", "generalMerchandise"),
            new CommodityType("Electronic Merchandise", "", "electronicMerchandise"),
            new CommodityType("Goods Under Refrigeration", "", "goodsUnderRefrigeration")
        );
    }

    /**
     * Creates geographical areas list with default values
     */
    public static List<GeographicalArea> createDefaultGeographicalAreas() {
        return Arrays.asList(
            new GeographicalArea("US & Canada", "", "e3b41d0c-4909-493f-906b-466d3858605c"),
            new GeographicalArea("Europe", "", "fdf0f10e-c835-473a-b0ab-fc764c8a362a"),
            new GeographicalArea("Asia", "", "2182e51a-cbd5-469f-ad32-39eb24c6d62e")
        );
    }

    /**
     * Creates loss history object with default values
     */
    public static LossHistory createDefaultLossHistory() {
        LossHistory lossHistory = new LossHistory();
        lossHistory.setTotalRaterPremium("0");
        lossHistory.setRaterPremiumLossPick("0.00");
        lossHistory.setLossAdjustedPremiumLossPick("0.00");

        // Create loss details
        LossHistory.LossDetail lossDetail = new LossHistory.LossDetail();
        lossDetail.setLossAdjustedPremium(0.0);
        lossDetail.setLossPremium(0.0);
        lossDetail.setLossDebitCredit("0");

        lossHistory.setShipperInterestCargo(lossDetail);
        lossHistory.setShipperInterestWarehouse(lossDetail);
        lossHistory.setCargoLiabilities(lossDetail);
        lossHistory.setWarehouseLegalLiability(lossDetail);
        lossHistory.setExcessWarehouseOperatorLegalLiability(lossDetail);

        lossHistory.setLossHistoryLastFiveYearLosses(Arrays.asList("0", "0", "0", "0", "0"));
        lossHistory.setTotalLossHistoryLastFiveYearLosses("0");
        lossHistory.setAverageLossHistoryLastFiveYearLosses("0");

        return lossHistory;
    }

    /**
     * Creates premium summary object with default values
     */
    public static PremiumSummary createDefaultPremiumSummary() {
        PremiumSummary premiumSummary = new PremiumSummary();
        premiumSummary.setWarehouseLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setCargoLiabilitiesPremiumSummary(0.0);
        premiumSummary.setTriaPremium("0");
        premiumSummary.setShipperInterestPremiumSummary(0.0);
        premiumSummary.setTotalPremium(0.0);
        premiumSummary.setPartnerNetPremium(0.0);
        premiumSummary.setExcessWarehouseOperatorLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setBlueHavenUWCommission(0.0);
        premiumSummary.setBrokerCommission(0.0);
        return premiumSummary;
    }

    /**
     * Sets receipts and shipper interest fields with default values
     */
    private static void setReceiptsAndShipperInterestFields(CalculatePremiumRequest request) {
        // Set receipts amounts and percentages
        request.setReceiptsAsBrokerAgentAmount("3000000");
        request.setReceiptsAsBrokerAgentPercentage("12");
        request.setReceiptsAsIndirectAirCarrierAmount("3000000");
        request.setReceiptsAsIndirectAirCarrierPercentage("12");
        request.setReceiptsAsMotorTruckCarrierAmount("3000000");
        request.setReceiptsAsMotorTruckCarrierPercentage("12");
        request.setReceiptsAsNVOCCAmount("3000000");
        request.setReceiptsAsNVOCCPercentage("12");
        request.setReceiptsAsWarehouseOperatorAmount("3000000");
        request.setReceiptsAsWarehouseOperatorPercentage("12");

        // Set shipper interest fields
        request.setShipperInterestAdjustmentPeriod("Monthly");
        request.setShipperInterestAdjustmentRate("As per Rate Matrix");
        request.setShipperInterestCargoInlandDeposit("$0");
        request.setShipperInterestWarehouseStorageDeposit("$0");

        // Set additional fields for premium calculation
        setAdditionalCalculationFields(request);
    }

    /**
     * Sets additional fields required for premium calculation
     */
    private static void setAdditionalCalculationFields(CalculatePremiumRequest request) {
        // Set timestamps
        String currentTime = "2025-06-10T19:07:27.416+00:00";
        request.setCreatedAt(currentTime);
        request.setUpdatedAt("2025-06-10T19:23:16.263+00:00");
        request.setCreatedBy("e52cf520-4e44-4103-b9bb-a6248d187bcf");
        request.setUpdatedBy("e52cf520-4e44-4103-b9bb-a6248d187bcf");

        // Set proposal information
        request.setProposalDate("06-10-2025");
        request.setProposalNumber("001_L22232");
        request.setUnderwriterName("SHAILJA KOUNDAL");

        // Set premium calculation fields
        request.setCargoLiabilityAjustmentRate("");
        request.setCargoLiabilityPremium(28470.0);
        request.setWarehouseFlatAnnualPremium(0.0);
        request.setWhllPremium(4608.0);
        request.setWllAjustmentRate("");
        request.setXwllAjustmentRate("");
        request.setXxwhllPremium(0.0);

        // Set sub LOB ID
        request.setSubLobId(null);

        // Set additional insured info
        java.util.Map<String, Object> additionalInsured = new java.util.HashMap<>();
        additionalInsured.put("id", "5d83c9ba-f806-41bc-9934-37547b8b1a7a");
        additionalInsured.put("city", "");
        additionalInsured.put("name", "");
        additionalInsured.put("state", "");
        additionalInsured.put("address", "");
        additionalInsured.put("zipcode", "");
        request.setAdditionalInsuredInfo(java.util.Arrays.asList(additionalInsured));

        // Set premium calculation structure
        request.setPremiumCalculation(createPremiumCalculationStructure());

        // Set other required structures
        request.setGeographicalScope(createGeographicalScopeStructure());
        request.setGoodsInsured(createGoodsInsuredStructure());
        request.setOceanCargoInlandTransit(createOceanCargoStructure());
        request.setQuoteStructure(createQuoteStructure());
        request.setShipperInterestSpecifiedCommodities(createShipperInterestSpecifiedCommodities());
        request.setTermsAndConditions(createTermsAndConditionsStructure());
        request.setValuation(createValuationStructure());
    }

    /**
     * Creates a comprehensive request with all fields including receipts and shipper interest
     */
    public static CalculatePremiumRequest createComprehensiveRequest() {
        CalculatePremiumRequest request = createValidRequest();

        // Set additional fields for comprehensive testing
        request.setCommodityTypes(createDefaultCommodityTypes());
        request.setGeographicalArea(createDefaultGeographicalAreas());

        return request;
    }

    /**
     * Creates a request with custom receipts values
     */
    public static CalculatePremiumRequest createRequestWithCustomReceipts(
            String amount, String percentage) {
        CalculatePremiumRequest request = createValidRequest();

        // Set custom receipts values for all types
        request.setReceiptsAsBrokerAgentAmount(amount);
        request.setReceiptsAsBrokerAgentPercentage(percentage);
        request.setReceiptsAsIndirectAirCarrierAmount(amount);
        request.setReceiptsAsIndirectAirCarrierPercentage(percentage);
        request.setReceiptsAsMotorTruckCarrierAmount(amount);
        request.setReceiptsAsMotorTruckCarrierPercentage(percentage);
        request.setReceiptsAsNVOCCAmount(amount);
        request.setReceiptsAsNVOCCPercentage(percentage);
        request.setReceiptsAsWarehouseOperatorAmount(amount);
        request.setReceiptsAsWarehouseOperatorPercentage(percentage);

        return request;
    }

    /**
     * Creates premium calculation structure based on curl data
     */
    private static java.util.Map<String, Object> createPremiumCalculationStructure() {
        java.util.Map<String, Object> premiumCalculation = new java.util.HashMap<>();

        // Shipper Interest Cargo
        java.util.Map<String, Object> shipperInterestCargo = new java.util.HashMap<>();
        premiumCalculation.put("shipperInterestCargo", shipperInterestCargo);

        // Cargo Liabilities
        java.util.Map<String, Object> cargoLiabilities = new java.util.HashMap<>();
        cargoLiabilities.put("adjustedPremium", "28470");
        cargoLiabilities.put("debitCredit", "0");
        premiumCalculation.put("cargoLiabilities", cargoLiabilities);

        // Warehouse Legal Liability
        java.util.Map<String, Object> warehouseLegalLiability = new java.util.HashMap<>();
        warehouseLegalLiability.put("adjustedPremium", "4608");
        warehouseLegalLiability.put("debitCredit", "0");
        premiumCalculation.put("warehouseLegalLiability", warehouseLegalLiability);

        // Excess Warehouse Operator Legal Liability
        java.util.Map<String, Object> excessWarehouseOperatorLegalLiability = new java.util.HashMap<>();
        excessWarehouseOperatorLegalLiability.put("adjustedPremium", "3282");
        excessWarehouseOperatorLegalLiability.put("debitCredit", "0");
        premiumCalculation.put("excessWarehouseOperatorLegalLiability", excessWarehouseOperatorLegalLiability);

        return premiumCalculation;
    }

    /**
     * Creates geographical scope structure
     */
    private static java.util.Map<String, Object> createGeographicalScopeStructure() {
        java.util.Map<String, Object> geographicalScope = new java.util.HashMap<>();
        geographicalScope.put("title", "2. Geographical Scope");
        geographicalScope.put("statement", "From ports and/or places anywhere in the World to ports and/or places anywhere in the World including all domestic and/or internal transits as required.");
        geographicalScope.put("statement2", "Excluding absolutely any exposure in the following countries: Afghanistan, Angola, Belarus, Burundi, Central African Republic (CAR), Congo Democratic Republic (Kinshasa), Cote d'Ivoire (Ivory Coast), Crimea, Cuba, Eritrea, Ethiopia, Gaza (strip), Haiti, Iran, Iraq, Kyrgyzstan, Lebanon, Liberia, Libya, Mali, Myanmar (Burma), Nigeria, North Korea, Russia, Rwanda, Sierra Leone, Somalia, South Sudan, Sudan, Syria, Tajikistan, Turkmenistan, Ukraine, Uzbekistan, Venezuela, Yemen and Zimbabwe.");
        geographicalScope.put("statement3", "Also excluding territories in violation of Economic Trades Sanctions, and any other country where legislation decrees insurance must be affected locally");
        return geographicalScope;
    }

    /**
     * Creates goods insured structure
     */
    private static java.util.Map<String, Object> createGoodsInsuredStructure() {
        java.util.Map<String, Object> goodsInsured = new java.util.HashMap<>();
        goodsInsured.put("title", "1. Goods Insured");
        goodsInsured.put("statement", "Upon lawful goods and/or merchandise suitably packed for transit, consisting principally of, but not limited to, commodities as declared by the Insured and approved by the Company as per the rate schedule herein. Referable and excluded commodities as set forth in the Commodity Index unless otherwise indicated herein.");
        return goodsInsured;
    }

    /**
     * Creates ocean cargo inland transit structure
     */
    private static java.util.Map<String, Object> createOceanCargoStructure() {
        java.util.Map<String, Object> oceanCargo = new java.util.HashMap<>();
        oceanCargo.put("additionalWording", "Where a different Limit per specific commodity is shown under Commodity Terms & Conditions Section A (5), such limits shall take precedence above those included under this section. Shipments by metal barge require approval from Insurers prior to attachment.");
        return oceanCargo;
    }

    /**
     * Creates quote structure
     */
    private static java.util.Map<String, Object> createQuoteStructure() {
        java.util.Map<String, Object> quoteStructure = new java.util.HashMap<>();
        quoteStructure.put("premium", 0);
        quoteStructure.put("option", "Monthly reporting rate matrix");
        return quoteStructure;
    }

    /**
     * Creates shipper interest specified commodities structure
     */
    private static java.util.Map<String, Object> createShipperInterestSpecifiedCommodities() {
        java.util.Map<String, Object> shipperInterest = new java.util.HashMap<>();
        shipperInterest.put("additionalWording", "Limits for specific commodities as shown herein shall supersede any limits as shown on the Warehouse Storage section above.");
        return shipperInterest;
    }

    /**
     * Creates terms and conditions structure
     */
    private static java.util.Map<String, Object> createTermsAndConditionsStructure() {
        java.util.Map<String, Object> termsAndConditions = new java.util.HashMap<>();

        // CL terms
        java.util.List<java.util.Map<String, Object>> clTerms = new java.util.ArrayList<>();
        clTerms.add(createTerm("Carriers Legal Liability Policy Form", "c17cc8af-79b8-4ab2-8e82-681cbbd10ea9"));
        clTerms.add(createTerm("Freight Forwarders Legal Liability Policy Form", "f40914d7-8c63-4936-976f-4ab8b7752d94"));
        clTerms.add(createTerm("Freight Forwarders Errors & Omissions Policy Form", "6d0aeb2f-d7d4-47b3-ae1d-fefdd18e49e8"));
        termsAndConditions.put("CL", clTerms);

        // SI terms
        java.util.List<java.util.Map<String, Object>> siTerms = new java.util.ArrayList<>();
        siTerms.add(createTerm("Ocean Cargo Policy Form", "181d4791-b172-4461-80ed-9995fe2cced5"));
        siTerms.add(createTerm("Inland Transit Coverage Form", "ae418e62-bb94-48fe-9d64-b26075151561"));
        termsAndConditions.put("SI", siTerms);

        // General terms
        java.util.List<java.util.Map<String, Object>> generalTerms = new java.util.ArrayList<>();
        generalTerms.add(createTerm("Economic and Trade Sanctions Endorsement", "d459a8de-5808-4085-a7b5-834beced8f85"));
        generalTerms.add(createTerm("Marine Cyber Exclusions", "ce89a8a3-a627-4136-aec1-fe0dc15b91bb"));
        termsAndConditions.put("general", generalTerms);

        return termsAndConditions;
    }

    /**
     * Helper method to create term object
     */
    private static java.util.Map<String, Object> createTerm(String value, String id) {
        java.util.Map<String, Object> term = new java.util.HashMap<>();
        term.put("value", value);
        term.put("id", id);
        return term;
    }

    /**
     * Creates valuation structure
     */
    private static java.util.Map<String, Object> createValuationStructure() {
        java.util.Map<String, Object> valuation = new java.util.HashMap<>();
        valuation.put("title", "3. Valuation");
        valuation.put("subTitle1", "CIF + 10% :");
        valuation.put("subTitle2", "Used Goods :");
        valuation.put("statement", "All Goods Insured, except those specifically provided for elsewhere herein, to be valued as follows:");
        valuation.put("statement2", "Valued, premium included, at amount of invoice, including all lawful and usual charges in the invoice, and including prepaid and/or advanced and/or guaranteed freight, if any, plus 10%.");
        valuation.put("statement3", "Goods and machinery insured under this policy shall be valued at the actual cost to the Insured to replace same with an item of like kind and quality, but if not replaced, then (i) at the actual cash value of the item as determined by an independent surveyor appointed by the Company or (ii) at the book value of the item as maintained in the accounting records of the Insured, whichever is higher.");
        return valuation;
    }
}
