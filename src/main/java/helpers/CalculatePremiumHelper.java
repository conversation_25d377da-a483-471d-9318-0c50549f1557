package helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import pojo.logistics.*;
import java.util.Arrays;
import java.util.List;

public class CalculatePremiumHelper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Creates a valid calculate premium request with default values
     */
    public static CalculatePremiumRequest createValidRequest() {
        CalculatePremiumRequest request = new CalculatePremiumRequest();

        // Set basic info
        request.setId(createIdInfo(1747404043L, "2025-05-16T14:00:43.000+00:00"));
        request.setInsuredInfo(createInsuredInfo());
        request.setClassType(Arrays.asList("SI"));
        request.setLob(Arrays.asList("CAR", "WRH"));
        request.setStatus("REVIEW");
        request.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        request.setSubmissionId("e5c5b825-e07a-48df-9a38-ac26371e4512");
        request.setBusinessType("New Business");
        request.setGfrLast12Months("1000000");
        request.setGfrNext12Months("120000000");
        request.setContainerisedShipments("80");
        request.setMinimumPerCertificate("10");
        request.setRate(0.052);
        request.setPremium(62890.0);
        request.setLossHistory(createDefaultLossHistory());
        request.setPremiumSummary(createDefaultPremiumSummary());

        // Set broker info
        request.setBrokerInfo(createBrokerInfo());

        // Set receipts and shipper interest fields
        setReceiptsAndShipperInterestFields(request);

        return request;
    }

    /**
     * Creates a request with specific LOB and class
     */
    public static CalculatePremiumRequest createRequestWithLobAndClass(String lobString, String classType) {
        CalculatePremiumRequest request = createValidRequest();

        // Parse LOB string
        List<String> lobList = Arrays.asList(lobString.split(","));
        for (int i = 0; i < lobList.size(); i++) {
            lobList.set(i, lobList.get(i).trim());
        }

        request.setLob(lobList);
        request.setClassType(Arrays.asList(classType));

        return request;
    }

    /**
     * Creates an invalid request for negative testing
     */
    public static String createInvalidRequest() {
        return "{\n" +
            "  \"invalidField\": \"invalidValue\",\n" +
            "  \"missingRequiredFields\": true\n" +
            "}";
    }

    /**
     * Converts request object to JSON string
     */
    public static String toJsonString(CalculatePremiumRequest request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert request to JSON", e);
        }
    }

    /**
     * Creates ID info object
     */
    private static CalculatePremiumRequest.IdInfo createIdInfo(Long timestamp, String date) {
        CalculatePremiumRequest.IdInfo idInfo = new CalculatePremiumRequest.IdInfo();
        idInfo.setTimestamp(timestamp);
        idInfo.setDate(date);
        return idInfo;
    }

    /**
     * Creates insured info object with default values
     */
    private static CalculatePremiumRequest.InsuredInfo createInsuredInfo() {
        CalculatePremiumRequest.InsuredInfo insuredInfo = new CalculatePremiumRequest.InsuredInfo();
        insuredInfo.setId(createIdInfo(1747404042L, "2025-05-16T14:00:42.000+00:00"));
        insuredInfo.setName("");
        insuredInfo.setBusinessName("Test Business");
        insuredInfo.setInsuredId("eeb7d55b-6549-476c-a264-290b4819ed29");
        insuredInfo.setClientId("e6cb5a81-0822-4b20-86db-13e3934eaaea");
        insuredInfo.setState("AZ");
        insuredInfo.setCompanyAddress("10825 Old Mill Road, Omaha, NE 68154");
        insuredInfo.setCompanyName("Continental Indemnity Company");
        insuredInfo.setEffectiveDate("2025-05-01");
        insuredInfo.setExpiryDate("2025-05-31");
        return insuredInfo;
    }

    /**
     * Creates broker info object with default values
     */
    private static CalculatePremiumRequest.BrokerInfo createBrokerInfo() {
        CalculatePremiumRequest.BrokerInfo brokerInfo = new CalculatePremiumRequest.BrokerInfo();
        brokerInfo.setBrokerContact("***********");
        brokerInfo.setBrokerOfRecord("Emet International Insurance LLC");
        brokerInfo.setAccountManager("Oliver Long");
        brokerInfo.setCommission("17");
        brokerInfo.setCarrier("Beazly");
        return brokerInfo;
    }

    /**
     * Creates commodity types list with default values
     */
    public static List<CommodityType> createDefaultCommodityTypes() {
        return Arrays.asList(
            new CommodityType("General Merchandise", "10", "generalMerchandise"),
            new CommodityType("Electronic Merchandise", "", "electronicMerchandise"),
            new CommodityType("Goods Under Refrigeration", "", "goodsUnderRefrigeration")
        );
    }

    /**
     * Creates geographical areas list with default values
     */
    public static List<GeographicalArea> createDefaultGeographicalAreas() {
        return Arrays.asList(
            new GeographicalArea("US & Canada", "", "e3b41d0c-4909-493f-906b-466d3858605c"),
            new GeographicalArea("Europe", "", "fdf0f10e-c835-473a-b0ab-fc764c8a362a"),
            new GeographicalArea("Asia", "", "2182e51a-cbd5-469f-ad32-39eb24c6d62e")
        );
    }

    /**
     * Creates loss history object with default values
     */
    public static LossHistory createDefaultLossHistory() {
        LossHistory lossHistory = new LossHistory();
        lossHistory.setTotalRaterPremium("0");
        lossHistory.setRaterPremiumLossPick("0.00");
        lossHistory.setLossAdjustedPremiumLossPick("0.00");

        // Create loss details
        LossHistory.LossDetail lossDetail = new LossHistory.LossDetail();
        lossDetail.setLossAdjustedPremium(0.0);
        lossDetail.setLossPremium(0.0);
        lossDetail.setLossDebitCredit("0");

        lossHistory.setShipperInterestCargo(lossDetail);
        lossHistory.setShipperInterestWarehouse(lossDetail);
        lossHistory.setCargoLiabilities(lossDetail);
        lossHistory.setWarehouseLegalLiability(lossDetail);
        lossHistory.setExcessWarehouseOperatorLegalLiability(lossDetail);

        lossHistory.setLossHistoryLastFiveYearLosses(Arrays.asList("0", "0", "0", "0", "0"));
        lossHistory.setTotalLossHistoryLastFiveYearLosses("0");
        lossHistory.setAverageLossHistoryLastFiveYearLosses("0");

        return lossHistory;
    }

    /**
     * Creates premium summary object with default values
     */
    public static PremiumSummary createDefaultPremiumSummary() {
        PremiumSummary premiumSummary = new PremiumSummary();
        premiumSummary.setWarehouseLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setCargoLiabilitiesPremiumSummary(0.0);
        premiumSummary.setTriaPremium("0");
        premiumSummary.setShipperInterestPremiumSummary(0.0);
        premiumSummary.setTotalPremium(0.0);
        premiumSummary.setPartnerNetPremium(0.0);
        premiumSummary.setExcessWarehouseOperatorLegalLiabilityPremiumSummary(0.0);
        premiumSummary.setBlueHavenUWCommission(0.0);
        premiumSummary.setBrokerCommission(0.0);
        return premiumSummary;
    }

    /**
     * Sets receipts and shipper interest fields with default values
     */
    private static void setReceiptsAndShipperInterestFields(CalculatePremiumRequest request) {
        // Set receipts amounts and percentages
        request.setReceiptsAsBrokerAgentAmount("3000000");
        request.setReceiptsAsBrokerAgentPercentage("12");
        request.setReceiptsAsIndirectAirCarrierAmount("3000000");
        request.setReceiptsAsIndirectAirCarrierPercentage("12");
        request.setReceiptsAsMotorTruckCarrierAmount("3000000");
        request.setReceiptsAsMotorTruckCarrierPercentage("12");
        request.setReceiptsAsNVOCCAmount("3000000");
        request.setReceiptsAsNVOCCPercentage("12");
        request.setReceiptsAsWarehouseOperatorAmount("3000000");
        request.setReceiptsAsWarehouseOperatorPercentage("12");

        // Set shipper interest fields
        request.setShipperInterestAdjustmentPeriod("Monthly");
        request.setShipperInterestAdjustmentRate("As per Rate Matrix");
        request.setShipperInterestCargoInlandDeposit("$0");
        request.setShipperInterestWarehouseStorageDeposit("$0");

        // Set additional fields for premium calculation
        setAdditionalCalculationFields(request);
    }

    /**
     * Sets additional fields required for premium calculation
     */
    private static void setAdditionalCalculationFields(CalculatePremiumRequest request) {
        // Set timestamps
        String currentTime = "2025-06-10T19:07:27.416+00:00";
        request.setCreatedAt(currentTime);
        request.setUpdatedAt("2025-06-10T19:23:16.263+00:00");
        request.setCreatedBy("e52cf520-4e44-4103-b9bb-a6248d187bcf");
        request.setUpdatedBy("e52cf520-4e44-4103-b9bb-a6248d187bcf");

        // Set proposal information
        request.setProposalDate("06-10-2025");
        request.setProposalNumber("001_L22232");
        request.setUnderwriterName("SHAILJA KOUNDAL");

        // Set premium calculation fields
        request.setCargoLiabilityAjustmentRate("");
        request.setCargoLiabilityPremium(28470.0);
        request.setWarehouseFlatAnnualPremium(0.0);
        request.setWhllPremium(4608.0);
        request.setWllAjustmentRate("");
        request.setXwllAjustmentRate("");
        request.setXxwhllPremium(0.0);

        // Set sub LOB ID
        request.setSubLobId(null);

        // Set additional insured info
        java.util.Map<String, Object> additionalInsured = new java.util.HashMap<>();
        additionalInsured.put("id", "5d83c9ba-f806-41bc-9934-37547b8b1a7a");
        additionalInsured.put("city", "");
        additionalInsured.put("name", "");
        additionalInsured.put("state", "");
        additionalInsured.put("address", "");
        additionalInsured.put("zipcode", "");
        request.setAdditionalInsuredInfo(java.util.Arrays.asList(additionalInsured));
    }

    /**
     * Creates a comprehensive request with all fields including receipts and shipper interest
     */
    public static CalculatePremiumRequest createComprehensiveRequest() {
        CalculatePremiumRequest request = createValidRequest();

        // Set additional fields for comprehensive testing
        request.setCommodityTypes(createDefaultCommodityTypes());
        request.setGeographicalArea(createDefaultGeographicalAreas());

        return request;
    }

    /**
     * Creates a request with custom receipts values
     */
    public static CalculatePremiumRequest createRequestWithCustomReceipts(
            String amount, String percentage) {
        CalculatePremiumRequest request = createValidRequest();

        // Set custom receipts values for all types
        request.setReceiptsAsBrokerAgentAmount(amount);
        request.setReceiptsAsBrokerAgentPercentage(percentage);
        request.setReceiptsAsIndirectAirCarrierAmount(amount);
        request.setReceiptsAsIndirectAirCarrierPercentage(percentage);
        request.setReceiptsAsMotorTruckCarrierAmount(amount);
        request.setReceiptsAsMotorTruckCarrierPercentage(percentage);
        request.setReceiptsAsNVOCCAmount(amount);
        request.setReceiptsAsNVOCCPercentage(percentage);
        request.setReceiptsAsWarehouseOperatorAmount(amount);
        request.setReceiptsAsWarehouseOperatorPercentage(percentage);

        return request;
    }
}
