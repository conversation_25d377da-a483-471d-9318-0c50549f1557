package helpers;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class PropertiesHelper {

    private static final Logger log = LogManager.getLogger(PropertiesHelper.class);

    private static final Properties properties = new Properties();
    private static final Properties properties2 = new Properties();


    public PropertiesHelper() {
        loadProperties();
    }

    public static void setProperty(String propertyName, String value){
        properties.setProperty(propertyName, value);
    }
    public static void setMailProperty(String propertyName, String value){
        properties2.setProperty(propertyName, value);
    }

    public static String getProperty(String propertyName) {
        return properties.getProperty(propertyName);
    }


    public static String getMailProperty(String propertyName) {
        return properties2.getProperty(propertyName);
    }
    /**
     * Loads the properties from an stream
     */
    public void loadProperties() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream inputStream = classLoader.getResourceAsStream("config.properties");
        InputStream inputStream2 = classLoader.getResourceAsStream("emailSettings.properties");

        try {
            properties.load(inputStream);
            properties2.load(inputStream2);

            inputStream.close();
            inputStream2.close();

        } catch (IOException e) {
            log.error("Error reading the properties input stream", e);
        }
    }

}