package helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import pojo.logistics.PolicySubmissionRequest;
import java.util.Arrays;
import java.util.List;

public class PolicySubmissionHelper {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Creates a valid policy submission request with default values
     */
    public static PolicySubmissionRequest createValidSubmissionRequest() {
        PolicySubmissionRequest request = new PolicySubmissionRequest();
        
        // Create insured info
        PolicySubmissionRequest.InsuredInfo insuredInfo = new PolicySubmissionRequest.InsuredInfo();
        insuredInfo.setName("");
        insuredInfo.setBusinessName("Test Business Automation");
        
        request.setInsuredInfo(insuredInfo);
        request.setClassType(Arrays.asList("SI"));
        request.setLob(Arrays.asList("CAR"));
        request.setStatus("REVIEW");
        
        return request;
    }
    
    /**
     * Creates a submission request with specific LOB and class
     */
    public static PolicySubmissionRequest createSubmissionRequestWithLobAndClass(String lobString, String classString) {
        PolicySubmissionRequest request = createValidSubmissionRequest();
        
        // Parse LOB string
        List<String> lobList = Arrays.asList(lobString.split(","));
        lobList.replaceAll(String::trim);
        
        // Parse class string
        List<String> classList = Arrays.asList(classString.split(","));
        classList.replaceAll(String::trim);
        
        request.setLob(lobList);
        request.setClassType(classList);
        
        return request;
    }
    
    /**
     * Creates a submission request with custom business name
     */
    public static PolicySubmissionRequest createSubmissionRequestWithBusinessName(String businessName) {
        PolicySubmissionRequest request = createValidSubmissionRequest();
        request.getInsuredInfo().setBusinessName(businessName);
        return request;
    }
    
    /**
     * Creates a submission request with custom status
     */
    public static PolicySubmissionRequest createSubmissionRequestWithStatus(String status) {
        PolicySubmissionRequest request = createValidSubmissionRequest();
        request.setStatus(status);
        return request;
    }
    
    /**
     * Creates an invalid submission request for negative testing
     */
    public static String createInvalidSubmissionRequest() {
        return "{\n" +
            "  \"invalidField\": \"invalidValue\",\n" +
            "  \"missingRequiredFields\": true\n" +
            "}";
    }
    
    /**
     * Creates a submission request with missing required fields
     */
    public static String createSubmissionRequestWithMissingFields() {
        return "{\n" +
            "  \"insuredInfo\": {\n" +
            "    \"name\": \"Test Name\"\n" +
            "  }\n" +
            "}";
    }
    
    /**
     * Converts request object to JSON string
     */
    public static String toJsonString(PolicySubmissionRequest request) {
        try {
            return objectMapper.writeValueAsString(request);
        } catch (Exception e) {
            throw new RuntimeException("Failed to convert request to JSON", e);
        }
    }
    
    /**
     * Creates a comprehensive submission request with all fields
     */
    public static PolicySubmissionRequest createComprehensiveSubmissionRequest() {
        PolicySubmissionRequest request = new PolicySubmissionRequest();
        
        // Create detailed insured info
        PolicySubmissionRequest.InsuredInfo insuredInfo = new PolicySubmissionRequest.InsuredInfo();
        insuredInfo.setName("John Doe");
        insuredInfo.setBusinessName("Comprehensive Test Business Ltd");
        
        request.setInsuredInfo(insuredInfo);
        request.setClassType(Arrays.asList("SI", "CL"));
        request.setLob(Arrays.asList("CAR", "WRH"));
        request.setStatus("REVIEW");
        
        return request;
    }
    
    /**
     * Creates submission request for specific test scenarios
     */
    public static PolicySubmissionRequest createSubmissionRequestForScenario(String scenario) {
        switch (scenario.toLowerCase()) {
            case "car_only":
                return createSubmissionRequestWithLobAndClass("CAR", "SI");
            case "warehouse_only":
                return createSubmissionRequestWithLobAndClass("WRH", "SI");
            case "multi_lob":
                return createSubmissionRequestWithLobAndClass("CAR,WRH", "SI,CL");
            case "cl_class":
                return createSubmissionRequestWithLobAndClass("CAR", "CL");
            case "comprehensive":
                return createComprehensiveSubmissionRequest();
            default:
                return createValidSubmissionRequest();
        }
    }
    
    /**
     * Validates if a submission request has required fields
     */
    public static boolean isValidSubmissionRequest(PolicySubmissionRequest request) {
        if (request == null) return false;
        if (request.getInsuredInfo() == null) return false;
        if (request.getInsuredInfo().getBusinessName() == null || 
            request.getInsuredInfo().getBusinessName().isEmpty()) return false;
        if (request.getClassType() == null || request.getClassType().isEmpty()) return false;
        if (request.getLob() == null || request.getLob().isEmpty()) return false;
        if (request.getStatus() == null || request.getStatus().isEmpty()) return false;
        
        return true;
    }
    
    /**
     * Creates a simple JSON string for basic testing
     */
    public static String createSimpleSubmissionJson() {
        return "{\n" +
            "  \"insuredInfo\": {\n" +
            "    \"name\": \"\",\n" +
            "    \"businessName\": \"Simple Test Business\"\n" +
            "  },\n" +
            "  \"class\": [\"SI\"],\n" +
            "  \"lob\": [\"CAR\"],\n" +
            "  \"status\": \"REVIEW\"\n" +
            "}";
    }
    
    /**
     * Creates a JSON string with specific LOB and class
     */
    public static String createSubmissionJsonWithLobAndClass(String lob, String classType) {
        String[] lobArray = lob.split(",");
        StringBuilder lobJson = new StringBuilder("[");
        for (int i = 0; i < lobArray.length; i++) {
            lobJson.append("\"").append(lobArray[i].trim()).append("\"");
            if (i < lobArray.length - 1) {
                lobJson.append(", ");
            }
        }
        lobJson.append("]");
        
        String[] classArray = classType.split(",");
        StringBuilder classJson = new StringBuilder("[");
        for (int i = 0; i < classArray.length; i++) {
            classJson.append("\"").append(classArray[i].trim()).append("\"");
            if (i < classArray.length - 1) {
                classJson.append(", ");
            }
        }
        classJson.append("]");
        
        return "{\n" +
            "  \"insuredInfo\": {\n" +
            "    \"name\": \"\",\n" +
            "    \"businessName\": \"Test Business Automation\"\n" +
            "  },\n" +
            "  \"class\": " + classJson + ",\n" +
            "  \"lob\": " + lobJson + ",\n" +
            "  \"status\": \"REVIEW\"\n" +
            "}";
    }
}
