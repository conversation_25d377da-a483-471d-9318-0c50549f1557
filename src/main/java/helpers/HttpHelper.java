package helpers;

import decoders.DecoderManager;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Map;

import static io.restassured.RestAssured.given;

public class HttpHelper {

    private final String apiServerHost = "https://" + DecoderManager.getDecoderInstance().getApiHost();
    private RequestSpecification requestSpecification;
    private static final Logger log = LogManager.getLogger(HttpHelper.class);
    private static Boolean AUTH_REQUIRED=true;


    public HttpHelper(Boolean isAuthRequired) {
        System.out.println("Token "+KeycloakLogin.getToken());
        PropertiesHelper propertiesHelper = new PropertiesHelper();

        if (AUTH_REQUIRED.equals(isAuthRequired)) {
            requestSpecification = given()
                    .relaxedHTTPSValidation()
                    .log().uri()
                    .header("authorization",
                            KeycloakLogin.getToken()
                    ).
                            header("client-id",PropertiesHelper.getProperty("submission.client.id"));
//
        }            if (AUTH_REQUIRED.equals(!isAuthRequired)) {
            requestSpecification = given()
                    .relaxedHTTPSValidation()
                    .log().uri().
                            header("client-id", PropertiesHelper.getProperty("client.id"));
        }

        }

    public Response requestPost(String endpoint, String requestPayload) throws Throwable {
        log.info(endpoint+" ***********    "+requestPayload);
        System.out.println("request body "+endpoint+" ***********    "+requestPayload);
        log.info("requestSpecification:- "+" ***********    "+requestSpecification);
        return requestSpecification.contentType(ContentType.JSON).body(requestPayload)
                .when().post(apiServerHost + endpoint);
    }
    public Response requestPost(String endpoint) throws Throwable {
        log.info("requestSpecification:- "+" ***********    "+requestSpecification);
        return requestSpecification.contentType(ContentType.URLENC)
                .when().post(apiServerHost + endpoint);
    }

    public Response requestGet(String endpoint,Map<String, String> queryParams) throws Throwable {
        System.out.println("Get end point" + endpoint+requestSpecification.toString());
        return requestSpecification.contentType(ContentType.JSON).queryParams(queryParams).when().get(apiServerHost + endpoint);

    }


    public Response requestGet(String endpoint) throws Throwable {
        System.out.println("Get end point"+endpoint);
        return requestSpecification.contentType(ContentType.JSON).when().get(apiServerHost + endpoint);
    }

    public Response requestDelete(String endpoint) throws Throwable {
        log.info("Get end point"+endpoint);
        return requestSpecification.contentType(ContentType.JSON).when().delete(apiServerHost + endpoint);
    }


    public Response requestPut(String endpoint,String requestPayload) throws Throwable {
        log.info("requestSpecification:- "+" ***********    "+requestPayload);
        return requestSpecification.contentType(ContentType.JSON).body(requestPayload)
                .when().put(apiServerHost + endpoint);

    }
    public Response requestGraphQLPost(String endpoint, Object requestPayload) throws Throwable {
        System.out.println(endpoint+" ***********  requestPayload  "+requestPayload.toString());
        System.out.println(endpoint+" ***********    "+requestPayload.toString());
        System.out.println("requestSpecification:- "+" ***********    "+requestSpecification);
        //        System.out.println( "requestSpecification--  "+requestSpecification );
//        return (Response) requestSpecification;
        return requestSpecification.given().log().all().
                contentType(ContentType.JSON).
                body(requestPayload).
                when().
                post(apiServerHost + endpoint);

    }
    public Response  postWithQueryParams(String endpoint, Map<String, String> queryParams) throws Throwable {

        System.out.println("requestSpecification:- "+" ***********    "+requestSpecification);
        //        System.out.println( "requestSpecification--  "+requestSpecification );
//        return (Response) requestSpecification;
        return requestSpecification.given().log().all().
                contentType(ContentType.JSON).queryParams(queryParams).
                when().
                post(apiServerHost + endpoint);

    }

    public Response  putWithQueryParams(String endpoint, Map<String, String> queryParams) throws Throwable {

        System.out.println("requestSpecification:- "+" ***********    "+requestSpecification);

        return requestSpecification.given().log().all().
                contentType(ContentType.JSON).queryParams(queryParams).
                when().
                put(apiServerHost + endpoint);

    }

    public Response requestDelete(String endpoint,String requestPayload) throws Throwable {
        log.info("Delete endpoint "+endpoint);
        log.info("requestSpecification:- "+" ***********    "+requestSpecification);
        return requestSpecification.contentType(ContentType.JSON).when().delete(apiServerHost + endpoint);
    }


}
