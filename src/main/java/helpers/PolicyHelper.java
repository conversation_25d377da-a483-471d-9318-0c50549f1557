package helpers;

import com.fasterxml.jackson.databind.JsonNode;
import com.github.javafaker.Faker;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import pojo.brokerageHubService.BrokerData;
import pojo.brokerageHubService.SearchBrokerInput;
import pojo.graphql.BrokerHubsQueries;
import pojo.graphql.GraphQLQuery;
import utils.Constant;
import utils.Utils;

import java.io.IOException;
import java.util.*;

public class PolicyHelper extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(PolicyHelper.class);
//    private static HttpHelper httpHelper;
    //    public Response response;
//    Constant constant=new Constant();
     BrokerData brokerData =new BrokerData();
    BrokerData.Address address =new BrokerData.Address();
//     GraphQLQuery mutation =new GraphQLQuery();
     String submissionID, blockedBySubmissionID,firstName, referID,brokerageName, brokerageID,team,officeID,brokerID,brokerName,email,city,zip,state,policyId,policyNumber,policyStatus,invoiceId,endorsementId;
   String  address2;
    String insuredid = "";
     PropertiesHelper propertiesHelper=new PropertiesHelper();
     Boolean  isBlocked;
    private  Faker faker = new Faker();

    public  <T> T   selectRandomValue(List<T> list) {
        Random random = new Random();
        int randomIndex = random.nextInt(list.size());
        return list.get(randomIndex);
    }
    public  void getBrokerageBrokerAndOffice() throws Throwable {
        httpResponse = httpHelper.requestGraphQLPost("/core/brokerage-service/broker/search?pageNumber=0&pageSize=30", BrokerHubsQueries.SEARCH_BROKERAGE);
        System.out.println("Brokearge name"+httpResponse.prettyPrint());
        /// TO-do
        List<String> brokeragename= httpResponse.getBody().jsonPath().getList("data.items.brokerageName");
        List<String> brokerageid=httpResponse.getBody().jsonPath().getList("data.items.brokerageId");
        brokerageName=selectRandomValue(brokeragename);
        String request="{\"searchName\":\"a\"}";
        httpResponse= httpHelper.requestGraphQLPost("/core/brokerage-service/broker/search?pageNumber=0&pageSize=30", request);

        List<String> brokername =httpResponse.getBody().jsonPath().getList("data.items.broker.firstName");
        System.out.println("brokername"+" "+httpResponse.getBody().jsonPath().getString("data.items[1].broker.firstName"));
        System.out.println("brokername"+" "+httpResponse.getBody().prettyPrint());

        brokerageID = httpResponse.getBody().jsonPath().getString("data.items[0].broker.brokerageId");
        officeID = httpResponse.getBody().jsonPath().getString("data.items[0].broker.officeId");
        brokerID = httpResponse.getBody().jsonPath().getString("data.items[0].broker.brokerId");
        brokerageName = httpResponse.getBody().jsonPath().getString("data.items[0].brokerageName");
        email = httpResponse.getBody().jsonPath().getString("data.items[0].broker.emailAddress");
        address2 = httpResponse.getBody().jsonPath().getString("data.items[0].broker.address.addressLine1");
        city = httpResponse.getBody().jsonPath().getString("data.items[0].broker.address.city");
        state = httpResponse.getBody().jsonPath().getString("data.items[0].broker.address.state");
        zip = httpResponse.getBody().jsonPath().getString("data.items[0].broker.address.zipCode");
        team = httpResponse.getBody().jsonPath().getString("data.items[0].broker.team");
        brokerName = httpResponse.getBody().jsonPath().getString("data.items[0].broker.firstName") + " " + httpResponse.getBody().jsonPath().getString("data.items[0].broker.lastName");
   }


    public  void createSubmission() throws Throwable {
        firstName="API AUTOMATIONS TEST "+faker.name().fullName();

        String submissionPayload = "{\n" +
                "    \"submissionId\": \"\",\n" +
                "    \"submissionNumber\": \""+Utils.generateRandomNumbers(6)+"\",\n" +
                "    \"status\": \"INCOMPLETE\",\n" +
                "    \"state\": \"INBOX\",\n" +
                "    \"comment\": null,\n" +
                "    \"clientId\": \"803517a8-c42a-44c3-b54b-fccafaec1ad3\",\n" +
                "    \"sourceId\": null,\n" +
                "    \"referenceId\": null,\n" +
                "    \"fleetSize\": 0,\n" +
                "    \"type\": \"CONSTRUCTION\",\n" +
                "    \"productName\": \"Excess Liability - Annual Construction\",\n" +
                "    \"receivedDate\": \"2024-06-07T12:02:38.273426Z\",\n" +
                "    \"updatedAt\": \"2024-06-10T16:19:03.173371Z\",\n" +
                "    \"blockedBySubmissionId\": null,\n" +
                "    \"isBlocked\": false,\n" +
                "    \"transactionType\": \"New Business\",\n" +
                "    \"productType\": \"XANN\",\n" +
                "    \"policyStatus\": \"Submitted\",\n" +
                "    \"definedStatus\": \"Active\",\n" +
                "    \"carrier\": \"Texas Insurance Company\",\n" +
                "    \"policyInceptionDate\": \"2023-01-06\",\n" +
                "    \"policyExpirationDate\": \"2024-01-06\",\n" +
                "    \"underwriterName\": \"backend auto\",\n" +
                "    \"underwriterEmail\": \"<EMAIL>\",\n" +
                "    \"transactionDate\": \"2024-06-07\",\n" +
                "    \"priorPolicyNumber\": \"N/A\",\n" +
                "    \"policyCurrency\": \"USD\",\n" +
                "    \"policyTerm\": null,\n" +
                "    \"policyNumber\": null,\n" +
                "    \"agencyName\": \"Innolabs\",\n" +
                "    \"insuredName\": \"API AUTOMATION TESTs\",\n" +
                "    \"duplicatedBy\": \"b08767fd-8e50-40db-b9b2-9e9b50ff6881\",\n" +
                "    \"brokerInfo\": {\n" +
                "  \"id\": \""+brokerID+"\",\n" +
                "  \"name\": \""+brokerageName+"\",\n" +
                "  \"team\": \"Broker Team\",\n" +
                "  \"senderName\": \""+brokerName+"\",\n" +
                "  \"senderEmail\": \""+email+"\",\n" +
                "  \"mailingAddress\": {\n" +
                "    \"street\": \""+address2+"\",\n" +
                "    \"city\": \""+city+"\",\n" +
                "    \"state\": \""+state+"\",\n" +
                "    \"zipCode\": \""+zip+"\"\n" +
                "  }\n" +
                "},\n" +
                "    \"insuredInfo\": {\n" +
                "        \"id\": \"\",\n" +
                "        \"firstName\": \""+firstName+"\",\n" +
                "        \"dba\": null,\n" +
                "        \"owner\": \""+faker.name().fullName()+"\",\n" +
                "        \"generalContractor\": \""+faker.name().fullName()+"\",\n" +
                "        \"otherNamedInsureds\": [\""+faker.name().fullName()+"\"],\n" +
                "        \"mailingAddress\": {\n" +
                "            \"street1\": \"Street "+faker.address().streetName()+"\",\n" +
                "            \"street2\": \""+faker.address().buildingNumber()+"\",\n" +
                "            \"city\": \""+faker.address().city()+"\",\n" +
                "            \"state\": \""+faker.address().state()+"\",\n" +
                "            \"zipCode\": \""+Utils.generateRandomNumbers(8)+"\"\n" +
                "        }\n" +
                "    },\n" +
                "    \"projectDetails\": [\n" +
                "        {\n" +
                "            \"id\": \"\",\n" +
                "            \"name\": \"API AUTOMATIONS "+faker.name().fullName()+"\",\n" +
                "            \"street\": \""+faker.address().buildingNumber()+"\",\n" +
                "            \"city\": \""+faker.address().city()+"\",\n" +
                "            \"state\": \""+faker.address().state()+"\",\n" +
                "            \"zipCode\": \""+faker.address().zipCode()+"\",\n" +
                "            \"description\": null,\n" +
                "            \"productType\": null\n" +
                "        }\n" +
                "    ],\n" +
                "    \"descriptionOfOperations\": \"Complete rehab of property within last 4 years. New roof, added security, fence around property\",\n" +
                "    \"exposureDetails\": {\n" +
                "        \"eachOccurrenceLimitPrimary\": \"100000\",\n" +
                "        \"eachOccurrenceLimitExcess\": null,\n" +
                "        \"generalAggregateLimit\": null,\n" +
                "        \"productsCompletedOperationsAggregateLimit\": null,\n" +
                "        \"personalAdvertisingInjuryLimit\": null,\n" +
                "        \"damageToPremisesRented\": null,\n" +
                "        \"medicalPaymentsLimit\": null,\n" +
                "        \"otherAggregateLimit\": null,\n" +
                "        \"productsCompletedOperationsAggregate\": null,\n" +
                "        \"policyAggregateLimit\": null,\n" +
                "        \"numberOfGeneralAggregateReinstatement\": 3\n" +
                "    },\n" +
                "    \"deductibleDetails\": {\n" +
                "        \"retentionType\": null,\n" +
                "        \"deductibleAmount\": \"1000\",\n" +
                "        \"sirAmount\": \"1000\"\n" +
                "    },\n" +
                "    \"rateDetails\": [\n" +
                "        {\n" +
                "            \"rateByExposureBasis\": 10000,\n" +
                "            \"exposureBasis\": \"10000\",\n" +
                "            \"exposureBasisDetailed\": \"100000\",\n" +
                "            \"exposureAmountByExposureBasis\": \"10000\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"premiumDetails\": {\"isoClassificationCode\": \"90000\",\n" +
                "            \"feeDescription\": null,\n" +
                "            \"feeDetails\": [\n" +
                "                {\n" +
                "                    \"feeDescription\": \"Engineering Fee\",\n" +
                "                    \"feeAmount\": \"100\"\n" +
                "                }\n" +
                "            ],\n" +
                "            \"totalTechnicalPremium\": \"1\",\n" +
                "            \"premiumDueDate\": null,\n" +
                "            \"totalWrittenPremium\": \"122222\",\n" +
                "            \"underWriterDebitCreditFactor\": 212,\n" +
                "            \"soldToTechnicalPercentage\": \"0\",\n" +
                "            \"triaPremium\": \"4888.88\",\n" +
                "            \"totalAutos\": null,\n" +
                "            \"audit\": \"Yes\",\n" +
                "            \"commissionDetails\": {\n" +
                "                \"type\": null,\n" +
                "                \"percentage\": 12,\n" +
                "                \"amount\": \"14666.64\"\n" +
                "            },\n" +
                "            \"totalFees\": \"10000\",\n" +
                "            \"isTriaPremiumEnabled\": true" +
                "    },\n" +
                "    \"nyftzClass\": \"Class 2-13000\",\n" +
                "    \"scheduleOfValues\": 123,\n" +
                "    \"riskState\": [\n" +
                "        \"FL\"\n" +
                "    ],\n" +
                "    \"softBlocked\": null,\n" +
                "    \"counterBorReceived\": null,\n" +
                "    \"tupdated\": \"2024-06-10T16:25:42.771568Z\",\n" +
                "    \"deleted\": false,\n" +
                "    \"tcreated\": \"2024-06-07T12:03:04.300749Z\",\n" +
                "    \"blockedSubmissions\": [],\n" +
                "    \"unblockSubmissionRequest\": null,\n" +
                "    \"lossInfo\": []\n" +
                "}";
        httpResponse=httpHelper.requestPost("/workflow-service/api/v1/submission/construction",submissionPayload);
        System.out.println("create submission API "+httpResponse.prettyPeek());

        //if(httpResponse.statusCode()==200){
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("stateList", Arrays.asList("RECEIVED", "NOT_CLEARED", "REVIEW", "QUOTED", "BOUND", "ISSUED", "ENDORSEMENT", "DECLINED", "LOST", "CLOSED", "NOT_RESERVED"));
        requestBody.put("type", "CONSTRUCTION");

    httpResponse=httpHelper.requestGraphQLPost("/sub/submission-handler-service/api/v1/submissions/search?page=0&size=20&sortOrder=desc&sortBy=receivedDate",requestBody);
    if(httpResponse.statusCode()==200){
        System.out.println("Search API "+httpResponse.getBody().prettyPrint());
        submissionID=httpResponse.getBody().path("result[0].submissionId");
    isBlocked =httpResponse.getBody().path("result[0].isBlocked");
    if(isBlocked) {
        blockedBySubmissionID = httpResponse.getBody().path("result[0].blockedBySubmissionId");
    }

//    }
}

    }

    public  void validateSubmissionIsBlocked() throws Throwable {
        Map<String, String> queryParams4 = Map.of(
                "type", "CONSTRUCTION"
        );
        httpResponse=httpHelper.postWithQueryParams("/submission-handler-service/api/v1/submissions/"+submissionID+"/unblock/blocking/requests", queryParams4);
//        if(httpResponse.statusCode()==200){
            System.out.println("unblock response"+httpResponse.prettyPeek());
            referID=httpResponse.body().path("result.requestId");
        System.out.println("referID"+referID);
//referID="641bd540-6045-4683-8bb2-bc45f1daf301";
        Map<String, String> queryParams24 = Map.of(
                    "submissionStatus", "APPROVED",
                    "comment", "",
                    "type", "CONSTRUCTION"
            );

        httpResponse=httpHelper.putWithQueryParams("/submission-handler-service/api/v1/submissions/blocking/requests/changeStatus/"+referID,queryParams24);
//    }
//        System.out.println("approve unblock request "+httpResponse.prettyPeek());
        System.out.println("approve unblock request"+httpResponse.body().prettyPrint());


    }

    public  void validateassignSubmissions() throws Throwable {
//        if(httpResponse.statusCode()==200) {
            Map<String, String> queryParams2 = Map.of(
                    "userId", "6aa760cc-4cec-464d-b2dc-8d11a97f1cac"
            );
            httpResponse = httpHelper.postWithQueryParams("/submission-handler-service/api/v1/submissions/" + submissionID + "/assign-user", queryParams2);
            System.out.println("assign user"+httpResponse.body().prettyPrint());

//        }
}
    public  void validateFoldersInQuote() throws Throwable {
        HttpHelper helper=new HttpHelper(true);
        httpResponse=helper.requestGet("/core/document-service/folders?submissionId="+submissionID);
        System.out.println("folder structure"+httpResponse.prettyPrint());
  List<String> foldersName =httpResponse.getBody().jsonPath().getList("data.folderName");
        List<String> foldersNameExpected =new ArrayList<>();
        foldersNameExpected.add("Additional_Correspondences");
        foldersNameExpected.add("Original_Submission");
        foldersNameExpected.add("Invoice");

        List<String> documentNumber =httpResponse.getBody().jsonPath().getList("data.documentCount");
        int items =httpResponse.getBody().jsonPath().getInt("totalItems");
//Assert.assertTrue(new HashSet<>(foldersName).containsAll(foldersNameExpected));

    }
    public  void vvalidateFoldersInForce() throws Throwable {
    }

        public  void validateMoveSubmissionToInforce(String productName, String productState, String productType) throws Throwable {
        getBrokerageBrokerAndOffice();
            createSubmission();
       Thread.sleep(10000);
//       validateSubmissionIsBlocked();
       validateassignSubmissions();
      httpResponse=httpHelper.requestPost("/submission-handler-service/api/v1/submissions/"+submissionID+"/change-state-status","{\"submissionStatus\":\"UW_REVIEW\",\"submissionState\":\"UW_REVIEW\",\"type\":\"CONSTRUCTION\"}");
        System.out.println("chabge status"+httpResponse.prettyPeek());

        Assert.assertEquals(httpResponse.statusCode(),200);
        Thread.sleep(7000);
        httpResponse=httpHelper.requestPost("/submission-handler-service/api/v1/submissions/"+submissionID+"/change-state-status","{\"submissionStatus\":\"QUOTED\",\"submissionState\":\"QUOTED\",\"type\":\"CONSTRUCTION\"}");
        System.out.println("change status"+httpResponse.prettyPeek());
//            validateFoldersInQuote();
        Assert.assertEquals(httpResponse.statusCode(),200);
            Thread.sleep(10000);
            createPolicy(productName, productState, productType);
//            validateFoldersInQuote();
//            validateEndorsementCreation("MONETARY");
//            validateEndorsementCreation("NON_MONETARY");
//            validateEndorsementCreation("CANCEL_REWRITE");
//            validateEndorsementCreation("CANCEL");

        }


    public  void createPolicy(String productName, String productState, String productType) throws Throwable {
        HttpHelper helper = new HttpHelper(true);
        httpResponse = helper.requestGet("/core/policy-service/submissions/" + submissionID + "?fetchSubmission=false");
        if (httpResponse.statusCode() == 200) {
insuredid=httpResponse.getBody().jsonPath().getString("data.insuredId");

            System.out.println("create submission API " + httpResponse.prettyPeek());
            try {
                // Step 1: Read JSON from file
                JsonNode jsonNode = JsonFileReader.readJsonFromFile("src/main/resources/backendPayload/brokerageHubService/createPolicy.json");
//                "": "Excess Liability - Annual Construction",
//                        "productState": "Excess",
//                        "productType": "XANN",

                JsonModifier.modifyJson(jsonNode, "productName", productName);
                JsonModifier.modifyJson(jsonNode, "productState", productState);
                JsonModifier.modifyJson(jsonNode, "productType", productType);
                JsonModifier.modifyJson(jsonNode, "officeId", officeID);
                JsonModifier.modifyJson(jsonNode, "submissionId", submissionID);
                JsonModifier.modifyJson(jsonNode, "brokerageId", brokerageID);
                JsonModifier.modifyJson(jsonNode, "brokerId", brokerID);
                JsonModifier.modifyJson(jsonNode, "brokerInfo.id", firstName);
                JsonModifier.modifyJson(jsonNode, "brokerInfo.name", brokerName);
                JsonModifier.modifyJson(jsonNode, "brokerInfo.senderEmail", email);
//                JsonModifier.modifyJson(jsonNode, "clientId", PropertiesHelper.getProperty("submission.client.id"));
                JsonModifier.modifyJson(jsonNode, "insuredId", insuredid);
                JsonModifier.modifyJson(jsonNode, "brokerInfo.id", brokerID);
                // Step 3: Send HTTP request with modified JSON payload
                httpResponse = httpHelper.requestPost("/core/policy-service/policies", String.valueOf(jsonNode));
                // Step 4: Print response
                System.out.println("Response: " + httpResponse.prettyPrint());

                policyId=httpResponse.getBody().jsonPath().getString("data.policyId");
                policyNumber=httpResponse.getBody().jsonPath().getString("data.policyNumber");
                invoiceId=httpResponse.getBody().jsonPath().getString("data.invoiceId");
                policyNumber=httpResponse.getBody().jsonPath().getString("data.policyNumber");



            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        else {
         Assert.fail("Submission creation failed in core");
        }
    }
    public  void validateInvoiceIsGenerated() throws Throwable {

    }

    public  void validateEndorsementCreation(String EndorsementType ) throws Throwable {
String transactionDescription = "",endorsementType = "",endorsementSequence = "",endorsementNumber = "";

          HttpHelper httpHelper2 = new HttpHelper(true);
JsonNode jsonNode = JsonFileReader.readJsonFromFile("src/main/resources/backendPayload/brokerageHubService/endorsement.json");

        if(EndorsementType.equalsIgnoreCase("MONETARY"))
        {
            transactionDescription="Monetray";
            endorsementType="MONETARY";
            endorsementSequence="1";
            endorsementNumber="1";
        }else if (EndorsementType.equalsIgnoreCase("NON_MONETARY")){
            transactionDescription="NON_MONETARY";
            endorsementType="NON_MONETARY";
            endorsementSequence="2";
            endorsementNumber="2";
        }else if (EndorsementType.equalsIgnoreCase("CANCEL")){
            transactionDescription="CANCEL";
            endorsementType="CANCEL";
            endorsementSequence="3";
            endorsementNumber="3";
            JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.premiumDetails.premiumReturnType", "Short Rate");

        }else if (EndorsementType.equalsIgnoreCase("CANCEL_REWRITE")){
            transactionDescription="CANCEL_REWRITE";
            endorsementType="CANCEL_REWRITE";
            endorsementSequence="4";
            endorsementNumber="4";
        }

        JsonModifier.modifyJson(jsonNode, "transactionDescription", transactionDescription);
        JsonModifier.modifyJson(jsonNode, "endorsementType", endorsementType);
        JsonModifier.modifyJson(jsonNode, "endorsementNumber", endorsementNumber);
        JsonModifier.modifyJson(jsonNode, "endorsementSequence", endorsementSequence);
        JsonModifier.modifyJson(jsonNode, "endorsementEffectiveDate", Utils.getCurrentDate("yyyy-MM-dd"));
        JsonModifier.modifyJson(jsonNode, "endorsementStatus", "DRAFT");

        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.policyId", policyId);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.policyNumber", policyNumber);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.lastPolicyStatus", "IN_FORCE");

        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.insuredName", firstName);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.officeId", officeID);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.submissionId", submissionID);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerageId", brokerageID);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerId", brokerID);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerInfo.id", firstName);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerInfo.name", brokerName);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerInfo.senderEmail", email);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.clientId", "803517a8-c42a-44c3-b54b-fccaf8768tg87t78gaec1ad3");
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.insuredId", insuredid);
        JsonModifier.modifyJson(jsonNode, "updatedPolicyInfo.brokerInfo.id", brokerID);

        httpResponse = httpHelper.requestPost("/core/policy-service/policies/" + policyId + "/endorsements",String.valueOf(jsonNode ));
        endorsementId = httpResponse.getBody().jsonPath().getString("data.id");
        invoiceId = httpResponse.getBody().jsonPath().getString("data.invoiceId");
        String endorsementStatus = httpResponse.getBody().jsonPath().getString("data.endorsementStatus");
        endorsementId = httpResponse.getBody().jsonPath().getString("data.id");
        System.out.println("endorsment Response: " +endorsementStatus+ httpResponse.prettyPrint());
        JsonModifier.modifyJson(jsonNode, "id", endorsementId);
        JsonModifier.modifyJson(jsonNode, "transactionDescription", transactionDescription);
        JsonModifier.modifyJson(jsonNode, "endorsementType", endorsementType);
        JsonModifier.modifyJson(jsonNode, "endorsementNumber", endorsementNumber);
        JsonModifier.modifyJson(jsonNode, "endorsementSequence", endorsementSequence);
        JsonModifier.modifyJson(jsonNode, "endorsementEffectiveDate", Utils.getCurrentDate("yyyy-MM-dd"));
        JsonModifier.modifyJson(jsonNode, "endorsementStatus", "PROCESSING");
        httpResponse = httpHelper.requestPut("/core/policy-service/policies/" + policyId + "/endorsements/" + endorsementId, String.valueOf(jsonNode));
        String endorsementStatus2 = httpResponse.getBody().jsonPath().getString("data.endorsementStatus");
        System.out.println(" endorsementStatus2 Response: " +endorsementStatus2+ httpResponse.prettyPrint());
        System.out.println("submissions policy:"+submissionID);
        Map<String, String> queryParams4 = Map.of(
                "fetchSubmission", "false"
        );
        Thread.sleep(6000);

        httpResponse = httpHelper2.requestGet("/core/policy-service/submissions/" + submissionID , queryParams4);
        System.out.println("httpResponse policy "+httpResponse.prettyPrint());
        String policyStatus = httpResponse.getBody().jsonPath().getString("data.status");
        if(EndorsementType.equalsIgnoreCase("MONETARY"))
        {
            Assert.assertEquals("IN_FORCE",policyStatus );
            System.out.println("System.out.println(policyStatus)"+policyStatus);

        }else if (EndorsementType.equalsIgnoreCase("NON_MONETARY")){
            Assert.assertEquals("IN_FORCE",policyStatus );
            System.out.println("System.out.println(policyStatus)"+policyStatus);
        }else if (EndorsementType.equalsIgnoreCase("CANCEL")){
            Assert.assertEquals("CANCELED",policyStatus );
            System.out.println("System.out.println(policyStatus)"+policyStatus);

        }else if (EndorsementType.equalsIgnoreCase("CANCEL_REWRITE")){
            Assert.assertEquals("IN_FORCE",policyStatus );
            System.out.println("System.out.println(policyStatus)"+policyStatus);

        }

    }

}
