package decoders;

import helpers.PropertiesHelper;


public class DecoderManager {

    private static DecoderManager instance = null;
    private Decoder decoder;

    private DecoderManager() {
        decoderCustomer();
    }

    public static Decoder getDecoderInstance() {
        //if (instance == null) {
            instance = new DecoderManager();
        //}
        return instance.getDecoder();
    }

    private void decoderCustomer() {
        System.out.println("is this run again"+PropertiesHelper.getProperty("tier") );

        switch (PropertiesHelper.getProperty("tier")) {
            case "CONSTRUCTION-DEV":
                decoder = new ConstructionDevelopDecoder();
                break;
            case "CONSTRUCTION-QA":
                decoder = new ConstructionQaDecoder();
                break;
            case "CONSTRUCTION-STAGE":
                decoder = new ConstructionStageDecoder();
                break;
            case "CONSTRUCTION-INTEGRATION":
            decoder = new ConstructionIntegrationDecoder();
            case "LOGISTICS-DEV":
                decoder = new LogisticsDevelopDecoder();
                break;
            case "LOGISTICS-QA":
                decoder = new LogisticsQaDecoder();
                break;
            case "LOGISTICS-STAGE":
                decoder = new LogisticsStageDecoder();
                break;
            case "LOGISTICS-INTEGRATION":
                decoder = new LogisticsIntegrationDecoder();
            case "AVIATION-DEV":
                decoder = new AviationDevelopDecoder();
                break;
            case "AVIATION-QA":
                decoder = new AviationQaDecoder();
                break;
            case "AVIATION-STAGE":
                decoder = new AviationStageDecoder();
                break;
            case "AVIATION-INTEGRATION":
                decoder = new AviationIntegrationDecoder();
            break;
            case "AVIATION-DEMO":
            decoder = new AviationDemoDecoder();
            break;
            default:
                //throw new ConfigurationException("Env config missing at decoderCustomer()");
        }
    }

    private Decoder getDecoder() {
        return decoder;
    }

}
