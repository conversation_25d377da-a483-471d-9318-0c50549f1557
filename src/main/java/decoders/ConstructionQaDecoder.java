package decoders;

import helpers.PropertiesHelper;

public class ConstructionQaDecoder implements Decoder {

    @Override
    public String getApiHost() {
        return PropertiesHelper.getProperty("api.host.qa");
    }

    @Override
    public String getAuthHost() {
        return PropertiesHelper.getProperty("auth.host.qa");
    }

    @Override
    public String getKeycloakClientSecret() {
        return PropertiesHelper.getProperty("keycloak.client_secret.qa");
    }

}