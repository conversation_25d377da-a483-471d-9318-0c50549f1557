package decoders;

import helpers.PropertiesHelper;

public class AviationIntegrationDecoder implements Decoder {
    @Override
    public String getApiHost() {
        return PropertiesHelper.getProperty("api.host.dev");
    }

    @Override
    public String getAuthHost() {
        return PropertiesHelper.getProperty("auth.host.dev");
    }

    @Override
    public String getKeycloakClientSecret() {
        return PropertiesHelper.getProperty("keycloak.client_secret.dev");
    }
}
