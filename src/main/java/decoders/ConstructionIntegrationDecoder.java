package decoders;

import helpers.PropertiesHelper;

public class ConstructionIntegrationDecoder implements Decoder {

    @Override
    public String getApiHost() {
        return PropertiesHelper.getProperty("api.host.integration");
    }

    @Override
    public String getAuthHost() {
        return PropertiesHelper.getProperty("auth.host.integration");
    }

    @Override
    public String getKeycloakClientSecret() {
        return PropertiesHelper.getProperty("keycloak.client_secret.integration");
    }

}