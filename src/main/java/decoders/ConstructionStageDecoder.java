package decoders;

import helpers.PropertiesHelper;

public class ConstructionStageDecoder implements Decoder {

    @Override
    public String getApiHost() {
        return PropertiesHelper.getProperty("api.host.staging");
    }

    @Override
    public String getAuthHost() {
        return PropertiesHelper.getProperty("auth.host.staging");
    }

    @Override
    public String getKeycloakClientSecret() {
        return PropertiesHelper.getProperty("keycloak.client_secret.staging");
    }

}