package decoders;

import helpers.PropertiesHelper;

public class DemoDecoder implements Decoder {

    @Override
    public String getApiHost() {
        return PropertiesHelper.getProperty("api.host.demo");
    }

    @Override
    public String getAuthHost() {
        return PropertiesHelper.getProperty("auth.host.demo");
    }

    @Override
    public String getKeycloakClientSecret() {
        return PropertiesHelper.getProperty("keycloak.client_secret.demo");
    }

}