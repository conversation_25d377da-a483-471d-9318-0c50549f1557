package pojo.brokerageHubService;

public class BrokerData {
    private String firstName;
    private String lastName;
    private String workPhone;
    private String mobilePhone;
    private String officeId;
    private String emailAddress;
    private String brokerageId;
    private String team;
    private String legalEntity;
    private String jobTitle;
    private boolean filingBroker;
    private String surplusLinesBrokerageName;
    private String surplusLinesAddress;
    private String surplusLinesCity;
    private String surplusLinesState;
    private String surplusLinesZipCode;
    private String surplusLinesLicenseNumber;
    private Address address;
    private String status;
    private String id;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Constructor
    public BrokerData() {
    }

    // Getters and setters
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getWorkPhone() {
        return workPhone;
    }

    public void setWorkPhone(String workPhone) {
        this.workPhone = workPhone;
    }

    public String getMobilePhone() {
        return mobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        this.mobilePhone = mobilePhone;
    }

    public String getOfficeId() {
        return officeId;
    }

    public void setOfficeId(String officeId) {
        this.officeId = officeId;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getBrokerageId() {
        return brokerageId;
    }

    public void setBrokerageId(String brokerageId) {
        this.brokerageId = brokerageId;
    }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    public String getLegalEntity() {
        return legalEntity;
    }

    public void setLegalEntity(String legalEntity) {
        this.legalEntity = legalEntity;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public boolean isFilingBroker() {
        return filingBroker;
    }

    public void setFilingBroker(boolean filingBroker) {
        this.filingBroker = filingBroker;
    }

    public String getSurplusLinesBrokerageName() {
        return surplusLinesBrokerageName;
    }

    public void setSurplusLinesBrokerageName(String surplusLinesBrokerageName) {
        this.surplusLinesBrokerageName = surplusLinesBrokerageName;
    }

    public String getSurplusLinesAddress() {
        return surplusLinesAddress;
    }

    public void setSurplusLinesAddress(String surplusLinesAddress) {
        this.surplusLinesAddress = surplusLinesAddress;
    }

    public String getSurplusLinesCity() {
        return surplusLinesCity;
    }

    public void setSurplusLinesCity(String surplusLinesCity) {
        this.surplusLinesCity = surplusLinesCity;
    }

    public String getSurplusLinesState() {
        return surplusLinesState;
    }

    public void setSurplusLinesState(String surplusLinesState) {
        this.surplusLinesState = surplusLinesState;
    }

    public String getSurplusLinesZipCode() {
        return surplusLinesZipCode;
    }

    public void setSurplusLinesZipCode(String surplusLinesZipCode) {
        this.surplusLinesZipCode = surplusLinesZipCode;
    }

    public String getSurplusLinesLicenseNumber() {
        return surplusLinesLicenseNumber;
    }

    public void setSurplusLinesLicenseNumber(String surplusLinesLicenseNumber) {
        this.surplusLinesLicenseNumber = surplusLinesLicenseNumber;
    }

    public Address getAddress() {
        return address;
    }

    public void setAddress(Address address) {
        this.address = address;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // AddressInput class for nested object
    public static class Address {
        private String country;
        private String state;
        private String city;
        private String zipcode;
        private String addressLine1;
        private String addressLine2;
        private String addressType;



        // Constructor
        public Address() {
        }

        // Getters and setters
        public String getCountry() {
            return country;
        }

        public void setCountry(String country) {
            this.country = country;
        }

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public String getZipcode() {
            return zipcode;
        }

        public void setZipcode(String zipcode) {
            this.zipcode = zipcode;
        }

        public String getAddressLine1() {
            return addressLine1;
        }

        public void setAddressLine1(String addressLine1) {
            this.addressLine1 = addressLine1;
        }

        public String getAddressLine2() {
            return addressLine2;
        }

        public void setAddressLine2(String addressLine2) {
            this.addressLine2 = addressLine2;
        }

        public String getAddressType() {
            return addressType;
        }

        public void setAddressType(String addressType) {
            this.addressType = addressType;
        }
    }
}
