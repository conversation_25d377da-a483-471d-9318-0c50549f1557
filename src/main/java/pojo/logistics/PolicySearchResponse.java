package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class PolicySearchResponse {
    
    @JsonProperty("data")
    private List<PolicyData> data;
    
    @JsonProperty("totalElements")
    private Integer totalElements;
    
    @JsonProperty("totalPages")
    private Integer totalPages;
    
    @JsonProperty("pageNumber")
    private Integer pageNumber;
    
    @JsonProperty("pageSize")
    private Integer pageSize;
    
    @JsonProperty("first")
    private Boolean first;
    
    @JsonProperty("last")
    private Boolean last;
    
    @JsonProperty("numberOfElements")
    private Integer numberOfElements;
    
    // Constructors
    public PolicySearchResponse() {}
    
    // Getters and Setters
    public List<PolicyData> getData() {
        return data;
    }
    
    public void setData(List<PolicyData> data) {
        this.data = data;
    }
    
    public Integer getTotalElements() {
        return totalElements;
    }
    
    public void setTotalElements(Integer totalElements) {
        this.totalElements = totalElements;
    }
    
    public Integer getTotalPages() {
        return totalPages;
    }
    
    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }
    
    public Integer getPageNumber() {
        return pageNumber;
    }
    
    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    public Integer getPageSize() {
        return pageSize;
    }
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    public Boolean getFirst() {
        return first;
    }
    
    public void setFirst(Boolean first) {
        this.first = first;
    }
    
    public Boolean getLast() {
        return last;
    }
    
    public void setLast(Boolean last) {
        this.last = last;
    }
    
    public Integer getNumberOfElements() {
        return numberOfElements;
    }
    
    public void setNumberOfElements(Integer numberOfElements) {
        this.numberOfElements = numberOfElements;
    }
    
    @Override
    public String toString() {
        return "PolicySearchResponse{" +
                "data=" + data +
                ", totalElements=" + totalElements +
                ", totalPages=" + totalPages +
                ", pageNumber=" + pageNumber +
                ", pageSize=" + pageSize +
                ", first=" + first +
                ", last=" + last +
                ", numberOfElements=" + numberOfElements +
                '}';
    }
    
    // Inner class for Policy Data
    public static class PolicyData {
        @JsonProperty("submissionId")
        private String submissionId;
        
        @JsonProperty("proposalNumber")
        private String proposalNumber;
        
        @JsonProperty("insuredInfo")
        private InsuredInfo insuredInfo;
        
        @JsonProperty("productCode")
        private List<String> productCode;
        
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("underwriter")
        private String underwriter;
        
        @JsonProperty("effectiveDate")
        private String effectiveDate;
        
        @JsonProperty("expiryDate")
        private String expiryDate;
        
        @JsonProperty("createdAt")
        private String createdAt;
        
        @JsonProperty("updatedAt")
        private String updatedAt;
        
        @JsonProperty("premium")
        private Double premium;
        
        @JsonProperty("businessType")
        private String businessType;
        
        // Constructors
        public PolicyData() {}
        
        // Getters and Setters
        public String getSubmissionId() {
            return submissionId;
        }
        
        public void setSubmissionId(String submissionId) {
            this.submissionId = submissionId;
        }
        
        public String getProposalNumber() {
            return proposalNumber;
        }
        
        public void setProposalNumber(String proposalNumber) {
            this.proposalNumber = proposalNumber;
        }
        
        public InsuredInfo getInsuredInfo() {
            return insuredInfo;
        }
        
        public void setInsuredInfo(InsuredInfo insuredInfo) {
            this.insuredInfo = insuredInfo;
        }
        
        public List<String> getProductCode() {
            return productCode;
        }
        
        public void setProductCode(List<String> productCode) {
            this.productCode = productCode;
        }
        
        public String getStatus() {
            return status;
        }
        
        public void setStatus(String status) {
            this.status = status;
        }
        
        public String getUnderwriter() {
            return underwriter;
        }
        
        public void setUnderwriter(String underwriter) {
            this.underwriter = underwriter;
        }
        
        public String getEffectiveDate() {
            return effectiveDate;
        }
        
        public void setEffectiveDate(String effectiveDate) {
            this.effectiveDate = effectiveDate;
        }
        
        public String getExpiryDate() {
            return expiryDate;
        }
        
        public void setExpiryDate(String expiryDate) {
            this.expiryDate = expiryDate;
        }
        
        public String getCreatedAt() {
            return createdAt;
        }
        
        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }
        
        public String getUpdatedAt() {
            return updatedAt;
        }
        
        public void setUpdatedAt(String updatedAt) {
            this.updatedAt = updatedAt;
        }
        
        public Double getPremium() {
            return premium;
        }
        
        public void setPremium(Double premium) {
            this.premium = premium;
        }
        
        public String getBusinessType() {
            return businessType;
        }
        
        public void setBusinessType(String businessType) {
            this.businessType = businessType;
        }
        
        @Override
        public String toString() {
            return "PolicyData{" +
                    "submissionId='" + submissionId + '\'' +
                    ", proposalNumber='" + proposalNumber + '\'' +
                    ", insuredInfo=" + insuredInfo +
                    ", productCode=" + productCode +
                    ", status='" + status + '\'' +
                    ", underwriter='" + underwriter + '\'' +
                    ", premium=" + premium +
                    '}';
        }
    }
    
    // Inner class for InsuredInfo
    public static class InsuredInfo {
        @JsonProperty("businessName")
        private String businessName;
        
        @JsonProperty("insuredId")
        private String insuredId;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("companyName")
        private String companyName;
        
        // Constructors
        public InsuredInfo() {}
        
        // Getters and Setters
        public String getBusinessName() {
            return businessName;
        }
        
        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
        
        public String getInsuredId() {
            return insuredId;
        }
        
        public void setInsuredId(String insuredId) {
            this.insuredId = insuredId;
        }
        
        public String getState() {
            return state;
        }
        
        public void setState(String state) {
            this.state = state;
        }
        
        public String getCompanyName() {
            return companyName;
        }
        
        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }
        
        @Override
        public String toString() {
            return "InsuredInfo{" +
                    "businessName='" + businessName + '\'' +
                    ", insuredId='" + insuredId + '\'' +
                    ", state='" + state + '\'' +
                    ", companyName='" + companyName + '\'' +
                    '}';
        }
    }
}
