package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public class CalculatePremiumRequest {

    @JsonProperty("_id")
    private IdInfo id;

    @JsonProperty("insuredInfo")
    private InsuredInfo insuredInfo;

    @JsonProperty("class")
    private List<String> classType;

    @JsonProperty("lob")
    private List<String> lob;

    @JsonProperty("status")
    private String status;

    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("submissionId")
    private String submissionId;

    @JsonProperty("businessType")
    private String businessType;

    @JsonProperty("gfrLast12Months")
    private String gfrLast12Months;

    @JsonProperty("gfrNext12Months")
    private String gfrNext12Months;

    @JsonProperty("containerisedShipments")
    private String containerisedShipments;

    @JsonProperty("minimumPerCertificate")
    private String minimumPerCertificate;

    @JsonProperty("rate")
    private Double rate;

    @JsonProperty("premium")
    private Double premium;

    @JsonProperty("brokerInfo")
    private BrokerInfo brokerInfo;

    @JsonProperty("commodityTypes")
    private List<CommodityType> commodityTypes;

    @JsonProperty("conveyance")
    private Map<String, Object> conveyance;

    @JsonProperty("geographicalArea")
    private List<GeographicalArea> geographicalArea;

    @JsonProperty("lossHistory")
    private LossHistory lossHistory;

    @JsonProperty("premiumSummary")
    private PremiumSummary premiumSummary;

    @JsonProperty("receiptsAsBrokerAgentAmount")
    private String receiptsAsBrokerAgentAmount;

    @JsonProperty("receiptsAsBrokerAgentPercentage")
    private String receiptsAsBrokerAgentPercentage;

    @JsonProperty("receiptsAsIndirectAirCarrierAmount")
    private String receiptsAsIndirectAirCarrierAmount;

    @JsonProperty("receiptsAsIndirectAirCarrierPercentage")
    private String receiptsAsIndirectAirCarrierPercentage;

    @JsonProperty("receiptsAsMotorTruckCarrierAmount")
    private String receiptsAsMotorTruckCarrierAmount;

    @JsonProperty("receiptsAsMotorTruckCarrierPercentage")
    private String receiptsAsMotorTruckCarrierPercentage;

    @JsonProperty("receiptsAsNVOCCAmount")
    private String receiptsAsNVOCCAmount;

    @JsonProperty("receiptsAsNVOCCPercentage")
    private String receiptsAsNVOCCPercentage;

    @JsonProperty("receiptsAsWarehouseOperatorAmount")
    private String receiptsAsWarehouseOperatorAmount;

    @JsonProperty("receiptsAsWarehouseOperatorPercentage")
    private String receiptsAsWarehouseOperatorPercentage;

    @JsonProperty("shipperInterestAdjustmentPeriod")
    private String shipperInterestAdjustmentPeriod;

    @JsonProperty("shipperInterestAdjustmentRate")
    private String shipperInterestAdjustmentRate;

    @JsonProperty("shipperInterestCargoInlandDeposit")
    private String shipperInterestCargoInlandDeposit;

    // Constructors
    public CalculatePremiumRequest() {}

    // Getters and Setters
    public IdInfo getId() { return id; }
    public void setId(IdInfo id) { this.id = id; }

    public InsuredInfo getInsuredInfo() { return insuredInfo; }
    public void setInsuredInfo(InsuredInfo insuredInfo) { this.insuredInfo = insuredInfo; }

    public List<String> getClassType() { return classType; }
    public void setClassType(List<String> classType) { this.classType = classType; }

    public List<String> getLob() { return lob; }
    public void setLob(List<String> lob) { this.lob = lob; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getClientId() { return clientId; }
    public void setClientId(String clientId) { this.clientId = clientId; }

    public String getSubmissionId() { return submissionId; }
    public void setSubmissionId(String submissionId) { this.submissionId = submissionId; }

    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }

    public String getGfrLast12Months() { return gfrLast12Months; }
    public void setGfrLast12Months(String gfrLast12Months) { this.gfrLast12Months = gfrLast12Months; }

    public String getGfrNext12Months() { return gfrNext12Months; }
    public void setGfrNext12Months(String gfrNext12Months) { this.gfrNext12Months = gfrNext12Months; }

    public String getContainerisedShipments() { return containerisedShipments; }
    public void setContainerisedShipments(String containerisedShipments) { this.containerisedShipments = containerisedShipments; }

    public String getMinimumPerCertificate() { return minimumPerCertificate; }
    public void setMinimumPerCertificate(String minimumPerCertificate) { this.minimumPerCertificate = minimumPerCertificate; }

    public Double getRate() { return rate; }
    public void setRate(Double rate) { this.rate = rate; }

    public Double getPremium() { return premium; }
    public void setPremium(Double premium) { this.premium = premium; }

    public BrokerInfo getBrokerInfo() { return brokerInfo; }
    public void setBrokerInfo(BrokerInfo brokerInfo) { this.brokerInfo = brokerInfo; }

    public List<CommodityType> getCommodityTypes() { return commodityTypes; }
    public void setCommodityTypes(List<CommodityType> commodityTypes) { this.commodityTypes = commodityTypes; }

    public Map<String, Object> getConveyance() { return conveyance; }
    public void setConveyance(Map<String, Object> conveyance) { this.conveyance = conveyance; }

    public List<GeographicalArea> getGeographicalArea() { return geographicalArea; }
    public void setGeographicalArea(List<GeographicalArea> geographicalArea) { this.geographicalArea = geographicalArea; }

    public LossHistory getLossHistory() { return lossHistory; }
    public void setLossHistory(LossHistory lossHistory) { this.lossHistory = lossHistory; }

    public PremiumSummary getPremiumSummary() { return premiumSummary; }
    public void setPremiumSummary(PremiumSummary premiumSummary) { this.premiumSummary = premiumSummary; }

    // Inner classes for nested objects
    public static class IdInfo {
        @JsonProperty("timestamp")
        private Long timestamp;

        @JsonProperty("date")
        private String date;

        // Constructors, getters and setters
        public IdInfo() {}

        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }

        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
    }

    public static class InsuredInfo {
        @JsonProperty("_id")
        private IdInfo id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("businessName")
        private String businessName;

        @JsonProperty("insuredId")
        private String insuredId;

        @JsonProperty("clientId")
        private String clientId;

        @JsonProperty("state")
        private String state;

        @JsonProperty("companyAddress")
        private String companyAddress;

        @JsonProperty("companyName")
        private String companyName;

        @JsonProperty("effectiveDate")
        private String effectiveDate;

        @JsonProperty("expiryDate")
        private String expiryDate;

        // Constructors, getters and setters
        public InsuredInfo() {}

        public IdInfo getId() { return id; }
        public void setId(IdInfo id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getBusinessName() { return businessName; }
        public void setBusinessName(String businessName) { this.businessName = businessName; }

        public String getInsuredId() { return insuredId; }
        public void setInsuredId(String insuredId) { this.insuredId = insuredId; }

        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }

        public String getState() { return state; }
        public void setState(String state) { this.state = state; }

        public String getCompanyAddress() { return companyAddress; }
        public void setCompanyAddress(String companyAddress) { this.companyAddress = companyAddress; }

        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }

        public String getEffectiveDate() { return effectiveDate; }
        public void setEffectiveDate(String effectiveDate) { this.effectiveDate = effectiveDate; }

        public String getExpiryDate() { return expiryDate; }
        public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }
    }

    public static class BrokerInfo {
        @JsonProperty("brokerContact")
        private String brokerContact;

        @JsonProperty("brokerOfRecord")
        private String brokerOfRecord;

        @JsonProperty("accountManager")
        private String accountManager;

        @JsonProperty("commission")
        private String commission;

        @JsonProperty("carrier")
        private String carrier;

        // Constructors, getters and setters
        public BrokerInfo() {}

        public String getBrokerContact() { return brokerContact; }
        public void setBrokerContact(String brokerContact) { this.brokerContact = brokerContact; }

        public String getBrokerOfRecord() { return brokerOfRecord; }
        public void setBrokerOfRecord(String brokerOfRecord) { this.brokerOfRecord = brokerOfRecord; }

        public String getAccountManager() { return accountManager; }
        public void setAccountManager(String accountManager) { this.accountManager = accountManager; }

        public String getCommission() { return commission; }
        public void setCommission(String commission) { this.commission = commission; }

        public String getCarrier() { return carrier; }
        public void setCarrier(String carrier) { this.carrier = carrier; }
    }
}
