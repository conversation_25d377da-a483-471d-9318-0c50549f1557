package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public class CalculatePremiumRequest {

    @JsonProperty("_id")
    private IdInfo id;

    @JsonProperty("insuredInfo")
    private InsuredInfo insuredInfo;

    @JsonProperty("class")
    private List<String> classType;

    @JsonProperty("lob")
    private List<String> lob;

    @JsonProperty("status")
    private String status;

    @JsonProperty("clientId")
    private String clientId;

    @JsonProperty("submissionId")
    private String submissionId;

    @JsonProperty("businessType")
    private String businessType;

    @JsonProperty("gfrLast12Months")
    private String gfrLast12Months;

    @JsonProperty("gfrNext12Months")
    private String gfrNext12Months;

    @JsonProperty("containerisedShipments")
    private String containerisedShipments;

    @JsonProperty("minimumPerCertificate")
    private String minimumPerCertificate;

    @JsonProperty("rate")
    private Double rate;

    @JsonProperty("premium")
    private Double premium;

    @JsonProperty("brokerInfo")
    private BrokerInfo brokerInfo;

    @JsonProperty("commodityTypes")
    private List<CommodityType> commodityTypes;

    @JsonProperty("conveyance")
    private Map<String, Object> conveyance;

    @JsonProperty("geographicalArea")
    private List<GeographicalArea> geographicalArea;

    @JsonProperty("lossHistory")
    private LossHistory lossHistory;

    @JsonProperty("premiumSummary")
    private PremiumSummary premiumSummary;

    @JsonProperty("receiptsAsBrokerAgentAmount")
    private String receiptsAsBrokerAgentAmount;

    @JsonProperty("receiptsAsBrokerAgentPercentage")
    private String receiptsAsBrokerAgentPercentage;

    @JsonProperty("receiptsAsIndirectAirCarrierAmount")
    private String receiptsAsIndirectAirCarrierAmount;

    @JsonProperty("receiptsAsIndirectAirCarrierPercentage")
    private String receiptsAsIndirectAirCarrierPercentage;

    @JsonProperty("receiptsAsMotorTruckCarrierAmount")
    private String receiptsAsMotorTruckCarrierAmount;

    @JsonProperty("receiptsAsMotorTruckCarrierPercentage")
    private String receiptsAsMotorTruckCarrierPercentage;

    @JsonProperty("receiptsAsNVOCCAmount")
    private String receiptsAsNVOCCAmount;

    @JsonProperty("receiptsAsNVOCCPercentage")
    private String receiptsAsNVOCCPercentage;

    @JsonProperty("receiptsAsWarehouseOperatorAmount")
    private String receiptsAsWarehouseOperatorAmount;

    @JsonProperty("receiptsAsWarehouseOperatorPercentage")
    private String receiptsAsWarehouseOperatorPercentage;

    @JsonProperty("shipperInterestAdjustmentPeriod")
    private String shipperInterestAdjustmentPeriod;

    @JsonProperty("shipperInterestAdjustmentRate")
    private String shipperInterestAdjustmentRate;

    @JsonProperty("shipperInterestCargoInlandDeposit")
    private String shipperInterestCargoInlandDeposit;

    @JsonProperty("shipperInterestWarehouseStorageDeposit")
    private String shipperInterestWarehouseStorageDeposit;

    @JsonProperty("createdAt")
    private String createdAt;

    @JsonProperty("updatedAt")
    private String updatedAt;

    @JsonProperty("createdBy")
    private String createdBy;

    @JsonProperty("updatedBy")
    private String updatedBy;

    @JsonProperty("subLobId")
    private String subLobId;

    @JsonProperty("additionalInsuredInfo")
    private List<Map<String, Object>> additionalInsuredInfo;

    @JsonProperty("cargoLiabilityAjustmentRate")
    private String cargoLiabilityAjustmentRate;

    @JsonProperty("cargoLiabilityPremium")
    private Double cargoLiabilityPremium;

    @JsonProperty("geographicalScope")
    private Map<String, Object> geographicalScope;

    @JsonProperty("goodsInsured")
    private Map<String, Object> goodsInsured;

    @JsonProperty("oceanCargoInlandTransit")
    private Map<String, Object> oceanCargoInlandTransit;

    @JsonProperty("premiumCalculation")
    private Map<String, Object> premiumCalculation;

    @JsonProperty("proposalDate")
    private String proposalDate;

    @JsonProperty("proposalNumber")
    private String proposalNumber;

    @JsonProperty("quoteStructure")
    private Map<String, Object> quoteStructure;

    @JsonProperty("shipperInterestSpecifiedCommodities")
    private Map<String, Object> shipperInterestSpecifiedCommodities;

    @JsonProperty("termsAndConditions")
    private Map<String, Object> termsAndConditions;

    @JsonProperty("underwriterName")
    private String underwriterName;

    @JsonProperty("valuation")
    private Map<String, Object> valuation;

    @JsonProperty("warehouseFlatAnnualPremium")
    private Double warehouseFlatAnnualPremium;

    @JsonProperty("whllPremium")
    private Double whllPremium;

    @JsonProperty("wllAjustmentRate")
    private String wllAjustmentRate;

    @JsonProperty("xwllAjustmentRate")
    private String xwllAjustmentRate;

    @JsonProperty("xxwhllPremium")
    private Double xxwhllPremium;

    // Constructors
    public CalculatePremiumRequest() {}

    // Getters and Setters
    public IdInfo getId() { return id; }
    public void setId(IdInfo id) { this.id = id; }

    public InsuredInfo getInsuredInfo() { return insuredInfo; }
    public void setInsuredInfo(InsuredInfo insuredInfo) { this.insuredInfo = insuredInfo; }

    public List<String> getClassType() { return classType; }
    public void setClassType(List<String> classType) { this.classType = classType; }

    public List<String> getLob() { return lob; }
    public void setLob(List<String> lob) { this.lob = lob; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public String getClientId() { return clientId; }
    public void setClientId(String clientId) { this.clientId = clientId; }

    public String getSubmissionId() { return submissionId; }
    public void setSubmissionId(String submissionId) { this.submissionId = submissionId; }

    public String getBusinessType() { return businessType; }
    public void setBusinessType(String businessType) { this.businessType = businessType; }

    public String getGfrLast12Months() { return gfrLast12Months; }
    public void setGfrLast12Months(String gfrLast12Months) { this.gfrLast12Months = gfrLast12Months; }

    public String getGfrNext12Months() { return gfrNext12Months; }
    public void setGfrNext12Months(String gfrNext12Months) { this.gfrNext12Months = gfrNext12Months; }

    public String getContainerisedShipments() { return containerisedShipments; }
    public void setContainerisedShipments(String containerisedShipments) { this.containerisedShipments = containerisedShipments; }

    public String getMinimumPerCertificate() { return minimumPerCertificate; }
    public void setMinimumPerCertificate(String minimumPerCertificate) { this.minimumPerCertificate = minimumPerCertificate; }

    public Double getRate() { return rate; }
    public void setRate(Double rate) { this.rate = rate; }

    public Double getPremium() { return premium; }
    public void setPremium(Double premium) { this.premium = premium; }

    public BrokerInfo getBrokerInfo() { return brokerInfo; }
    public void setBrokerInfo(BrokerInfo brokerInfo) { this.brokerInfo = brokerInfo; }

    public List<CommodityType> getCommodityTypes() { return commodityTypes; }
    public void setCommodityTypes(List<CommodityType> commodityTypes) { this.commodityTypes = commodityTypes; }

    public Map<String, Object> getConveyance() { return conveyance; }
    public void setConveyance(Map<String, Object> conveyance) { this.conveyance = conveyance; }

    public List<GeographicalArea> getGeographicalArea() { return geographicalArea; }
    public void setGeographicalArea(List<GeographicalArea> geographicalArea) { this.geographicalArea = geographicalArea; }

    public LossHistory getLossHistory() { return lossHistory; }
    public void setLossHistory(LossHistory lossHistory) { this.lossHistory = lossHistory; }

    public PremiumSummary getPremiumSummary() { return premiumSummary; }
    public void setPremiumSummary(PremiumSummary premiumSummary) { this.premiumSummary = premiumSummary; }

    public String getReceiptsAsBrokerAgentAmount() { return receiptsAsBrokerAgentAmount; }
    public void setReceiptsAsBrokerAgentAmount(String receiptsAsBrokerAgentAmount) { this.receiptsAsBrokerAgentAmount = receiptsAsBrokerAgentAmount; }

    public String getReceiptsAsBrokerAgentPercentage() { return receiptsAsBrokerAgentPercentage; }
    public void setReceiptsAsBrokerAgentPercentage(String receiptsAsBrokerAgentPercentage) { this.receiptsAsBrokerAgentPercentage = receiptsAsBrokerAgentPercentage; }

    public String getReceiptsAsIndirectAirCarrierAmount() { return receiptsAsIndirectAirCarrierAmount; }
    public void setReceiptsAsIndirectAirCarrierAmount(String receiptsAsIndirectAirCarrierAmount) { this.receiptsAsIndirectAirCarrierAmount = receiptsAsIndirectAirCarrierAmount; }

    public String getReceiptsAsIndirectAirCarrierPercentage() { return receiptsAsIndirectAirCarrierPercentage; }
    public void setReceiptsAsIndirectAirCarrierPercentage(String receiptsAsIndirectAirCarrierPercentage) { this.receiptsAsIndirectAirCarrierPercentage = receiptsAsIndirectAirCarrierPercentage; }

    public String getReceiptsAsMotorTruckCarrierAmount() { return receiptsAsMotorTruckCarrierAmount; }
    public void setReceiptsAsMotorTruckCarrierAmount(String receiptsAsMotorTruckCarrierAmount) { this.receiptsAsMotorTruckCarrierAmount = receiptsAsMotorTruckCarrierAmount; }

    public String getReceiptsAsMotorTruckCarrierPercentage() { return receiptsAsMotorTruckCarrierPercentage; }
    public void setReceiptsAsMotorTruckCarrierPercentage(String receiptsAsMotorTruckCarrierPercentage) { this.receiptsAsMotorTruckCarrierPercentage = receiptsAsMotorTruckCarrierPercentage; }

    public String getReceiptsAsNVOCCAmount() { return receiptsAsNVOCCAmount; }
    public void setReceiptsAsNVOCCAmount(String receiptsAsNVOCCAmount) { this.receiptsAsNVOCCAmount = receiptsAsNVOCCAmount; }

    public String getReceiptsAsNVOCCPercentage() { return receiptsAsNVOCCPercentage; }
    public void setReceiptsAsNVOCCPercentage(String receiptsAsNVOCCPercentage) { this.receiptsAsNVOCCPercentage = receiptsAsNVOCCPercentage; }

    public String getReceiptsAsWarehouseOperatorAmount() { return receiptsAsWarehouseOperatorAmount; }
    public void setReceiptsAsWarehouseOperatorAmount(String receiptsAsWarehouseOperatorAmount) { this.receiptsAsWarehouseOperatorAmount = receiptsAsWarehouseOperatorAmount; }

    public String getReceiptsAsWarehouseOperatorPercentage() { return receiptsAsWarehouseOperatorPercentage; }
    public void setReceiptsAsWarehouseOperatorPercentage(String receiptsAsWarehouseOperatorPercentage) { this.receiptsAsWarehouseOperatorPercentage = receiptsAsWarehouseOperatorPercentage; }

    public String getShipperInterestAdjustmentPeriod() { return shipperInterestAdjustmentPeriod; }
    public void setShipperInterestAdjustmentPeriod(String shipperInterestAdjustmentPeriod) { this.shipperInterestAdjustmentPeriod = shipperInterestAdjustmentPeriod; }

    public String getShipperInterestAdjustmentRate() { return shipperInterestAdjustmentRate; }
    public void setShipperInterestAdjustmentRate(String shipperInterestAdjustmentRate) { this.shipperInterestAdjustmentRate = shipperInterestAdjustmentRate; }

    public String getShipperInterestCargoInlandDeposit() { return shipperInterestCargoInlandDeposit; }
    public void setShipperInterestCargoInlandDeposit(String shipperInterestCargoInlandDeposit) { this.shipperInterestCargoInlandDeposit = shipperInterestCargoInlandDeposit; }

    public String getShipperInterestWarehouseStorageDeposit() { return shipperInterestWarehouseStorageDeposit; }
    public void setShipperInterestWarehouseStorageDeposit(String shipperInterestWarehouseStorageDeposit) { this.shipperInterestWarehouseStorageDeposit = shipperInterestWarehouseStorageDeposit; }

    public String getCreatedAt() { return createdAt; }
    public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }

    public String getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }

    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }

    public String getUpdatedBy() { return updatedBy; }
    public void setUpdatedBy(String updatedBy) { this.updatedBy = updatedBy; }

    public String getSubLobId() { return subLobId; }
    public void setSubLobId(String subLobId) { this.subLobId = subLobId; }

    public List<Map<String, Object>> getAdditionalInsuredInfo() { return additionalInsuredInfo; }
    public void setAdditionalInsuredInfo(List<Map<String, Object>> additionalInsuredInfo) { this.additionalInsuredInfo = additionalInsuredInfo; }

    public String getCargoLiabilityAjustmentRate() { return cargoLiabilityAjustmentRate; }
    public void setCargoLiabilityAjustmentRate(String cargoLiabilityAjustmentRate) { this.cargoLiabilityAjustmentRate = cargoLiabilityAjustmentRate; }

    public Double getCargoLiabilityPremium() { return cargoLiabilityPremium; }
    public void setCargoLiabilityPremium(Double cargoLiabilityPremium) { this.cargoLiabilityPremium = cargoLiabilityPremium; }

    public Map<String, Object> getGeographicalScope() { return geographicalScope; }
    public void setGeographicalScope(Map<String, Object> geographicalScope) { this.geographicalScope = geographicalScope; }

    public Map<String, Object> getGoodsInsured() { return goodsInsured; }
    public void setGoodsInsured(Map<String, Object> goodsInsured) { this.goodsInsured = goodsInsured; }

    public Map<String, Object> getOceanCargoInlandTransit() { return oceanCargoInlandTransit; }
    public void setOceanCargoInlandTransit(Map<String, Object> oceanCargoInlandTransit) { this.oceanCargoInlandTransit = oceanCargoInlandTransit; }

    public Map<String, Object> getPremiumCalculation() { return premiumCalculation; }
    public void setPremiumCalculation(Map<String, Object> premiumCalculation) { this.premiumCalculation = premiumCalculation; }

    public String getProposalDate() { return proposalDate; }
    public void setProposalDate(String proposalDate) { this.proposalDate = proposalDate; }

    public String getProposalNumber() { return proposalNumber; }
    public void setProposalNumber(String proposalNumber) { this.proposalNumber = proposalNumber; }

    public Map<String, Object> getQuoteStructure() { return quoteStructure; }
    public void setQuoteStructure(Map<String, Object> quoteStructure) { this.quoteStructure = quoteStructure; }

    public Map<String, Object> getShipperInterestSpecifiedCommodities() { return shipperInterestSpecifiedCommodities; }
    public void setShipperInterestSpecifiedCommodities(Map<String, Object> shipperInterestSpecifiedCommodities) { this.shipperInterestSpecifiedCommodities = shipperInterestSpecifiedCommodities; }

    public Map<String, Object> getTermsAndConditions() { return termsAndConditions; }
    public void setTermsAndConditions(Map<String, Object> termsAndConditions) { this.termsAndConditions = termsAndConditions; }

    public String getUnderwriterName() { return underwriterName; }
    public void setUnderwriterName(String underwriterName) { this.underwriterName = underwriterName; }

    public Map<String, Object> getValuation() { return valuation; }
    public void setValuation(Map<String, Object> valuation) { this.valuation = valuation; }

    public Double getWarehouseFlatAnnualPremium() { return warehouseFlatAnnualPremium; }
    public void setWarehouseFlatAnnualPremium(Double warehouseFlatAnnualPremium) { this.warehouseFlatAnnualPremium = warehouseFlatAnnualPremium; }

    public Double getWhllPremium() { return whllPremium; }
    public void setWhllPremium(Double whllPremium) { this.whllPremium = whllPremium; }

    public String getWllAjustmentRate() { return wllAjustmentRate; }
    public void setWllAjustmentRate(String wllAjustmentRate) { this.wllAjustmentRate = wllAjustmentRate; }

    public String getXwllAjustmentRate() { return xwllAjustmentRate; }
    public void setXwllAjustmentRate(String xwllAjustmentRate) { this.xwllAjustmentRate = xwllAjustmentRate; }

    public Double getXxwhllPremium() { return xxwhllPremium; }
    public void setXxwhllPremium(Double xxwhllPremium) { this.xxwhllPremium = xxwhllPremium; }

    // Inner classes for nested objects
    public static class IdInfo {
        @JsonProperty("timestamp")
        private Long timestamp;

        @JsonProperty("date")
        private String date;

        // Constructors, getters and setters
        public IdInfo() {}

        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }

        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
    }

    public static class InsuredInfo {
        @JsonProperty("_id")
        private IdInfo id;

        @JsonProperty("name")
        private String name;

        @JsonProperty("businessName")
        private String businessName;

        @JsonProperty("insuredId")
        private String insuredId;

        @JsonProperty("clientId")
        private String clientId;

        @JsonProperty("state")
        private String state;

        @JsonProperty("companyAddress")
        private String companyAddress;

        @JsonProperty("companyName")
        private String companyName;

        @JsonProperty("effectiveDate")
        private String effectiveDate;

        @JsonProperty("expiryDate")
        private String expiryDate;

        @JsonProperty("insuredNumber")
        private Integer insuredNumber;

        @JsonProperty("isDeleted")
        private Boolean isDeleted;

        @JsonProperty("createdAt")
        private String createdAt;

        @JsonProperty("updatedAt")
        private String updatedAt;

        @JsonProperty("insuredClientId")
        private String insuredClientId;

        @JsonProperty("address")
        private String address;

        @JsonProperty("city")
        private String city;

        @JsonProperty("noOfEmployees")
        private String noOfEmployees;

        @JsonProperty("riskManager")
        private String riskManager;

        @JsonProperty("yearsOnExperience")
        private String yearsOnExperience;

        @JsonProperty("zipcode")
        private String zipcode;

        // Constructors, getters and setters
        public InsuredInfo() {}

        public IdInfo getId() { return id; }
        public void setId(IdInfo id) { this.id = id; }

        public String getName() { return name; }
        public void setName(String name) { this.name = name; }

        public String getBusinessName() { return businessName; }
        public void setBusinessName(String businessName) { this.businessName = businessName; }

        public String getInsuredId() { return insuredId; }
        public void setInsuredId(String insuredId) { this.insuredId = insuredId; }

        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }

        public String getState() { return state; }
        public void setState(String state) { this.state = state; }

        public String getCompanyAddress() { return companyAddress; }
        public void setCompanyAddress(String companyAddress) { this.companyAddress = companyAddress; }

        public String getCompanyName() { return companyName; }
        public void setCompanyName(String companyName) { this.companyName = companyName; }

        public String getEffectiveDate() { return effectiveDate; }
        public void setEffectiveDate(String effectiveDate) { this.effectiveDate = effectiveDate; }

        public String getExpiryDate() { return expiryDate; }
        public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }

        public Integer getInsuredNumber() { return insuredNumber; }
        public void setInsuredNumber(Integer insuredNumber) { this.insuredNumber = insuredNumber; }

        public Boolean getIsDeleted() { return isDeleted; }
        public void setIsDeleted(Boolean isDeleted) { this.isDeleted = isDeleted; }

        public String getCreatedAt() { return createdAt; }
        public void setCreatedAt(String createdAt) { this.createdAt = createdAt; }

        public String getUpdatedAt() { return updatedAt; }
        public void setUpdatedAt(String updatedAt) { this.updatedAt = updatedAt; }

        public String getInsuredClientId() { return insuredClientId; }
        public void setInsuredClientId(String insuredClientId) { this.insuredClientId = insuredClientId; }

        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getNoOfEmployees() { return noOfEmployees; }
        public void setNoOfEmployees(String noOfEmployees) { this.noOfEmployees = noOfEmployees; }

        public String getRiskManager() { return riskManager; }
        public void setRiskManager(String riskManager) { this.riskManager = riskManager; }

        public String getYearsOnExperience() { return yearsOnExperience; }
        public void setYearsOnExperience(String yearsOnExperience) { this.yearsOnExperience = yearsOnExperience; }

        public String getZipcode() { return zipcode; }
        public void setZipcode(String zipcode) { this.zipcode = zipcode; }
    }

    public static class BrokerInfo {
        @JsonProperty("brokerContact")
        private String brokerContact;

        @JsonProperty("brokerOfRecord")
        private String brokerOfRecord;

        @JsonProperty("accountManager")
        private String accountManager;

        @JsonProperty("commission")
        private String commission;

        @JsonProperty("carrier")
        private String carrier;

        // Constructors, getters and setters
        public BrokerInfo() {}

        public String getBrokerContact() { return brokerContact; }
        public void setBrokerContact(String brokerContact) { this.brokerContact = brokerContact; }

        public String getBrokerOfRecord() { return brokerOfRecord; }
        public void setBrokerOfRecord(String brokerOfRecord) { this.brokerOfRecord = brokerOfRecord; }

        public String getAccountManager() { return accountManager; }
        public void setAccountManager(String accountManager) { this.accountManager = accountManager; }

        public String getCommission() { return commission; }
        public void setCommission(String commission) { this.commission = commission; }

        public String getCarrier() { return carrier; }
        public void setCarrier(String carrier) { this.carrier = carrier; }
    }
}
