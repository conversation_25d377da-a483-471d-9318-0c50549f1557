package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.Map;

public class PolicySubmissionDetailedResponse {
    
    @JsonProperty("data")
    private PolicyData data;
    
    @JsonProperty("success")
    private Boolean success;
    
    @JsonProperty("message")
    private String message;
    
    @JsonProperty("timestamp")
    private String timestamp;
    
    // Constructors
    public PolicySubmissionDetailedResponse() {}
    
    // Getters and Setters
    public PolicyData getData() { return data; }
    public void setData(PolicyData data) { this.data = data; }
    
    public Boolean getSuccess() { return success; }
    public void setSuccess(Boolean success) { this.success = success; }
    
    public String getMessage() { return message; }
    public void setMessage(String message) { this.message = message; }
    
    public String getTimestamp() { return timestamp; }
    public void setTimestamp(String timestamp) { this.timestamp = timestamp; }
    
    // Main Policy Data class
    public static class PolicyData {
        @JsonProperty("_id")
        private IdInfo id;
        
        @JsonProperty("insuredInfo")
        private DetailedInsuredInfo insuredInfo;
        
        @JsonProperty("class")
        private List<String> classType;
        
        @JsonProperty("lob")
        private List<String> lob;
        
        @JsonProperty("status")
        private String status;
        
        @JsonProperty("clientId")
        private String clientId;
        
        @JsonProperty("submissionId")
        private String submissionId;
        
        @JsonProperty("createdAt")
        private String createdAt;
        
        @JsonProperty("updatedAt")
        private String updatedAt;
        
        @JsonProperty("createdBy")
        private String createdBy;
        
        @JsonProperty("updatedBy")
        private String updatedBy;
        
        @JsonProperty("businessType")
        private String businessType;
        
        @JsonProperty("premium")
        private Double premium;
        
        @JsonProperty("rate")
        private Double rate;
        
        @JsonProperty("proposalNumber")
        private String proposalNumber;
        
        @JsonProperty("proposalDate")
        private String proposalDate;
        
        @JsonProperty("underwriterName")
        private String underwriterName;
        
        @JsonProperty("additionalInsuredInfo")
        private List<AdditionalInsuredInfo> additionalInsuredInfo;
        
        @JsonProperty("brokerInfo")
        private CalculatePremiumRequest.BrokerInfo brokerInfo;
        
        @JsonProperty("commodityTypes")
        private List<CommodityType> commodityTypes;
        
        @JsonProperty("conveyance")
        private Map<String, Object> conveyance;
        
        @JsonProperty("geographicalArea")
        private List<GeographicalArea> geographicalArea;
        
        @JsonProperty("geographicalScope")
        private GeographicalScope geographicalScope;
        
        @JsonProperty("goodsInsured")
        private GoodsInsured goodsInsured;
        
        @JsonProperty("lossHistory")
        private LossHistory lossHistory;
        
        @JsonProperty("premiumSummary")
        private PremiumSummary premiumSummary;
        
        @JsonProperty("termsAndConditions")
        private Map<String, List<TermsCondition>> termsAndConditions;
        
        @JsonProperty("valuation")
        private Valuation valuation;
        
        // Additional fields
        @JsonProperty("containerisedShipments")
        private String containerisedShipments;
        
        @JsonProperty("minimumPerCertificate")
        private String minimumPerCertificate;
        
        @JsonProperty("gfrLast12Months")
        private String gfrLast12Months;
        
        @JsonProperty("gfrNext12Months")
        private String gfrNext12Months;
        
        @JsonProperty("ffLicenceNumber")
        private String ffLicenceNumber;
        
        @JsonProperty("fmcsaNumber")
        private String fmcsaNumber;
        
        @JsonProperty("nvoccLicenceNumber")
        private String nvoccLicenceNumber;
        
        // Constructors and getters/setters
        public PolicyData() {}
        
        // Getters and Setters (showing key ones)
        public IdInfo getId() { return id; }
        public void setId(IdInfo id) { this.id = id; }
        
        public DetailedInsuredInfo getInsuredInfo() { return insuredInfo; }
        public void setInsuredInfo(DetailedInsuredInfo insuredInfo) { this.insuredInfo = insuredInfo; }
        
        public List<String> getClassType() { return classType; }
        public void setClassType(List<String> classType) { this.classType = classType; }
        
        public List<String> getLob() { return lob; }
        public void setLob(List<String> lob) { this.lob = lob; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public String getSubmissionId() { return submissionId; }
        public void setSubmissionId(String submissionId) { this.submissionId = submissionId; }
        
        public Double getPremium() { return premium; }
        public void setPremium(Double premium) { this.premium = premium; }
        
        public Double getRate() { return rate; }
        public void setRate(Double rate) { this.rate = rate; }
        
        public String getProposalNumber() { return proposalNumber; }
        public void setProposalNumber(String proposalNumber) { this.proposalNumber = proposalNumber; }
        
        public String getUnderwriterName() { return underwriterName; }
        public void setUnderwriterName(String underwriterName) { this.underwriterName = underwriterName; }
        
        public PremiumSummary getPremiumSummary() { return premiumSummary; }
        public void setPremiumSummary(PremiumSummary premiumSummary) { this.premiumSummary = premiumSummary; }
    }
    
    // Detailed Insured Info class
    public static class DetailedInsuredInfo {
        @JsonProperty("_id")
        private IdInfo id;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("businessName")
        private String businessName;
        
        @JsonProperty("insuredId")
        private String insuredId;
        
        @JsonProperty("clientId")
        private String clientId;
        
        @JsonProperty("insuredNumber")
        private Integer insuredNumber;
        
        @JsonProperty("insuredClientId")
        private String insuredClientId;
        
        @JsonProperty("address")
        private String address;
        
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("zipcode")
        private String zipcode;
        
        @JsonProperty("effectiveDate")
        private String effectiveDate;
        
        @JsonProperty("expiryDate")
        private String expiryDate;
        
        @JsonProperty("noOfEmployees")
        private String noOfEmployees;
        
        @JsonProperty("riskManager")
        private String riskManager;
        
        @JsonProperty("yearsOnExperience")
        private String yearsOnExperience;
        
        @JsonProperty("companyAddress")
        private String companyAddress;
        
        @JsonProperty("companyName")
        private String companyName;
        
        // Constructors and getters/setters
        public DetailedInsuredInfo() {}
        
        public String getBusinessName() { return businessName; }
        public void setBusinessName(String businessName) { this.businessName = businessName; }
        
        public String getInsuredId() { return insuredId; }
        public void setInsuredId(String insuredId) { this.insuredId = insuredId; }
        
        public String getState() { return state; }
        public void setState(String state) { this.state = state; }
        
        public String getEffectiveDate() { return effectiveDate; }
        public void setEffectiveDate(String effectiveDate) { this.effectiveDate = effectiveDate; }
        
        public String getExpiryDate() { return expiryDate; }
        public void setExpiryDate(String expiryDate) { this.expiryDate = expiryDate; }
    }
    
    // ID Info class
    public static class IdInfo {
        @JsonProperty("timestamp")
        private Long timestamp;
        
        @JsonProperty("date")
        private String date;
        
        public IdInfo() {}
        
        public Long getTimestamp() { return timestamp; }
        public void setTimestamp(Long timestamp) { this.timestamp = timestamp; }
        
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
    }
    
    // Additional Insured Info class
    public static class AdditionalInsuredInfo {
        @JsonProperty("id")
        private String id;
        
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("address")
        private String address;
        
        @JsonProperty("city")
        private String city;
        
        @JsonProperty("state")
        private String state;
        
        @JsonProperty("zipcode")
        private String zipcode;
        
        public AdditionalInsuredInfo() {}
        
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
    }
}
