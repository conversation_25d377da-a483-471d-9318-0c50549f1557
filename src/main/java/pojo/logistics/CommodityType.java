package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class CommodityType {
    
    @JsonProperty("label")
    private String label;
    
    @JsonProperty("percentage")
    private String percentage;
    
    @JsonProperty("key")
    private String key;
    
    // Constructors
    public CommodityType() {}
    
    public CommodityType(String label, String percentage, String key) {
        this.label = label;
        this.percentage = percentage;
        this.key = key;
    }
    
    // Getters and Setters
    public String getLabel() {
        return label;
    }
    
    public void setLabel(String label) {
        this.label = label;
    }
    
    public String getPercentage() {
        return percentage;
    }
    
    public void setPercentage(String percentage) {
        this.percentage = percentage;
    }
    
    public String getKey() {
        return key;
    }
    
    public void setKey(String key) {
        this.key = key;
    }
    
    @Override
    public String toString() {
        return "CommodityType{" +
                "label='" + label + '\'' +
                ", percentage='" + percentage + '\'' +
                ", key='" + key + '\'' +
                '}';
    }
}
