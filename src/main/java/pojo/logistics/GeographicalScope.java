package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GeographicalScope {
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("statement")
    private String statement;
    
    @JsonProperty("statement2")
    private String statement2;
    
    @JsonProperty("statement3")
    private String statement3;
    
    // Constructors
    public GeographicalScope() {}
    
    public GeographicalScope(String title, String statement, String statement2, String statement3) {
        this.title = title;
        this.statement = statement;
        this.statement2 = statement2;
        this.statement3 = statement3;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getStatement() {
        return statement;
    }
    
    public void setStatement(String statement) {
        this.statement = statement;
    }
    
    public String getStatement2() {
        return statement2;
    }
    
    public void setStatement2(String statement2) {
        this.statement2 = statement2;
    }
    
    public String getStatement3() {
        return statement3;
    }
    
    public void setStatement3(String statement3) {
        this.statement3 = statement3;
    }
    
    @Override
    public String toString() {
        return "GeographicalScope{" +
                "title='" + title + '\'' +
                ", statement='" + statement + '\'' +
                ", statement2='" + statement2 + '\'' +
                ", statement3='" + statement3 + '\'' +
                '}';
    }
}
