package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class TermsCondition {
    
    @JsonProperty("value")
    private String value;
    
    @JsonProperty("id")
    private String id;
    
    // Constructors
    public TermsCondition() {}
    
    public TermsCondition(String value, String id) {
        this.value = value;
        this.id = id;
    }
    
    // Getters and Setters
    public String getValue() {
        return value;
    }
    
    public void setValue(String value) {
        this.value = value;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    @Override
    public String toString() {
        return "TermsCondition{" +
                "value='" + value + '\'' +
                ", id='" + id + '\'' +
                '}';
    }
}
