package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class LossHistory {
    
    @JsonProperty("totalRaterPremium")
    private String totalRaterPremium;
    
    @JsonProperty("raterPremiumLossPick")
    private String raterPremiumLossPick;
    
    @JsonProperty("lossAdjustedPremiumLossPick")
    private String lossAdjustedPremiumLossPick;
    
    @JsonProperty("shipperInterestCargo")
    private LossDetail shipperInterestCargo;
    
    @JsonProperty("shipperInterestWarehouse")
    private LossDetail shipperInterestWarehouse;
    
    @JsonProperty("cargoLiabilities")
    private LossDetail cargoLiabilities;
    
    @JsonProperty("warehouseLegalLiability")
    private LossDetail warehouseLegalLiability;
    
    @JsonProperty("excessWarehouseOperatorLegalLiability")
    private LossDetail excessWarehouseOperatorLegalLiability;
    
    @JsonProperty("lossHistoryLastFiveYearLosses")
    private List<String> lossHistoryLastFiveYearLosses;
    
    @JsonProperty("totalLossHistoryLastFiveYearLosses")
    private String totalLossHistoryLastFiveYearLosses;
    
    @JsonProperty("averageLossHistoryLastFiveYearLosses")
    private String averageLossHistoryLastFiveYearLosses;
    
    // Constructors
    public LossHistory() {}
    
    // Getters and Setters
    public String getTotalRaterPremium() {
        return totalRaterPremium;
    }
    
    public void setTotalRaterPremium(String totalRaterPremium) {
        this.totalRaterPremium = totalRaterPremium;
    }
    
    public String getRaterPremiumLossPick() {
        return raterPremiumLossPick;
    }
    
    public void setRaterPremiumLossPick(String raterPremiumLossPick) {
        this.raterPremiumLossPick = raterPremiumLossPick;
    }
    
    public String getLossAdjustedPremiumLossPick() {
        return lossAdjustedPremiumLossPick;
    }
    
    public void setLossAdjustedPremiumLossPick(String lossAdjustedPremiumLossPick) {
        this.lossAdjustedPremiumLossPick = lossAdjustedPremiumLossPick;
    }
    
    public LossDetail getShipperInterestCargo() {
        return shipperInterestCargo;
    }
    
    public void setShipperInterestCargo(LossDetail shipperInterestCargo) {
        this.shipperInterestCargo = shipperInterestCargo;
    }
    
    public LossDetail getShipperInterestWarehouse() {
        return shipperInterestWarehouse;
    }
    
    public void setShipperInterestWarehouse(LossDetail shipperInterestWarehouse) {
        this.shipperInterestWarehouse = shipperInterestWarehouse;
    }
    
    public LossDetail getCargoLiabilities() {
        return cargoLiabilities;
    }
    
    public void setCargoLiabilities(LossDetail cargoLiabilities) {
        this.cargoLiabilities = cargoLiabilities;
    }
    
    public LossDetail getWarehouseLegalLiability() {
        return warehouseLegalLiability;
    }
    
    public void setWarehouseLegalLiability(LossDetail warehouseLegalLiability) {
        this.warehouseLegalLiability = warehouseLegalLiability;
    }
    
    public LossDetail getExcessWarehouseOperatorLegalLiability() {
        return excessWarehouseOperatorLegalLiability;
    }
    
    public void setExcessWarehouseOperatorLegalLiability(LossDetail excessWarehouseOperatorLegalLiability) {
        this.excessWarehouseOperatorLegalLiability = excessWarehouseOperatorLegalLiability;
    }
    
    public List<String> getLossHistoryLastFiveYearLosses() {
        return lossHistoryLastFiveYearLosses;
    }
    
    public void setLossHistoryLastFiveYearLosses(List<String> lossHistoryLastFiveYearLosses) {
        this.lossHistoryLastFiveYearLosses = lossHistoryLastFiveYearLosses;
    }
    
    public String getTotalLossHistoryLastFiveYearLosses() {
        return totalLossHistoryLastFiveYearLosses;
    }
    
    public void setTotalLossHistoryLastFiveYearLosses(String totalLossHistoryLastFiveYearLosses) {
        this.totalLossHistoryLastFiveYearLosses = totalLossHistoryLastFiveYearLosses;
    }
    
    public String getAverageLossHistoryLastFiveYearLosses() {
        return averageLossHistoryLastFiveYearLosses;
    }
    
    public void setAverageLossHistoryLastFiveYearLosses(String averageLossHistoryLastFiveYearLosses) {
        this.averageLossHistoryLastFiveYearLosses = averageLossHistoryLastFiveYearLosses;
    }
    
    // Inner class for loss details
    public static class LossDetail {
        @JsonProperty("lossAdjustedPremium")
        private Double lossAdjustedPremium;
        
        @JsonProperty("lossPremium")
        private Double lossPremium;
        
        @JsonProperty("lossDebitCredit")
        private String lossDebitCredit;
        
        // Constructors
        public LossDetail() {}
        
        // Getters and Setters
        public Double getLossAdjustedPremium() {
            return lossAdjustedPremium;
        }
        
        public void setLossAdjustedPremium(Double lossAdjustedPremium) {
            this.lossAdjustedPremium = lossAdjustedPremium;
        }
        
        public Double getLossPremium() {
            return lossPremium;
        }
        
        public void setLossPremium(Double lossPremium) {
            this.lossPremium = lossPremium;
        }
        
        public String getLossDebitCredit() {
            return lossDebitCredit;
        }
        
        public void setLossDebitCredit(String lossDebitCredit) {
            this.lossDebitCredit = lossDebitCredit;
        }
    }
}
