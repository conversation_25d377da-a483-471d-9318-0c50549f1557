package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class GoodsInsured {
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("statement")
    private String statement;
    
    // Constructors
    public GoodsInsured() {}
    
    public GoodsInsured(String title, String statement) {
        this.title = title;
        this.statement = statement;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getStatement() {
        return statement;
    }
    
    public void setStatement(String statement) {
        this.statement = statement;
    }
    
    @Override
    public String toString() {
        return "GoodsInsured{" +
                "title='" + title + '\'' +
                ", statement='" + statement + '\'' +
                '}';
    }
}
