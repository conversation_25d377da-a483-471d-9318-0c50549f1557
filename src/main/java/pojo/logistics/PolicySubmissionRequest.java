package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class PolicySubmissionRequest {
    
    @JsonProperty("insuredInfo")
    private InsuredInfo insuredInfo;
    
    @JsonProperty("class")
    private List<String> classType;
    
    @JsonProperty("lob")
    private List<String> lob;
    
    @JsonProperty("status")
    private String status;
    
    // Constructors
    public PolicySubmissionRequest() {}
    
    public PolicySubmissionRequest(InsuredInfo insuredInfo, List<String> classType, List<String> lob, String status) {
        this.insuredInfo = insuredInfo;
        this.classType = classType;
        this.lob = lob;
        this.status = status;
    }
    
    // Getters and Setters
    public InsuredInfo getInsuredInfo() {
        return insuredInfo;
    }
    
    public void setInsuredInfo(InsuredInfo insuredInfo) {
        this.insuredInfo = insuredInfo;
    }
    
    public List<String> getClassType() {
        return classType;
    }
    
    public void setClassType(List<String> classType) {
        this.classType = classType;
    }
    
    public List<String> getLob() {
        return lob;
    }
    
    public void setLob(List<String> lob) {
        this.lob = lob;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "PolicySubmissionRequest{" +
                "insuredInfo=" + insuredInfo +
                ", classType=" + classType +
                ", lob=" + lob +
                ", status='" + status + '\'' +
                '}';
    }
    
    // Inner class for InsuredInfo
    public static class InsuredInfo {
        @JsonProperty("name")
        private String name;
        
        @JsonProperty("businessName")
        private String businessName;
        
        // Constructors
        public InsuredInfo() {}
        
        public InsuredInfo(String name, String businessName) {
            this.name = name;
            this.businessName = businessName;
        }
        
        // Getters and Setters
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getBusinessName() {
            return businessName;
        }
        
        public void setBusinessName(String businessName) {
            this.businessName = businessName;
        }
        
        @Override
        public String toString() {
            return "InsuredInfo{" +
                    "name='" + name + '\'' +
                    ", businessName='" + businessName + '\'' +
                    '}';
        }
    }
}
