package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

public class PolicySearchRequest {
    
    @JsonProperty("freeText")
    private String freeText;
    
    @JsonProperty("productCode")
    private List<String> productCode;
    
    @JsonProperty("underwriters")
    private List<String> underwriters;
    
    @JsonProperty("logisticStatus")
    private List<String> logisticStatus;
    
    // Constructors
    public PolicySearchRequest() {}
    
    public PolicySearchRequest(String freeText, List<String> productCode, List<String> underwriters, List<String> logisticStatus) {
        this.freeText = freeText;
        this.productCode = productCode;
        this.underwriters = underwriters;
        this.logisticStatus = logisticStatus;
    }
    
    // Getters and Setters
    public String getFreeText() {
        return freeText;
    }
    
    public void setFreeText(String freeText) {
        this.freeText = freeText;
    }
    
    public List<String> getProductCode() {
        return productCode;
    }
    
    public void setProductCode(List<String> productCode) {
        this.productCode = productCode;
    }
    
    public List<String> getUnderwriters() {
        return underwriters;
    }
    
    public void setUnderwriters(List<String> underwriters) {
        this.underwriters = underwriters;
    }
    
    public List<String> getLogisticStatus() {
        return logisticStatus;
    }
    
    public void setLogisticStatus(List<String> logisticStatus) {
        this.logisticStatus = logisticStatus;
    }
    
    @Override
    public String toString() {
        return "PolicySearchRequest{" +
                "freeText='" + freeText + '\'' +
                ", productCode=" + productCode +
                ", underwriters=" + underwriters +
                ", logisticStatus=" + logisticStatus +
                '}';
    }
}
