package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class Valuation {
    
    @JsonProperty("title")
    private String title;
    
    @JsonProperty("subTitle1")
    private String subTitle1;
    
    @JsonProperty("subTitle2")
    private String subTitle2;
    
    @JsonProperty("statement")
    private String statement;
    
    @JsonProperty("statement2")
    private String statement2;
    
    @JsonProperty("statement3")
    private String statement3;
    
    // Constructors
    public Valuation() {}
    
    public Valuation(String title, String subTitle1, String subTitle2, String statement, String statement2, String statement3) {
        this.title = title;
        this.subTitle1 = subTitle1;
        this.subTitle2 = subTitle2;
        this.statement = statement;
        this.statement2 = statement2;
        this.statement3 = statement3;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getSubTitle1() {
        return subTitle1;
    }
    
    public void setSubTitle1(String subTitle1) {
        this.subTitle1 = subTitle1;
    }
    
    public String getSubTitle2() {
        return subTitle2;
    }
    
    public void setSubTitle2(String subTitle2) {
        this.subTitle2 = subTitle2;
    }
    
    public String getStatement() {
        return statement;
    }
    
    public void setStatement(String statement) {
        this.statement = statement;
    }
    
    public String getStatement2() {
        return statement2;
    }
    
    public void setStatement2(String statement2) {
        this.statement2 = statement2;
    }
    
    public String getStatement3() {
        return statement3;
    }
    
    public void setStatement3(String statement3) {
        this.statement3 = statement3;
    }
    
    @Override
    public String toString() {
        return "Valuation{" +
                "title='" + title + '\'' +
                ", subTitle1='" + subTitle1 + '\'' +
                ", subTitle2='" + subTitle2 + '\'' +
                ", statement='" + statement + '\'' +
                ", statement2='" + statement2 + '\'' +
                ", statement3='" + statement3 + '\'' +
                '}';
    }
}
