package pojo.logistics;

import com.fasterxml.jackson.annotation.JsonProperty;

public class PremiumSummary {
    
    @JsonProperty("warehouseLegalLiabilityPremiumSummary")
    private Double warehouseLegalLiabilityPremiumSummary;
    
    @JsonProperty("cargoLiabilitiesPremiumSummary")
    private Double cargoLiabilitiesPremiumSummary;
    
    @JsonProperty("triaPremium")
    private String triaPremium;
    
    @JsonProperty("shipperInterestPremiumSummary")
    private Double shipperInterestPremiumSummary;
    
    @JsonProperty("totalPremium")
    private Double totalPremium;
    
    @JsonProperty("partnerNetPremium")
    private Double partnerNetPremium;
    
    @JsonProperty("excessWarehouseOperatorLegalLiabilityPremiumSummary")
    private Double excessWarehouseOperatorLegalLiabilityPremiumSummary;
    
    @JsonProperty("blueHavenUWCommission")
    private Double blueHavenUWCommission;
    
    @JsonProperty("brokerCommission")
    private Double brokerCommission;
    
    // Constructors
    public PremiumSummary() {}
    
    // Getters and Setters
    public Double getWarehouseLegalLiabilityPremiumSummary() {
        return warehouseLegalLiabilityPremiumSummary;
    }
    
    public void setWarehouseLegalLiabilityPremiumSummary(Double warehouseLegalLiabilityPremiumSummary) {
        this.warehouseLegalLiabilityPremiumSummary = warehouseLegalLiabilityPremiumSummary;
    }
    
    public Double getCargoLiabilitiesPremiumSummary() {
        return cargoLiabilitiesPremiumSummary;
    }
    
    public void setCargoLiabilitiesPremiumSummary(Double cargoLiabilitiesPremiumSummary) {
        this.cargoLiabilitiesPremiumSummary = cargoLiabilitiesPremiumSummary;
    }
    
    public String getTriaPremium() {
        return triaPremium;
    }
    
    public void setTriaPremium(String triaPremium) {
        this.triaPremium = triaPremium;
    }
    
    public Double getShipperInterestPremiumSummary() {
        return shipperInterestPremiumSummary;
    }
    
    public void setShipperInterestPremiumSummary(Double shipperInterestPremiumSummary) {
        this.shipperInterestPremiumSummary = shipperInterestPremiumSummary;
    }
    
    public Double getTotalPremium() {
        return totalPremium;
    }
    
    public void setTotalPremium(Double totalPremium) {
        this.totalPremium = totalPremium;
    }
    
    public Double getPartnerNetPremium() {
        return partnerNetPremium;
    }
    
    public void setPartnerNetPremium(Double partnerNetPremium) {
        this.partnerNetPremium = partnerNetPremium;
    }
    
    public Double getExcessWarehouseOperatorLegalLiabilityPremiumSummary() {
        return excessWarehouseOperatorLegalLiabilityPremiumSummary;
    }
    
    public void setExcessWarehouseOperatorLegalLiabilityPremiumSummary(Double excessWarehouseOperatorLegalLiabilityPremiumSummary) {
        this.excessWarehouseOperatorLegalLiabilityPremiumSummary = excessWarehouseOperatorLegalLiabilityPremiumSummary;
    }
    
    public Double getBlueHavenUWCommission() {
        return blueHavenUWCommission;
    }
    
    public void setBlueHavenUWCommission(Double blueHavenUWCommission) {
        this.blueHavenUWCommission = blueHavenUWCommission;
    }
    
    public Double getBrokerCommission() {
        return brokerCommission;
    }
    
    public void setBrokerCommission(Double brokerCommission) {
        this.brokerCommission = brokerCommission;
    }
    
    @Override
    public String toString() {
        return "PremiumSummary{" +
                "warehouseLegalLiabilityPremiumSummary=" + warehouseLegalLiabilityPremiumSummary +
                ", cargoLiabilitiesPremiumSummary=" + cargoLiabilitiesPremiumSummary +
                ", triaPremium='" + triaPremium + '\'' +
                ", shipperInterestPremiumSummary=" + shipperInterestPremiumSummary +
                ", totalPremium=" + totalPremium +
                ", partnerNetPremium=" + partnerNetPremium +
                ", excessWarehouseOperatorLegalLiabilityPremiumSummary=" + excessWarehouseOperatorLegalLiabilityPremiumSummary +
                ", blueHavenUWCommission=" + blueHavenUWCommission +
                ", brokerCommission=" + brokerCommission +
                '}';
    }
}
