package pojo.graphql;


public class InsuredQueries {
    public    final static String CREATE_INSURED="mutation CreateCustomer( $businessType: String!, $businessName: String!, $dba: String!, $createAddressInput: CreateAddressInputCS! ) { createCustomer( input: { businessType: $businessType businessName: $businessName dba: $dba createAddressInput: $createAddressInput } ) { id clientId businessType businessName dba isDeleted createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 officeType createdAt updatedAt } } } ";
    public   final static String UPDATE_INSURED="mutation UpdateCustomer( $id: ID!, $businessType: String!, $businessName: String!, $dba: String!, $createAddressInput:UpdateAddressInputCS!) { updateCustomer( input: { id: $id businessType: $businessType businessName: $businessName dba: $dba updateAddressInput: $createAddressInput } ) { id clientId businessType businessName dba isDeleted createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 officeType createdAt updatedAt } } }";
    public   final static String DELETE_INSURED="mutation DeleteCustomer { deleteCustomer(input: \"insuredID\") } ";
    public   final static String SEARCH_INSURED="query SearchCustomer($input: SearchCustomerInput!) {  searchCustomer(input: $input) {    totalElements    pageableInfo {      page      size      __typename    }    items {      businessName      businessType      clientId      address {        addressLine1        addressLine2        city        country        createdAt        id        officeType        state        updatedAt        zipcode        __typename      }      createdAt      dba      id      isDeleted      updatedAt      __typename    }    __typename  }}";

}
