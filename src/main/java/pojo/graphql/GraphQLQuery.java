package pojo.graphql;

public class GraphQLQuery {
    public String getQuery() {
        return query;
    }

    public Object getVariables() {
        return variables;
    }

    private  String query;
    private  Object variables;
//    private  String mutation;
//    public String getMutation() {
//        return mutation;
//    }

//    public void setMutation(String mutation) {
//        this.mutation = mutation;
//    }

    public void setVariables(Object variable){
        this.variables=variable;
    }

    public void setQuery(String query){
        this.query=query;
    }
}
