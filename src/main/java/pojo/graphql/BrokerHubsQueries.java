package pojo.graphql;

public class BrokerHubsQueries {
//    public final static String CREATE_BROKERAGE="mutation CreateBrokerage($name:String!,$brokercode:String!,$businessType:String!,$defaultCommission:Float) { createBrokerage( input: { name: $name brokerCode: $brokercode businessType: $businessType defaultCommission: $defaultCommission } ) { id clientId name brokerCode businessType defaultCommission createdAt updatedAt } } ";
   public final static String CREATE_BROKERAGE="mutation CreateBrokerage($name:String!,$brokercode:String!,$businessType:String!,$defaultCommission:Float) { createBrokerage( input: { name: $name brokerCode: $brokercode businessType: $businessType defaultCommission: $defaultCommission } ) { id clientId name brokerCode businessType defaultCommission createdAt updatedAt } } ";
    public final static String UPDATE_BROKERAGE="mutation UpdateBrokerage($id: ID!,$name:String!,$brokercode:String!,$businessType:String!,$defaultCommission:Float) { updateBrokerage( input: { name: $name brokerCode: $brokercode businessType: $businessType defaultCommission: $defaultCommission ,id:$id} ) { id clientId name brokerCode businessType defaultCommission createdAt updatedAt offices { id clientId officeName email createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } brokers { id clientId firstName lastName workPhone mobilePhone emailAddress team legalEntity jobTitle filingBroker surplusLinesBrokerageName surplusLinesAddress surplusLinesCity surplusLinesState surplusLinesZipCode surplusLinesLicenseNumber status createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } } } } }";
   public final static String SEARCH_BROKERAGE="{\"searchText\":\"a\"}";
    public final static String DELETE_BROKERAGE="mutation DeleteBrokerage { deleteBrokerage(input: \"brokerageID\") }";

    public final static String CREATE_OFFICE="mutation CreateOffice ($officeName:String!,$createAddressInput:CreateAddressInputBS!,$email:String!,$brokerageId:ID!){ createOffice( input: { officeName: $officeName createAddressInput: $createAddressInput email: $email brokerageId: $brokerageId } ) { id clientId officeName email createdAt updatedAt } } ";
    public final static String UPDATE_OFFICE="mutation UpdateOffice ($officeName:String!,$createAddressInput:UpdateAddressInputBS!,$email:String!,$id:ID!){ updateOffice( input: { id: $id updateAddressInput:$createAddressInput email:$email officeName:$officeName } ) { id clientId officeName email createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } brokers { id clientId firstName lastName workPhone mobilePhone emailAddress team legalEntity jobTitle filingBroker surplusLinesBrokerageName surplusLinesAddress surplusLinesCity surplusLinesState surplusLinesZipCode surplusLinesLicenseNumber status createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } } } } ";
    public final static String DELETE_OFFICE="mutation DeleteOffice { deleteOffice(input: \"OfficeID\") } ";
    public  final static String SEARCH_OFFICE="query SearchOffice { searchOffice( input: { officeName: \"test\" brokerageId: null brokerageName: null searchAddressInput: { id: null, city: null, zipcode: null } page: 0 size: 10 } ) { totalElements items { brokerageId office { id officeName address { addressLine1 addressLine2 addressType city country state zipcode __typename } email __typename } __typename } pageableInfo { page size __typename } __typename } }";

    public final static String CREATE_BROKER="mutation CreateBroker( $firstName: String!, $lastName: String!, $workPhone: String!, $mobilePhone: String!, $officeId: ID!, $emailAddress: String!, $brokerageId: ID!, $team: String!, $createAddressInput: CreateAddressInputBS!, $status: String!,$filingBroker:Boolean ) { createBroker( input: { firstName: $firstName lastName: $lastName workPhone: $workPhone mobilePhone: $mobilePhone officeId: $officeId emailAddress: $emailAddress brokerageId: $brokerageId team: $team legalEntity: null jobTitle: null filingBroker:$filingBroker  surplusLinesBrokerageName: null surplusLinesAddress: null surplusLinesCity: null surplusLinesState: null surplusLinesZipCode: null surplusLinesLicenseNumber: null createAddressInput: $createAddressInput status: $status } ) { id clientId firstName lastName workPhone mobilePhone emailAddress team legalEntity jobTitle filingBroker surplusLinesBrokerageName surplusLinesAddress surplusLinesCity surplusLinesState surplusLinesZipCode surplusLinesLicenseNumber status createdAt updatedAt } }";
    public final static String UPDATE_BROKER="mutation UpdateBroker( $id:ID!, $firstName: String!, $lastName: String!, $workPhone: String!, $mobilePhone: String!, $officeId: ID!, $emailAddress: String!, $brokerageId: ID!, $team: String!, $createAddressInput: UpdateAddressInputBS!, $status: String! ) { updateBroker( input: { id:$id firstName: $firstName lastName: $lastName workPhone: $workPhone mobilePhone: $mobilePhone officeId: $officeId emailAddress: $emailAddress brokerageId: $brokerageId team: $team legalEntity: null jobTitle: null filingBroker: false surplusLinesBrokerageName: null surplusLinesAddress: null surplusLinesCity: null surplusLinesState: null surplusLinesZipCode: null surplusLinesLicenseNumber: null updateAddressInput: $createAddressInput status: $status \n" +
            "\n" +
            " } ) { id clientId firstName lastName workPhone mobilePhone emailAddress team legalEntity jobTitle filingBroker surplusLinesBrokerageName surplusLinesAddress surplusLinesCity surplusLinesState surplusLinesZipCode surplusLinesLicenseNumber status createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } } } ";
    public final static String DELETE_BROKER="mutation DeleteBroker {\n" +
            "    deleteBroker(input: \"brokerID\")\n" +
            "}";
    public final static String SEARCH_BROKER="query SearchBroker( $id: ID, $brokerageNames: [String], $brokerageOfficeCities: [String], $searchText: String, $sortInput: String, $sortOrder: String, $page: Int, $size: Int ) { searchBroker( input: { id: $id brokerageNames: $brokerageNames brokerageOfficeCities: $brokerageOfficeCities searchText: $searchText sortInput: $sortInput sortOrder: $sortOrder page: $page size: $size } ) { totalElements pageableInfo { page size } items { brokerageId brokerageName officeId officeCity broker { id clientId firstName lastName workPhone mobilePhone emailAddress team legalEntity jobTitle filingBroker surplusLinesBrokerageName surplusLinesAddress surplusLinesCity surplusLinesState surplusLinesZipCode surplusLinesLicenseNumber status createdAt updatedAt address { id country state city zipcode addressLine1 addressLine2 addressType createdAt updatedAt } } } } }";



}
