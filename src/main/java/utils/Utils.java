package utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Random;

public class Utils {

    private static final Logger log = LogManager.getLogger(utils.Utils.class);

    private Utils() {
    }

    /**
     * Returns the file path for the given file name
     */

    public static String generateRandomAlphabetChars(int length) {
        String SALTCHARS = "abcdefghijklmnopqrstuvwxyz";
        StringBuilder salt = new StringBuilder();
        Random r = new Random();
        while (salt.length() < length) { // length of the random string.
            int index = (int) (r.nextFloat() * SALTCHARS.length());
            salt.append(SALTCHARS.charAt(index));
        }
        return salt.substring(0, 1).toUpperCase() + salt.substring(1);
    }

    public static String generateRandomNumbers(int length) {
        String SALTCHARS = "123456789";
        StringBuilder salt = new StringBuilder();
        Random r = new Random();
        while (salt.length() < length) { // length of the random string.
            int index = (int) (r.nextFloat() * SALTCHARS.length());
            salt.append(SALTCHARS.charAt(index));
        }
        return salt.toString();
    }

    public static String generateRandomAlphaNumeric(int length) {
        String SALTCHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890";
        StringBuilder salt = new StringBuilder();
        Random r = new Random();
        while (salt.length() < length) { // length of the random string.
            int index = (int) (r.nextFloat() * SALTCHARS.length());
            salt.append(SALTCHARS.charAt(index));
        }
        return salt.toString();
    }

    public static String generateRandomSpecialCharacters(int length) {
        String SALTCHARS = "!@£$%^&*<>";
        StringBuilder salt = new StringBuilder();
        Random r = new Random();
        while (salt.length() < length) { // length of the random string.
            int index = (int) (r.nextFloat() * SALTCHARS.length());
            salt.append(SALTCHARS.charAt(index));
        }
        return salt.toString();
    }


    public static String getCurrentDate(String Pattern){
        DateTimeFormatter dtf = DateTimeFormatter.ofPattern(Pattern);
        LocalDateTime now = LocalDateTime.now();
        String currentDate = dtf.format(now);
        return currentDate;

    }


}

