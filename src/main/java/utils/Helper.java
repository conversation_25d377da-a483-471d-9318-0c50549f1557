package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.FileReader;
import java.io.IOException;
import java.util.List;

public class Helper {

    public static boolean isSorted(List<String> list)
    {
        boolean sorted = true;
        for (int i = 1; i < list.size(); i++) {
            if (list.get(i-1).compareTo(list.get(i)) > 0) sorted = false;
        }
        return sorted;
    }
    public static JsonNode readJsonNodeObjects(String requestPayloadPath) throws IOException {
        FileReader fileReader = new FileReader(requestPayloadPath);
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readTree(fileReader);
    }
}
