//package utils;
//
//import com.fasterxml.jackson.databind.JsonNode;
//import com.fasterxml.jackson.databind.node.ObjectNode;
//import io.cucumber.datatable.DataTable;
//import org.apache.logging.log4j.LogManager;
//import org.apache.logging.log4j.Logger;
//
//import java.io.BufferedReader;
//import java.io.InputStreamReader;
//import java.net.URL;
//import java.net.URLConnection;
//import java.util.LinkedHashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.regex.Matcher;
//
//public class ApiUtils
//{
//    private static Logger log = LogManager.getLogger(ApiUtils.class);
//
//    public static String replacePlaceholders(String input)
//    {
////        String output = input
////                .replaceAll("STARTDATETIME", Utils.getFirstDayOfYear("yyyy-MM-dd HH:mm:ss"))
////                .replaceAll("ENDDATETIME", Utils.getLastDayOfYear("yyyy-MM-dd HH:mm:ss"))
////                .replaceAll("STARTDATE", Utils.getFirstDayOfYear("yyyy-MM-dd"))
////                .replaceAll("ENDDATE", Utils.getLastDayOfYear("yyyy-MM-dd"))
////                .replaceAll("TODAYMINUSONEYEAR", Utils.getTodaysDatePlusNDaysMonthsYears(Constants.YEAR, -1, "yyyy-MM-dd"))
////                .replaceAll("TODAY", Utils.getCurrentDate("yyyy-MM-dd"))
////                .replaceAll("ACCOUNTID", String.valueOf(Account.getAccountId()))
////                .replaceAll("POLICYID", Policy.getPolicyId())
////                .replaceAll("ACCOUNTNAME", Account.getAccountName());
////
////        log.debug("Placeholder output: " + output);
////        return output;
//    }
//
//    /**
//     * Take a generic JSON payload file, update it with the property values that need to be changed for the test in progress,
//     * then return it as a new JSON file.
//     * @param jsonFile generic payload file to be used as a template for output data
//     * @param dataToInsert LinkedHashMap containing the property to be changed (key) and value to change it to (value).
//     * @return URL
//     * @throws Throwable
//     */
//    public static String buildJsonPayloadForRequest(String jsonFile, LinkedHashMap<String, String> dataToInsert) throws Throwable
//    {
//        URL json = Thread.currentThread().getContextClassLoader().getResource(jsonFile);
//        String returnString = "";
//        log.debug("JSON file to open: " + json.toString());
//
//        URLConnection conn = json.openConnection();
//        BufferedReader in = new BufferedReader(new InputStreamReader(conn.getInputStream()));
//        String inputLine;
//        while ((inputLine = in.readLine()) != null)
//            returnString += inputLine.trim();
//        in.close();
//
//        log.debug("URL: " + returnString);
//
//        log.debug("-----------------");
//        for (Map.Entry<String, String> map : dataToInsert.entrySet())
//        {
//            String key = map.getKey();
//            String newValue = null;
//            try
//            {
//                newValue = map.getValue();
//            }
//            catch (NullPointerException npe)
//            {
//                // Null value indicates we don't want to create/update the value (say in the case of an update).
//                // To set a payload value to null, set the LinkedHashMap's value to "NULL" (i.e. a string with value of "NULL").
//            }
//            log.debug("Key       : " + key);
//            log.debug("New Value : " + newValue);
//
//            if (returnString.contains(key) && newValue != null)
//            {
//                // Define property and property value
//                int propertyEndIndex = returnString.indexOf(key) + key.length();
//                String property = returnString.substring(returnString.indexOf(key), propertyEndIndex);
//                String oldValue;
//                log.debug("Property  : " + property);
//
//                // Cater for array objects in payload
//                if (newValue.startsWith("[") && newValue.endsWith("]") ||
//                    returnString.substring(propertyEndIndex + 3).startsWith("["))
//                {
//                    oldValue = returnString.substring(
//                                    propertyEndIndex + 2,
//                            returnString.indexOf("]", propertyEndIndex) + 1);
//                }
//                else if (newValue.contains("{") && newValue.contains("}") ||
//                        returnString.substring(propertyEndIndex + 3).startsWith("{"))
//                {
//                    oldValue = returnString.substring(
//                            propertyEndIndex + 2,
//                            returnString.indexOf("}", propertyEndIndex) + 1);
//                }
//                else
//                {
//                    try
//                    {
//                        oldValue = returnString.substring(
//                                        propertyEndIndex + 3,
//                                        returnString.indexOf(",", propertyEndIndex)).replaceAll("\"",""); // remove trailing comma and quotation marks
////                        oldValue = StringUtils.remove(oldValue, "}");
//                    }
//                    catch (IndexOutOfBoundsException ioobe)
//                    {
//                        // This occurs if we are editing the last variable in the payload, so catch exception and get index of bracket instead.
//                        oldValue = returnString.substring(
//                                propertyEndIndex + 3,
//                                returnString.indexOf("}", propertyEndIndex)).replaceAll("\"",""); // remove trailing comma and quotation marks
//                    }
//                }
//
//                // Replace values
//                if (!newValue.equals("NULL"))
//                    returnString = returnString.replaceFirst("\\Q" +  oldValue.trim() + "\\E", Matcher.quoteReplacement(newValue));
//                else
//                    returnString = returnString.replaceFirst("\\Q" + oldValue.trim() + "\\E", "null");
//
//                log.debug("Old Value: " + oldValue);
//            }
//            log.debug("-----------------");
//        }
//
//        log.debug("Final return string: " + returnString);
//
//        return returnString;
//    }
//
//    public static String buildJsonPayloadForRequest(String jsonFile, LinkedHashMap<String, String> dataToInsert, boolean useOriginalMethod) throws Throwable
//    {
//        if (useOriginalMethod)
//            return buildJsonPayloadForRequest(jsonFile, dataToInsert);
//        else
//        {
//            log.debug("JSON file to open: " + jsonFile);
//            JsonNode jsonPayload = Helper.readJsonNodeObjects(jsonFile);
//
//            for (String key : dataToInsert.keySet())
//                ((ObjectNode)jsonPayload).put(key, dataToInsert.get(key));
//
//            log.debug("Final return string: " + jsonPayload);
//            return jsonPayload.toString();
//        }
//    }
//
//
//    /**
//     * Sets a property's value based off a Cucumber Scenario's DataTable. The value set here will become the value that
//     * gets set in the request's payload before the request is run.
//     * <br><br>
//     * Usage: <br>
//     * linkedHashMap.put("startDate", ApiUtils.setPropertyValue(dataTable, 0, Utils.getFirstDayOfYear());
//     * <br><br>
//     * @param dataTable DataTable as received from the Scenario in the feature file
//     * @param index position of the value in the Scenario table
//     * @param defaultValue default value to set the property to if "DEFAULT" is specified in the Scenario
//     * @return
//     */
//    public static String setPropertyValue(DataTable dataTable, int index, String defaultValue)
//    {
//        List<String> dataTableList = dataTable.asList();
//        String value = null;
//        String returnValue;
//        try
//        {
//            // Sets the value based on what was written in the Scenario
//            value = dataTableList.get(index);
//        }
//        catch (Exception e) {} // value being null is a legitimate use case, so catch exceptions and continue.
//
//        if (value != null)
//        {
////            if (value.equals(ApiConstants.PAYLOAD_VALUE_DEFAULT))
////                returnValue = defaultValue; // specify DEFAULT in the Scenario and the default value gets set
////            else if (value.equals(ApiConstants.PAYLOAD_VALUE_NULL))
////                returnValue = "null"; // specify NULL in the Scenario and property's value is set to null
//            else if (value.equals("PAYLOAD_VALUE_EMPTY"))
//                returnValue = "";
//            else
//                returnValue = value; // if we didn't specify any of the above, set the property's value to the value received from the Scenario
//        }
//        else
//        {
//            // If no value was specified in the Scenario, we want to leave the original value from the JSON file, so here
//            // we return null. This is picked up by the buildJsonPayloadForRequest method, which will then
//            // bypass the related property when building the payload.
//            returnValue = null;
//        }
//
//        log.debug("setPropertyValue returnValue: " + returnValue);
//        return returnValue;
//    }
//}
//
//
