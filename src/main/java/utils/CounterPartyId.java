package utils;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

public class CounterPartyId {
    private static final Logger log = LogManager.getLogger(CounterPartyId.class);

    public static List<String> COUNTER_PARTY_ID =new LinkedList<String>();


    public int getCounterPartyId(){
        return  COUNTER_PARTY_ID.size();

    }

    public static String getCounterPartyId(int no) {
        try{
            log.info("submissions CounterPartyId"+ COUNTER_PARTY_ID.size());
            Iterator<String> itr = COUNTER_PARTY_ID.iterator();
            while (itr.hasNext()) {

                log.info(itr.next());
            }}
        catch (Exception e){
            log.info(e.getMessage());
        }
        return COUNTER_PARTY_ID.get(no);

    }}
