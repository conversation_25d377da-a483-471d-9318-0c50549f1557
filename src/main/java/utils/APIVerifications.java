package utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;
import io.restassured.response.ValidatableResponse;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.hamcrest.Matchers;
import org.json.JSONArray;
import org.json.JSONObject;
import org.junit.Assert;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

public class APIVerifications {
    private static final Logger log = LogManager.getLogger(APIVerifications.class);
    private static ObjectMapper objectMapper=new ObjectMapper();

    /*
    To-DO
     */


    public static void responseKeyValidationfromArray(Response response, String key) {
        try {
            JSONArray array = new JSONArray(response.getBody().asString());
            for(int i=0; i<array.length();i++) {
                JSONObject obj = array.getJSONObject(i);
            }
        } catch (Exception e) {
            Assert.fail();
        }
    }


    public static void responseKeyValidationFromJsonObject(Response response, String key) {
        try {
            JSONObject json = new JSONObject(response.getBody().asString());
            if(json.has(key) && json.get(key)!= null) {
                log.info("Sucessfully validated value of " + key + " It is " + json.get(key));
            }else {
                Assert.assertTrue("Assert fail Key is not availble",true);

            }
        } catch (Exception e) {
            Assert.fail("Assert fail"+e.fillInStackTrace());
        }
    }

    public static void getObjectCount(Response response, String key,String Path) {
        try {
            JSONObject json = new JSONObject(response.getBody().asString());
            if(json.has(key) && json.get(key)!= null) {
                log.info("Sucessfully validated value of " + key + " It is " + json.get(key));
            }else {
                Assert.assertTrue("Assert fail Key is not availble",true);

            }
        } catch (Exception e) {
            Assert.fail("Assert fail"+e.fillInStackTrace());
        }
    }

    public static void responseTimeValidation(Response response,Long responseTime) {
        try {
            long time=response.time();
            log.info("API TIME"+time);
            Assert.assertTrue("Api response time is :: " + time,true);
            ValidatableResponse valRes = response.then();
            // Asserting response time is less than 4000 milliseconds
            valRes.time(Matchers.lessThan(responseTime));

        } catch (Exception e) {
            Assert.assertTrue("Api response time is delayed",true);
        }
    }
    public static String getErrorMsg(Response response,String errorJsonPath) {
        JsonPath jsonPathEvaluator = response.getBody().jsonPath();
        String errorMsg = jsonPathEvaluator.getString(errorJsonPath);
       return errorMsg;
    }

    public static String verifyJsonObjectContainsText(Response response,String errorJsonPath) {
        JsonPath jsonPathEvaluator = response.getBody().jsonPath();
        String errorMsg = jsonPathEvaluator.getString(errorJsonPath);
        return errorMsg;
    }
    public static int getResponseData(Response response, String errorJsonPath) {
        JsonPath jsonPathEvaluator = response.getBody().jsonPath();
        int responseObj = jsonPathEvaluator.getInt(errorJsonPath);
        return responseObj;
    }


    public static List<String> getResponseDataList(Response response, String errorJsonPath) {
        JsonPath jsonPathEvaluator = response.getBody().jsonPath();
        List<String> responseObj = jsonPathEvaluator.getList(errorJsonPath);
        return responseObj;
    }

    public static void exactJsonMatcher(Response response, String jsonPath) throws IOException {
        String jsonPathEvaluator= response.getBody().asString();
        JsonNode jsonNode1;
        JsonNode jsonNode2;
        ObjectMapper om = new ObjectMapper();

        jsonNode1 = objectMapper.readTree(jsonPathEvaluator);
        jsonNode2 = objectMapper.readTree(new String(Files.readAllBytes(Paths.get(jsonPath))));
       // Assert.assertTrue(jsonNode1.equals(jsonNode2));

    }

    public static void isSorted(List<Integer> searchList,List<Integer> sortedList, String sortOrder) {
        log.info(searchList);
        Comparator order;
        if (sortOrder == "ASC") {
            order = Comparator.nullsFirst(Comparator.reverseOrder());

        } else order = Comparator.nullsLast(Comparator.naturalOrder());
        Collections.sort(sortedList, order);
        log.info(sortedList);
        Assert.assertEquals(sortedList,searchList);
    }

}
