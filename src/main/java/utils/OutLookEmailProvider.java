package utils;

import javax.mail.Authenticator;
import javax.mail.PasswordAuthentication;
import javax.mail.Session;
import java.util.Properties;

public class OutLookEmailProvider {
    private static Session session;
    public  static Session getEmailStore(String user, String password) {
        try {

            String protocol = "imaps";
            Properties props = new Properties();
            props.setProperty("mail.store.protocol", protocol);
            props.setProperty("mail.imaps.socketFactory.class", "javax.net.ssl.SSLSocketFactory");
            props.setProperty("mail.imaps.socketFactory.fallback", "false");
            props.setProperty("mail.imaps.port", "993");
            props.setProperty("mail.imaps.socketFactory.port", "993");
           // props.put("mail.debug", "true");
            props.put("mail.smtp.host", "imap-mail.outlook.com");
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.ssl.protocols", "TLSv1.2");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.ssl.trust", "imap-mail.outlook.com");
            props.put("mail.smtp.ssl.trust", "*");

            Authenticator auth = new Authenticator() {
                public PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(user, password);
                }
            };
            session = Session.getDefaultInstance(props, auth);

            return session;
        }

        finally {

        }


    }

}



