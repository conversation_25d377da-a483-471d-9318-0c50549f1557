package utils;

public class Constant {
    public static final String TEXT_MEDIA_TYPE = "text/*";
    public static final String HTML_MEDIA_TYPE = "text/html";
    public static final String MULTIPART_ALTERNATIVE_MEDIA_TYPE = "multipart/alternative";
    public static final String MULTIPART_ALL_MEDIA_TYPE = "multipart/*";
    public static final String TEXT_PLAIN_MEDIA_TYPE = "text/plain";
    public static final String CREATE_BROKERAGE_JSON_PATH="src/main/resources/backendPayload/brokerageHubService/createBrokerage.json";
    public static final String UPDATE_BROKERAGE_JSON_PATH="src/main/resources/backendPayload/brokerageHubService/updateBrokerage.json";

    public static final String BUSINESS_LEVEL="Legal";
    public static final String ACTIVE ="ACTIVE" ;
    public static final String ASC ="asc" ;
    public static final String DESC ="desc" ;

    public static String BROKERAGE_ID = null;
    public static  String OFFICE_ID = null;
    public static  String BROKER_ID = null;

    public static String getInsuredId() {
        return INSURED_ID;
    }

    public static void setInsuredId(String insuredId) {
        INSURED_ID = insuredId;
    }

    public static String INSURED_ID=null;
    public static void setBROKERAGE_ID(String BROKERAGE_ID){
        Constant.BROKERAGE_ID = BROKERAGE_ID;

    }
    public static String getBROKERAGE_ID(){
    return BROKERAGE_ID;
    }
    public static void setOFFICE_ID(String OFFICE_ID){
Constant.OFFICE_ID =OFFICE_ID;
    }
    public static String getOFFICE_ID(){
return OFFICE_ID;
    }
    public  static void setBROKER_ID(String BROKER_ID){
        Constant.BROKER_ID =BROKER_ID;
    }
    public static String getBROKER_ID(){
        return BROKER_ID;

    }



}
