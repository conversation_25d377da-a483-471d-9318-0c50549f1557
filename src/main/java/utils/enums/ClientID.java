package utils.enums;

public enum ClientID {
    STAGE_CLIENT("391a2336-0192-11eb-adc1-0242ac120002"),INTEGRATION_CLIENT("48ac4e50-0192-11eb-adc1-0242ac120002");
    public final String clientValue;

   //  getter method
    public String getClientValues()
    {
        return this.clientValue;
    }

    // enum constructor - cannot be public or protected
    ClientID(String clientValue)
    {
        this.clientValue = clientValue;
    }


}
