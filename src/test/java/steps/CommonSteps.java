package steps;

import com.fasterxml.jackson.databind.ObjectMapper;
import helpers.HttpHelper;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.commons.io.FileUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.Assert;
import utils.APIVerifications;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Iterator;

import static io.restassured.RestAssured.given;
import static io.restassured.module.jsv.JsonSchemaValidator.matchesJsonSchema;
import static org.junit.Assert.assertEquals;

public class CommonSteps {
    private static final Logger log = LogManager.getLogger(CommonSteps.class);
    public static Response response;
    private final HttpHelper httpHelper = new HttpHelper(true);

    @When("^POST \"([^\"]*)\"$")
    public void postWithBody(String endpoint, String requestPayload) throws Throwable {
        response = httpHelper.requestPost(endpoint, requestPayload);
    }

    @Then("the status code should be {int}")
    public void theStatusCodeShouldBe(int expectedStatusCode) throws Throwable {
//        System.out.println("response body:-- " + response.getBody().prettyPeek());
        response.then().log().ifValidationFails().statusCode(expectedStatusCode);
    }

    @Then("the status code should be test {int} {string}")
    public void theStatusCodeShouldBetest(int expectedStatusCode, String filename) throws Throwable {
//    System.out.println("response body:-- " + response.getBody().prettyPeek());
        try {
            String jsonResponse = response.getBody().asString();
            response.then().log().ifValidationFails().statusCode(expectedStatusCode);
            File file = new File("src/main/resources/schemas/json/"+filename+".json");
            ObjectMapper mapper = new ObjectMapper();
            // Write the JSON response to the file
            mapper.writeValue(file, mapper.readTree(jsonResponse));
//        System.out.println("JSON response saved to file: " + file.getAbsolutePath());
        } catch (IOException e) {
            e.printStackTrace();
        }

    }


    @And("^the response match the schema \"([^\"]*)\"$")
    public void theResponseMatchTheSchema(String fileName) throws Throwable {
        try {
            String expectedJson = FileUtils.readFileToString(new File(fileName));
            response.then().assertThat().body(matchesJsonSchema(expectedJson));
        } catch (FileNotFoundException e) {
            log.info(e);
        }
    }

    @When("Get {string}")
    public void get(String endPoint) throws Throwable {
        response = httpHelper.requestGet(endPoint);

    }

    @And("Validate {string} for invalid values")
    public void validateErrMSGForInvalidValues(String errorMsg) {
        String errorFromResponse = APIVerifications.getErrorMsg(CommonSteps.response, "error");
        log.info("expecter msg:---" + errorMsg);
        Assert.assertTrue("Assert message on invalid submissions", errorFromResponse.contains(errorMsg));
    }


    @When("^DELETE \"([^\"]*)\"$")
    public void deleteWithBody(String endpoint, String requestPayload) throws Throwable {
        response = httpHelper.requestDelete(endpoint, requestPayload);
    }

    @When("^PUT \"([^\"]*)\"$")
    public void putWithBody(String endpoint, String requestPayload) throws Throwable {
        response = httpHelper.requestPut(endpoint, requestPayload);
    }

    @And("validate error msg {string}")
    public void validateErrorMsg(String errMsg) {
        String errorFromResponse = APIVerifications.getErrorMsg(CommonSteps.response, "errors.message");
        assertEquals("Assert error message on submissions", errMsg, errorFromResponse);
    }

    @And("the response time is less then <{long}>")
    public void theResponseTimeIsLessThen(long responseTime) {
        APIVerifications.responseTimeValidation(response, responseTime);
    }

    @Then("user check api have response header")
    public void IteratingOverHeaders() {
        String contentType = response.header("Content-Type");
        assertEquals(contentType, "application/json;charset=UTF-8");
        String serverType = response.header("Server");
        assertEquals(serverType, "istio-envoy");
        String contentEncoding = response.header("Content-Encoding");
//        System.out.println("Key: " + contentEncoding);
        String cacheControl = response.header("cache-control");
        assertEquals(cacheControl, "private, no-cache, no-store, max-age=0, must-revalidate");
        String xContentTypeOptionsValue = response.header("x-content-type-options");
        assertEquals(xContentTypeOptionsValue, "nosniff");
        String xFrameOptionsValue = response.header("x-frame-options");
        assertEquals(xFrameOptionsValue, "DENY");

    }
    @Then("^the response match the json \"([^\"]*)\"$")
    public void theResponseMatches(String fileName) throws Throwable {
        try {
            String jsonResponse = response.getBody().asString();
            System.out.println("jsonResponse"+jsonResponse);
            String[] keysToIgnore = {"parentId", "clientId","createdAt","updatedAt"};
            String actualJson = new String(Files.readAllBytes(Paths.get("src/main/resources/schemas/json/"+fileName+".json")));

            JSONArray Responsejson1 = new JSONArray(jsonResponse);
            JSONArray actualJson2 = new JSONArray(actualJson);
            removeKeysFromArray(Responsejson1, keysToIgnore);
            removeKeysFromArray(actualJson2, keysToIgnore);
            // Read the expected JSON from the file

            // Use JSONAssert to compare the actual response with the expected JSON
            // Compare the two JSON objects after ignoring specified keys
            if (Responsejson1.similar(actualJson2)) {
                System.out.println("The JSON objects are the same, ignoring specified keys.");
            } else {
                System.out.println("The JSON objects are different.");
            }
//             assertEquals(expectedJson, jsonResponse);
//            String expectedJson = FileUtils.readFileToString(new File(fileName));
//            response.then().assertThat().body(matchesJsonSchema(expectedJson));
        } catch (FileNotFoundException e) {
            log.info(e);
        }
    }

    @Then("^the response match the json object \"([^\"]*)\"$")
    public void theResponseMatchesObject(String fileName) throws Throwable {
        try {
            String jsonResponse = response.getBody().asString();
            System.out.println("jsonResponse"+jsonResponse);
            String[] keysToIgnore = {"parentId", "clientId","createdAt","updatedAt","lockedCells","id","templateJson","validation","productType"};
            String actualJson = new String(Files.readAllBytes(Paths.get("src/main/resources/schemas/json/"+fileName+".json")));

            JSONObject Responsejson1 = new JSONObject(jsonResponse);
            JSONObject actualJson2 = new JSONObject(actualJson);
            removeKeys(Responsejson1, keysToIgnore);
            removeKeys(actualJson2, keysToIgnore);
            // Read the expected JSON from the file

            // Use JSONAssert to compare the actual response with the expected JSON
            // Compare the two JSON objects after ignoring specified keys
            if (Responsejson1.similar(actualJson2)) {
                System.out.println("The JSON objects are the same, ignoring specified keys.");
                Assert.assertTrue(Responsejson1.similar(actualJson2));
            } else {
                System.out.println("The JSON objects are different.");
                Assert.assertTrue(Responsejson1.similar(actualJson2));
            }
//             assertEquals(expectedJson, jsonResponse);
//            String expectedJson = FileUtils.readFileToString(new File(fileName));
//            response.then().assertThat().body(matchesJsonSchema(expectedJson));
        } catch (FileNotFoundException e) {
            log.info(e);
        }
    }

    // Recursively removes specified keys from each object in the JSON array
    public static void removeKeysFromArray(JSONArray jsonArray, String[] keysToIgnore) throws JSONException {
        for (int i = 0; i < jsonArray.length(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            removeKeys(jsonObject, keysToIgnore);  // Remove keys from each JSON object in the array
        }
    }
    // Recursively removes specified keys from the JSON object
    public static void removeKeys(JSONObject json, String[] keysToIgnore) {
        for (String key : keysToIgnore) {
            json.remove(key);  // Remove the key if it exists
        }

        // Recursively handle nested JSONObjects
        Iterator<String> keys = json.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            try {
                Object value = json.get(key);
                if (value instanceof JSONObject) {
                    removeKeys((JSONObject) value, keysToIgnore);
                }
            } catch (JSONException e) {
                e.printStackTrace();
            }
        }
    }

    @Then("the response should contain {string} as {string}")
    public void the_response_should_contain_as(String key, String expectedValue) {
        JSONObject jsonResponse = new JSONObject(response.asString());
        Assert.assertEquals(jsonResponse.getString(key), expectedValue, "Mismatch in " + key);
    }


    @Then("the total number of items should be {int}")
    public void the_total_number_of_items_should_be(int expectedTotal) {
        JSONObject jsonResponse = new JSONObject(response.asString());
        Assert.assertEquals("Total items do not match!", jsonResponse.getInt("totalItems"), expectedTotal);
    }
    @Then("the response should have a folder named {string}")
    public void the_response_should_have_a_folder_named(String folderName) {
        JSONObject jsonResponse = new JSONObject(response.asString());
        boolean folderExists = jsonResponse.getJSONObject("data").getJSONArray("folders")
                .toList()
                .stream()
                .anyMatch(folder -> ((JSONObject) folder).getString("folderName").equals(folderName));

        Assert.assertTrue("Folder " + folderName + " not found in response",folderExists);
    }

    @Then("the folder {string} should have a subfolder named {string}")
    public void the_folder_should_have_a_subfolder_named(String parentFolder, String subFolder) {
        JSONObject jsonResponse = new JSONObject(response.asString());
        boolean subFolderExists = jsonResponse.getJSONObject("data").getJSONArray("folders")
                .toList()
                .stream()
                .filter(folder -> ((JSONObject) folder).getString("folderName").equals(parentFolder))
                .flatMap(folder -> ((JSONObject) folder).getJSONArray("folders").toList().stream())
                .anyMatch(subfolder -> ((JSONObject) subfolder).getString("folderName").equals(subFolder));

        Assert.assertTrue( "Subfolder " + subFolder + " not found under " + parentFolder,subFolderExists);
    }

    @Then("the folder {string} should have a document count of {int}")
    public void the_folder_should_have_a_document_count_of(String folderName, int expectedCount) {
        JSONObject jsonResponse = new JSONObject(response.asString());
        boolean countMatches = jsonResponse.getJSONObject("data").getJSONArray("folders")
                .toList()
                .stream()
                .anyMatch(folder -> ((JSONObject) folder).getString("folderName").equals(folderName)
                        && ((JSONObject) folder).getInt("documentCount") == expectedCount);

        Assert.assertTrue( "Document count for folder " + folderName + " does not match expected count: " + expectedCount,countMatches);
    }


}