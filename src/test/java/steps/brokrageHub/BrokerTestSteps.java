package steps.brokrageHub;

import helpers.HttpHelper;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import pojo.brokerageHubService.BrokerData;
import pojo.brokerageHubService.SearchBrokerInput;
import pojo.graphql.BrokerHubsQueries;
import pojo.graphql.GraphQLQuery;
import steps.CommonSteps;
import utils.Constant;
import utils.Utils;

import java.util.List;

public class BrokerTestSteps {
    private static final Logger log = LogManager.getLogger(BrokerageTestSteps.class);
    GraphQLQuery mutation =new GraphQLQuery();
    HttpHelper httpHelper=new HttpHelper(true);
    BrokerData brokerData =new BrokerData();
    BrokerData.Address address =new BrokerData.Address();
    @When("the user send POST request {string} to create Broker")
    public void the_user_send_post_request_to_create_broker(String endpoint) throws Throwable {
brokerData.setFirstName(Utils.generateRandomAlphabetChars(8));
brokerData.setLastName(Utils.generateRandomAlphabetChars(6));
brokerData.setBrokerageId(Constant.getBROKERAGE_ID());
brokerData.setOfficeId(Constant.getOFFICE_ID());
brokerData.setEmailAddress(Utils.generateRandomAlphabetChars(8)+"@test.com");
        brokerData.setStatus(Constant.ACTIVE);
        brokerData.setFilingBroker(false);
        brokerData.setMobilePhone(Utils.generateRandomNumbers(10));
        brokerData.setWorkPhone(Utils.generateRandomNumbers(10));
        brokerData.setJobTitle("Test Job");
        brokerData.setTeam("Test Team");
        brokerData.setLegalEntity("Test Legal");
        address.setAddressLine1(Utils.generateRandomAlphaNumeric(7));
        address.setAddressLine2(Utils.generateRandomAlphaNumeric(8));
        address.setCity("London");
        address.setState("London");
        address.setZipcode(Utils.generateRandomNumbers(6));
        address.setAddressType("Test Type");
        address.setCountry("UK");
        brokerData.setAddress(address);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,brokerData);
    }
    @When("save the broker id")
    public void the_brokerage_id(){
        Constant.setBROKER_ID(CommonSteps.response.getBody().jsonPath().getString("data.brokerId"));
        System.out.println("createBroker-"+CommonSteps.response.getBody().jsonPath().getString("data.brokerId"));
    }
    @When("the user send POST request {string} to update Broker")
    public void the_user_send_post_request_to_update_broker(String endpoint) throws Throwable {
        brokerData.setId(Constant.getBROKER_ID());
        brokerData.setFirstName(Utils.generateRandomAlphabetChars(8));
        brokerData.setLastName(Utils.generateRandomAlphabetChars(6));
        brokerData.setBrokerageId(Constant.getBROKERAGE_ID());
        brokerData.setOfficeId(Constant.getOFFICE_ID());
        brokerData.setEmailAddress(Utils.generateRandomAlphabetChars(8)+"@test.com");
        brokerData.setStatus(Constant.ACTIVE);
        brokerData.setFilingBroker(false);
        brokerData.setMobilePhone(Utils.generateRandomNumbers(10));
        brokerData.setWorkPhone(Utils.generateRandomNumbers(10));
        brokerData.setJobTitle("Test Job");
        brokerData.setLegalEntity("Test Legal");
        brokerData.setTeam("Test Team");
        address.setAddressLine1(Utils.generateRandomAlphaNumeric(7));
        address.setAddressLine2(Utils.generateRandomAlphaNumeric(8));
        address.setCity("London");
        address.setState("London");
        address.setZipcode(Utils.generateRandomNumbers(6));
        address.setAddressType("Test Type");
        address.setCountry("UK");
        brokerData.setAddress(address);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,brokerData);


    }
    @When("the user send POST request {string} to delete Broker")
    public void the_user_send_post_request_to_delete_broker(String endpoint) throws Throwable {
        mutation.setQuery(BrokerHubsQueries.DELETE_BROKER.replace("brokerID",Constant.getBROKER_ID()));
        CommonSteps.response = httpHelper.requestDelete(endpoint +"/"+ Constant.getBROKER_ID());
    }
    @When("the user send POST request {string} to search Broker")
    public void the_user_send_post_request_to_search_broker(String endpoint) throws Throwable {
        String request="{\"searchName\":\"a\"}";
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint, request);
        List<String> brokername =CommonSteps.response.getBody().jsonPath().getList("data.searchBroker.items.brokerageName");
        System.out.println(brokername+" "+brokerData.getFirstName());
//        Assert.assertTrue(brokername.contains(brokerData.getFirstName()));

    }

}
