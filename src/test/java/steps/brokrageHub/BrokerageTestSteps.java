package steps.brokrageHub;
import com.fasterxml.jackson.databind.JsonNode;
import helpers.HttpHelper;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import pojo.brokerageHubService.CreateBrokerage;
import pojo.graphql.BrokerHubsQueries;
import pojo.graphql.GraphQLQuery;
import steps.CommonSteps;
import utils.Constant;
import utils.Helper;
import utils.Utils;

public class BrokerageTestSteps {
    private static final Logger log = LogManager.getLogger(BrokerageTestSteps.class);
    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    HttpHelper httpHelper=new HttpHelper(true);
    Constant constant=new Constant();
    CreateBrokerage createBrokerage =new CreateBrokerage();
    @Given("user request authentication")
    public void user_request_authentication() {
        // Write code here that turns the phrase above into concrete actions
    }

    @When("the user send POST request {string} to create Brokerage")
    public void the_user_send_post_request_brokerage_hub_graphql_to_create_brokerage(String endpoint) throws Throwable {
        createBrokerage.setName(Utils.generateRandomAlphabetChars(9));
        createBrokerage.setBrokercode(Utils.generateRandomNumbers(6));
        createBrokerage.setBusinessType("Legal");
        createBrokerage.setDefaultCommission(1.11f);
        System.out.println(createBrokerage);
//        mutation.setVariables(createBrokerage);
//        mutation.setQuery(BrokerHubsQueries.CREATE_BROKERAGE);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,createBrokerage);
        System.out.println("createBroker"+ createBrokerage.toString());
        System.out.println("query-"+ mutation.getQuery());


    }
    @When("save the brokerage id")
    public void the_brokerage_id(){
        Constant.setBROKERAGE_ID(CommonSteps.response.getBody().jsonPath().getString("data.brokerageId"));
        System.out.println("createBroker-"+CommonSteps.response.getBody().jsonPath().getString("data.brokerageId"));
    }

    @When("the user send POST request {string} to update Brokerage")
    public void the_user_send_post_request_brokerage_hub_graphql_to_update_brokerage(String endpoint) throws Throwable {
        JsonNode jsonPayload = Helper.readJsonNodeObjects(Constant.UPDATE_BROKERAGE_JSON_PATH);
//        Constant.setBROKERAGE_ID("b41bbdcc-22d5-40c9-a635-2caf9e5f22e3");
        createBrokerage.setId(Constant.getBROKERAGE_ID());
        createBrokerage.setName(Utils.generateRandomAlphabetChars(9));
        createBrokerage.setBrokercode(Utils.generateRandomNumbers(6));
        createBrokerage.setBusinessType("Legal");
        createBrokerage.setDefaultCommission(1.11f);
//        mutation.setVariables(createBrokerage);
//        mutation.setQuery(BrokerHubsQueries.UPDATE_BROKERAGE);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint, createBrokerage);
        CommonSteps.response.then().log().ifValidationFails().statusCode(201);
        String updatedBusinessID=CommonSteps.response.getBody().jsonPath().get("data.updateBrokerage.id");
    }
    @When("the user send POST request {string} to delete Brokerage")
    public void the_user_send_post_request_brokerage_hub_graphql_to_delete_brokerage(String endpoint) throws Throwable {
        CommonSteps.response = httpHelper.requestDelete(endpoint+"/"+Constant.getBROKERAGE_ID());
//        Assert.assertTrue(CommonSteps.response.getBody().jsonPath().get("data.deleteBrokerage"));
    }
    @When("the user send POST request {string} to search Brokerage")
    public void the_user_send_post_request_brokerage_hub_graphql_to_search_brokerage(String endpoint) throws Throwable {
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint, BrokerHubsQueries.SEARCH_BROKERAGE);
    }


}
