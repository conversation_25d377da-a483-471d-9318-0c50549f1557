package steps.brokrageHub;

import helpers.HttpHelper;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import pojo.brokerageHubService.Address;
import pojo.brokerageHubService.CreateOffice;
import pojo.graphql.BrokerHubsQueries;
import pojo.graphql.GraphQLQuery;
import steps.CommonSteps;
import utils.Constant;
import utils.Utils;

public class OfficeTestSteps {
    private static final Logger log = LogManager.getLogger(OfficeTestSteps.class);
    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    HttpHelper httpHelper=new HttpHelper(true);
    Constant constant=new Constant();
    CreateOffice createOffice=new CreateOffice();
    Address address =new Address();
    Address updateAddress =new Address();


    @When("the user send POST request {string} to create Office")
    public void the_user_send_post_request_to_create_office(String endpoint) throws Throwable {
//        Constant.setBROKERAGE_ID("ac5aff86-032c-4d5e-97c5-5b76c9b0a4ad");
        createOffice.setOfficeName(Utils.generateRandomAlphabetChars(8));
        createOffice.setBrokerageId(Constant.getBROKERAGE_ID());
        createOffice.setEmail("<EMAIL>");
        address.setAddressLine1("12 Road 12 Bridge "+Utils.generateRandomAlphabetChars(5));
        address.setAddressLine2("Sectro 6 "+Utils.generateRandomAlphabetChars(5));
        address.setAddressType("test12");
        address.setCity("London");
        address.setCountry("UK");
        address.setState("London");
        address.setZipCode("119902");
        createOffice.setAddress(address);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,createOffice);
          }



    @Then("save office ID")
    public void save_office_id() {
        Constant.setOFFICE_ID(CommonSteps.response.getBody().jsonPath().getString("data.officeId"));
        System.out.println("Office ID-"+CommonSteps.response.getBody().jsonPath().getString("data.officeId"));
    }
    @When("the user send POST request {string} to update Office")
    public void the_user_send_post_request_to_update_office(String endpoint) throws Throwable {
        createOffice.setOfficeName(Utils.generateRandomAlphabetChars(8));
        createOffice.setId(Constant.getOFFICE_ID());
        createOffice.setBrokerageId(Constant.getBROKERAGE_ID());
        createOffice.setId(Constant.getOFFICE_ID());
        createOffice.setEmail("<EMAIL>");
        updateAddress.setAddressLine1("12 Road 12 Bridge "+Utils.generateRandomAlphabetChars(5));
        updateAddress.setAddressLine2("Sectro 6 "+Utils.generateRandomAlphabetChars(5));
        updateAddress.setAddressType("test12");
        updateAddress.setCity("London");
        updateAddress.setCountry("UK");
        updateAddress.setState("London");
        updateAddress.setZipCode("119902");
        createOffice.setAddress(updateAddress);
//        mutation.setVariables(createOffice);
//        mutation.setQuery(BrokerHubsQueries.UPDATE_OFFICE);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,createOffice);
    }
    @When("the user send POST request {string} to delete Office")
    public void the_user_send_post_request_to_search_office(String endpoint) throws Throwable {
//        mutation.setQuery(BrokerHubsQueries.DELETE_OFFICE.replace("OfficeID",Constant.getOFFICE_ID()));
        CommonSteps.response = httpHelper.requestDelete(endpoint+ "/"+Constant.getOFFICE_ID());
//        Assert.assertTrue(CommonSteps.response.getBody().jsonPath().get("data.deleteOffice"));

    }
    @When("the user send POST request {string} to search Office")
    public void the_user_send_post_request_brokerage_hub_graphql_to_search_Office(String endpoint) throws Throwable {
        mutation.setQuery( BrokerHubsQueries.SEARCH_OFFICE);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint, mutation);
    }
}
