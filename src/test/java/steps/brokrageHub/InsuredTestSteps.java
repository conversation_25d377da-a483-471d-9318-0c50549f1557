package steps.brokrageHub;

import helpers.HttpHelper;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import pojo.brokerageHubService.InsuredData;
import pojo.brokerageHubService.SearchInput;
import pojo.graphql.GraphQLQuery;
import steps.CommonSteps;
import utils.Constant;
import utils.Utils;

public class InsuredTestSteps {
    private static final Logger log = LogManager.getLogger(BrokerageTestSteps.class);
    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    HttpHelper httpHelper=new HttpHelper(true);
    Constant constant=new Constant();
    InsuredData insuredData =new InsuredData();
    InsuredData.Address address =new InsuredData.Address();
    SearchInput searchInput=new SearchInput();
    SearchInput.Input input = new SearchInput.Input();



    @When("the user send POST request {string} to create Insured")
    public void the_user_send_post_request_to_create_insured(String endpoint) throws Throwable {
        insuredData.setBusinessType("Type1");
        insuredData.setBusinessName("Automated test "+Utils.generateRandomAlphabetChars(8));
        insuredData.setDba(Utils.generateRandomAlphabetChars(8));
        // Create an instance of AddressInput and set random values for its fieldsaddressInput.setCountry(generateRandomString());
        address.setState("NY "+Utils.generateRandomAlphabetChars(7));
        address.setCity("NY");
        address.setCountry("US");
        address.setZipCode("ERS 123");
        address.setAddressLine1("TH HALL Road "+Utils.generateRandomAlphabetChars(7));
        address.setAddressLine2("Block 7" +Utils.generateRandomAlphabetChars(7));
        address.setOfficeType("Option 1");

        // Set the created AddressInput instance to the createAddressInput field of InsuredData
        insuredData.setAddress(address);
        insuredData.setAddress(address);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,insuredData);    }

    @Then("save the Insured id")
    public void save_the_insured_id() {
        Constant.setInsuredId(CommonSteps.response.getBody().jsonPath().getString("data.insuredId"));
    }
    @When("the user send POST request {string} to update Insured")
    public void the_user_send_post_request_to_update_insured(String endpoint) throws Throwable {
        insuredData.setBusinessType("Type1");
        insuredData.setBusinessName("Automated test "+Utils.generateRandomAlphabetChars(8));
        insuredData.setDba(Utils.generateRandomAlphabetChars(8));
        insuredData.setId(Constant.getInsuredId());
        // Create an instance of AddressInput and set random values for its fieldsaddressInput.setCountry(generateRandomString());
        address.setState("NY "+Utils.generateRandomAlphabetChars(7));
        address.setCity("NY");
        address.setCountry("US");
        address.setZipCode("ERS 123");
        address.setAddressLine1("TH HALL Road "+Utils.generateRandomAlphabetChars(7));
        address.setAddressLine2("Block 7" +Utils.generateRandomAlphabetChars(7));
        address.setOfficeType("Option 1");

        // Set the created AddressInput instance to the createAddressInput field of InsuredData
        insuredData.setAddress(address);
        insuredData.setAddress(address);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint, insuredData);
    }
    @When("the user send POST request {string} to delete Insured")
    public void the_user_send_post_request_to_delete_insured(String endpoint) throws Throwable {
        CommonSteps.response = httpHelper.requestDelete(endpoint+Constant.getInsuredId());
    }
    @When("the user send POST request {string} to search Insured")
    public void the_user_send_post_request_to_search_insured(String endpoint) throws Throwable {
        input.setSearchText("auto");
       input.setIncludePolicyWithSubmission(false);
        searchInput.setInput(input);

//        mutation.setVariables(searchInput);
//        mutation.setQuery(InsuredQueries.SEARCH_INSURED);
        CommonSteps.response = httpHelper.requestGraphQLPost(endpoint,searchInput);
    }



}
