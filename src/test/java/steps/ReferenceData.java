package steps;

import io.cucumber.java.en.And;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.assertj.core.api.SoftAssertions;

import java.util.List;


public class ReferenceData extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(ReferenceData.class);
    private SoftAssertions softAssertions;

    public ReferenceData(SoftAssertions softAssertions) {
        this.softAssertions = softAssertions;
    }

    @When("POS {string}")
    public void pos(String arg0) {
    }

    @And("validate the personalAndAdvertisingInjuryLimit response matches expected")
    public void validateThePersonalAndAdvertisingInjuryLimitResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000",
                "$2,000,000",
                "$4,000,000",
                "$5,000,000",
                "Excluded"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
System.out.println(actualDataValues);
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for PersonalAndAdvertisingInjuryLimit ")
                .containsExactlyElementsOf(expectedDataValues);
        softAssertions.assertAll();

    }

    @And("validate the entityType=generalAggregateLimit&searchText= response matches expected")
    public void validateTheEntityTypeGeneralAggregateLimitSearchTextResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000",
                "$2,000,000",
                "$4,000,000",
                "$5,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");

        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for GeneralAggregateLimit")
                .containsExactlyElementsOf(expectedDataValues);
        softAssertions.assertAll();

    }

    @And("validate the medicalPaymentLimit response matches expected")
    public void validateTheMedicalPaymentLimitSearchTextResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$5,000",
                "$10,000",
                "$50,000",
                "$100,000",
                "Excluded"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for MedicalPaymentLimit ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the damageToPermisesRentedToYou response matches expected")
    public void validateTheDamageToPermisesRentedToYouResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$100,000",
                "$250,000",
                "Excluded"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for damageToPermisesRentedToYou ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the eachOccuranceExcess response matches expected")
    public void validateTheEachOccuranceExcessSearchTextResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000",
                "$1,500,000",
                "$2,000,000",
                "$2,500,000",
                "$3,000,000",
                "$3,500,000",
                "$4,000,000",
                "$4,500,000",
                "$5,000,000",
                "$6,000,000",
                "$7,000,000",
                "$7,500,000",
                "$8,000,000",
                "$8,500,000",
                "$9,000,000",
                "$10,000,000",
                "$11,000,000",
                "$12,000,000",
                "$12,500,000",
                "$13,000,000",
                "$13,500,000",
                "$13,750,000",
                "$14,000,000",
                "$15,000,000",
                "$20,000,000",
                "$25,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccuranceExcess ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the eachOccurancePrimary response matches expected")
    public void validateTheEachOccurancePrimarySearchTextResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000",
                "$2,000,000",
                "$5,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccurancePrimary ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the otherAggregateLimit response matches expected")
    public void validateTheOtherAggregateLimitResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000", "$1,500,000", "$2,000,000", "$2,500,000",
                "$3,000,000", "$3,500,000", "$4,000,000", "$4,500,000",
                "$5,000,000", "$6,000,000", "$7,000,000", "$7,500,000",
                "$8,000,000", "$8,500,000", "$9,000,000", "$10,000,000",
                "$11,000,000", "$12,000,000", "$12,500,000", "$13,000,000",
                "$13,500,000", "$13,750,000", "$14,000,000", "$15,000,000",
                "$20,000,000","$25,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccurancePrimary ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the productsCompletedOperationsAggregate response matches expected for Excess")
    public void validateTheProductsCompletedOperationsAggregateResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$1,000,000", "$2,000,000", "$2,500,000", "$3,000,000", "$3,500,000",
                "$4,000,000", "$4,500,000", "$5,000,000", "$6,000,000", "$7,000,000",
                "$7,500,000", "$8,000,000", "$8,500,000", "$9,000,000", "$10,000,000",
                "$11,000,000", "$12,000,000", "$12,500,000", "$13,000,000", "$13,500,000",
                "$13,750,000", "$14,000,000", "$15,000,000", "$20,000,000", "$25,000,000","Excluded"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccurancePrimary ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the productsCompletedOperationsAggregate response matches expected for Primary")
    public void validateTheProductsCompletedOperationsAggregateResponseMatchesExpectedForPrimary() {
        List<String> expectedDataValues = List.of(
                "$1,000,000",
                "$2,000,000",
                "$4,000,000",
                "$5,000,000",
                "Excluded"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccurancePrimary ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the retentionType response matches expected")
    public void validateTheRetentionTypeResponseMatchesExpected() {
            List<String> expectedDataValues = List.of(
                    "Deductible" ,
                            "Self Insured Retention",
                            "N/A - Guaranteed Cost"
            );
            List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
            softAssertions.assertThat(actualDataValues)
                    .as("Verify dataValue list in response for eachOccurancePrimary ")
                    .containsAll(expectedDataValues);
            System.out.println(actualDataValues);
            softAssertions.assertAll();
    }


    @And("validate the deductibleAmount response matches expected")
    public void validateTheDeductibleAmountResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "$5,000" ,
                        "$10,000" ,
                        "$15,000" ,
                        "$20,000" ,
                        "$25,000" ,
                        "$50,000" ,
                        "$75,000" ,
                        "$100,000" ,
                        "$125,000" ,
                        "$150,000" ,
                        "$200,000" ,
                        "$250,000" ,
                        "$500,000" ,
                        "$1,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for eachOccurancePrimary ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the SIR Amount response matches expected")
    public void validateTheSIRAmountResponseMatchesExpected() {

        List<String> expectedDataValues = List.of(
                "$5,000" ,
                        "$10,000" ,
                        "$15,000" ,
                        "$20,000" ,
                        "$25,000" ,
                        "$50,000" ,
                        "$75,000" ,
                        "$100,000" ,
                        "$125,000" ,
                        "$150,000" ,
                        "$200,000" ,
                        "$250,000" ,
                        "$500,000" ,
                        "$1,000,000"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the # of General Aggregate Reinstatements response matches expected")
    public void validateTheOfGeneralAggregateReinstatementsResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "N/A" ,
                        "1" ,
                        "2" ,
                        "3" ,
                        "4" ,
                        "5"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the # of General Aggregate exposureBasis response matches expected")
    public void validateTheOfGeneralAggregateExposureBasisResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "Flat" ,
                        "Each Acre" ,
                        "$1,000 Construction Costs" ,
                        "Each Contingent Units" ,
                        "Each Dam" ,
                        "$1,000 Gross Revenue" ,
                        "$1,000 Management Fees" ,
                        "Each Owned Units" ,
                        "$1,000 Payroll" ,
                        "1,000 Square Footage" ,
                        "Each Unit"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the excessCarrier response matches expected")
    public void validateTheCarrierExcessResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "Texas Insurance Company" ,
                        "Continental Insurance Company" ,
                        "Texas Insurance Company / MS Transverse Specialty Insurance Company" ,
                        "MS Transverse Specialty Insurance Company / Texas Insurance Company"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsAll(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the primaryCarrier response matches expected")
    public void validateTheCarrierPrimaryResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "Texas Insurance Company" ,
                "Continental Insurance Company"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsAll(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the Treaty response matches expected")
    public void validateTheTreatyResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "Excess M2" ,
                        "Habitational - 4" ,
                        "Nationwide - 1" ,
                        "Nationwide - 2" ,
                        "Nationwide - 3" ,
                        "Nationwide - 4" ,
                        "None" ,
                        "NY - 1" ,
                        "NY - 2" ,
                        "NY - 3" ,
                        "NY - 4" ,
                        "Primary M2"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsExactlyElementsOf(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }

    @And("validate the riskState response matches expected")
    public void validateTheRiskStateResponseMatchesExpected() {
        List<String> expectedDataValues = List.of(
                "AL" ,
                        "AK" ,
                        "AZ" ,
                        "AR" ,
                        "CA" ,
                        "CO" ,
                        "CT" ,
                        "DE" ,
                        "DC" ,
                        "FL" ,
                        "GA" ,
                        "HI" ,
                        "ID" ,
                        "IL" ,
                        "IN" ,
                        "IA" ,
                        "KS" ,
                        "KY" ,
                        "LA" ,
                        "ME" ,
                        "MD" ,
                        "MA" ,
                        "MI" ,
                        "MN" ,
                        "MS" ,
                        "MO" ,
                        "MT" ,
                        "NE" ,
                        "NV" ,
                        "NH" ,
                        "NJ" ,
                        "NM" ,
                        "NY" ,
                        "NC" ,
                        "ND" ,
                        "OH" ,
                        "OK" ,
                        "OR" ,
                        "PA" ,
                        "PR" ,
                        "RI" ,
                        "SC" ,
                        "SD" ,
                        "TN" ,
                        "TX" ,
                        "UT" ,
                        "VT" ,
                        "VA" ,
                        "WA" ,
                        "WV" ,
                        "WI" ,
                        "WY"
        );
        List<String> actualDataValues =  CommonSteps.response.jsonPath().getList("content.dataValue");
        softAssertions.assertThat(actualDataValues)
                .as("Verify dataValue list in response for SIR Amount ")
                .containsAll(expectedDataValues);
        System.out.println(actualDataValues);
        softAssertions.assertAll();
    }
}