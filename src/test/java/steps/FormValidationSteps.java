package steps;

import helpers.PolicyHelper;
import io.cucumber.java.en.*;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import pojo.graphql.GraphQLQuery;
import steps.brokrageHub.BrokerageTestSteps;
import utils.Constant;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.junit.jupiter.api.Assertions.assertTrue;

public class FormValidationSteps extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(FormValidationSteps.class);
    //    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    Constant constant=new Constant();
    PolicyHelper policyHelper=new PolicyHelper();
    Map<String, String> queryParams;
    private Response response;
    private List<String> mandatoryEndorsements;
//    private Response response;
    private String state;
    private List<Map<String, Object>> forms;
        List<String> mandatoryFormNames = new ArrayList<>();

//    @Given("I call the forms API for state {string} {string}")
    @Given("I call the forms API with:")
    public void i_call_forms_api_with(io.cucumber.datatable.DataTable table) throws Throwable {
//        this.state = stateCode;

        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("pageNumber", "0");
        queryParams.put("pageSize", "1000");
//        queryParams.put("state", stateCode);
        queryParams.put("classes", "SI");
        queryParams.put("lineOfBusinesses", "");

//        CommonSteps.response=httpHelper.requestGet(endPoint,queryParams);
        queryParams = table.asMap(String.class, String.class);

        Map<String, String> actualQueryParams = new HashMap<>();
        actualQueryParams.put("pageNumber", "0");
        actualQueryParams.put("pageSize", "100");
        actualQueryParams.put("state", queryParams.get("state"));
        actualQueryParams.put("classes", queryParams.get("classes"));
        actualQueryParams.put("lobAndConveyance", queryParams.get("lobAndConveyance"));

        // Call your method
        CommonSteps.response=httpHelper.requestGet("/reference-data-service/forms/optional", actualQueryParams);
        System.out.println("response test "+CommonSteps.response.prettyPrint());

        forms = CommonSteps.response.jsonPath().getList("data");
        System.out.println(CommonSteps.response.prettyPrint());

//        forms = response.jsonPath().getList("data");
    }

    @Then("I should see the following forms marked as Optional for state {string}:")
    public void i_should_see_optional_forms(String stateCode, io.cucumber.datatable.DataTable dataTable) {
        List<String> expectedForms = dataTable.asList();

        List<Map<String, Object>> allForms = CommonSteps.response.jsonPath().getList("data");
        List<String> errors = new ArrayList<>();

        for (String expectedFormName : expectedForms) {
            Optional<Map<String, Object>> match = allForms.stream()
                    .filter(f -> {
                        Map<String, Object> data = (Map<String, Object>) f.get("data");
                        return expectedFormName.equals(data.get("formName"));
                    })
                    .findFirst();

            if (match.isEmpty()) {
                errors.add("❌ Form not found: " + expectedFormName);
            } else {
                Map<String, Object> formData = (Map<String, Object>) match.get().get("data");
                Object stateValue = formData.getOrDefault(stateCode, null);
                if (stateValue == null) {
                    errors.add("❌ Form '" + expectedFormName + "' does not contain state: " + stateCode);
                } else if (!"Optional".equalsIgnoreCase(String.valueOf(stateValue))) {
                    errors.add("❌ Form '" + expectedFormName + "' is marked as: " + stateValue + " (Expected: Optional)");
                }
            }
        }

        if (!errors.isEmpty()) {
            errors.forEach(System.err::println);
        }

        Assertions.assertTrue(errors.isEmpty(), "Some forms are missing or not marked as Optional for state " + stateCode);
    }



    @Given("I call the forms API with 2:")
    public void call_forms_api(io.cucumber.datatable.DataTable table) throws Throwable {
        Map<String, String> input = table.asMap(String.class, String.class);
        queryParams = new HashMap<>();
        queryParams.put("pageNumber", "0");
        queryParams.put("pageSize", "1000");
        queryParams.put("state", input.get("state"));
        queryParams.put("classes", input.get("classes"));
        queryParams.put("lobAndConveyance", input.get("lobAndConveyance"));

        CommonSteps.response=httpHelper.requestGet("/reference-data-service/forms/optional", input);
    }

    @Then("I should see form {string} in the response for state {string}")
    public void validate_form_in_response(String expectedForm, String stateCode) {
        List<Map<String, Object>> forms = CommonSteps.response.jsonPath().getList("data");

        boolean formExists = forms.stream().anyMatch(form -> {
            Map<String, Object> data = (Map<String, Object>) form.get("data");
            return expectedForm.equals(data.get("formName")) &&
                    "Optional".equalsIgnoreCase(String.valueOf(data.get(stateCode)));
        });

        Assertions.assertTrue(formExists,
                "Form '" + expectedForm + "' should be marked Optional in state " + stateCode);
    }





    @Given("I hit the Forms API with state {string}, classes {string}, and lob {string}")
    public void i_hit_forms_api(String state, String classes, String lob) throws Throwable {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("state", state);
        queryParams.put("classes", classes);
        queryParams.put("lobAndConveyance", lob);
        queryParams.put("pageNumber", "0");
        queryParams.put("pageSize", "1000");

        CommonSteps.response=httpHelper.requestGet("/reference-data-service/forms/optional", queryParams);
    }

    @Then("the form {string} should be present in the response")
    public void form_should_be_present(String expectedFormName) {
        boolean isPresent = CommonSteps.response.jsonPath().getList("data.data.formName").contains(expectedFormName);
        Assertions.assertTrue(isPresent, "Expected form '" + expectedFormName + "' not found in response.");
    }




    @Given("I fetch the mandatory endorsements using common method for state {string}")
    public void i_fetch_mandatory_endorsements_using_common_method(String stateCode) throws Throwable {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("state", stateCode);
        queryParams.put("pageNumber", "0");
        queryParams.put("pageSize", "1000");

        String endpoint = "/reference-data-service/endorsements/mandatory";
       CommonSteps.response = httpHelper.requestGet(endpoint, queryParams);

        assertThat(CommonSteps.response.statusCode())
                .withFailMessage("Expected 200 OK but got %d", CommonSteps.response.statusCode())
                .isEqualTo(200);

//        List<String> mandatoryFormNames = new ArrayList<>();

        List<Map<String, Object>> data = CommonSteps.response.jsonPath().getList("data");
        mandatoryEndorsements = new ArrayList<>();

        for (Map<String, Object> item : data) {
            if (Boolean.TRUE.equals(item.get("mandatory"))) {
                mandatoryEndorsements.add((String) item.get("endorsementName"));
            }
        }

//// Example assertion if you have expected values
//        List<String> expectedForms = Arrays.asList("URL-LFRM-AMDEND-AL-CNI_AL Amendatory Endorsement");
//
//        for (String expected : expectedForms) {
//            Assert.assertTrue("Expected form not found: " + expected, mandatoryFormNames.contains(expected));
//        }
    }

    @Then("I should find endorsement {string} in the response")
    public void i_should_find_endorsement_in_response(String expectedEndorsement) {
        System.out.println("mandatoryEndorsements for state is "+mandatoryEndorsements);
        for (String expected : mandatoryEndorsements) {
            Assert.assertTrue("Expected form not found: " + expected, mandatoryFormNames.contains(expected));
        }
    }

    @Given("I fetch the Optional endorsements using common method for state {string}")
    public void iFetchTheOptionalEndorsementsUsingCommonMethodForState(String stateCode) throws Throwable {
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("state", stateCode);
        queryParams.put("pageNumber", "0");
        queryParams.put("pageSize", "1000");

        String endpoint = "/reference-data-service/endorsements/optional";
        CommonSteps.response = httpHelper.requestGet(endpoint, queryParams);

        assertThat(CommonSteps.response.statusCode())
                .withFailMessage("Expected 200 OK but got %d", CommonSteps.response.statusCode())
                .isEqualTo(200);

        List<Map<String, Object>> data = CommonSteps.response.jsonPath().getList("data");
        mandatoryEndorsements = new ArrayList<>();

        for (Map<String, Object> item : data) {
            if (Boolean.TRUE.equals(item.get("mandatory"))) {
                mandatoryEndorsements.add((String) item.get("endorsementName"));
            }
        }
    }
}


