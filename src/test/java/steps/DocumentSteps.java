package steps;

import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.path.json.JsonPath;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.assertj.core.api.SoftAssertions;
import org.junit.Assert;
import org.junit.Before;
import pojo.graphql.GraphQLQuery;
import utils.Constant;

import javax.swing.*;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.core.IsCollectionContaining.hasItems;
import static org.junit.Assert.assertThat;

public class DocumentSteps extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(DocumentSteps.class);
    //    public Response response;
    GraphQLQuery mutation = new GraphQLQuery();
    Constant constant = new Constant();
    private SoftAssertions softAssertions;

    static String folder, file;
    @Before
    public void setUp() {
        softAssertions = new SoftAssertions();
    }
    @When("Get folders list {string}")
    public void get_folders_list(String endPoint) throws Throwable {

        CommonSteps.response = httpHelper.requestGet(endPoint.replace(":submissionID","02f5bbde-4309-4fb7-9cb7-59cb7774ab2c"));
                //PolicySteps.SubmissionId));
        folder = CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].id");
//        JsonPath
        JsonPath jsonPath = CommonSteps.response.jsonPath();
//                JsonPath.from(CommonSteps.response.getBody().jsonPath);
        // Extract all top-level folders
        List<Object> topLevelFolders = jsonPath.getList("data.folders.folderName");
        assertThat(topLevelFolders, hasItems(
                "Claims", "Policy", "Accounting", "Submission", "Underlying", "Underwriting",
                "External Correspondence", "Internal Correspondence"
        ));

        // Verify nested folders for specific parent folders
        verifyNestedFolders(jsonPath, "Claims", "ASU Loss Runs", "Internal Claim Correspondences");
        verifyNestedFolders(jsonPath, "Policy", "Endorsement", "Policy", "Signed TRIA Accepted", "Signed TRIA Rejected", "SL Tax Form");
        verifyNestedFolders(jsonPath, "Accounting", "Invoice", "PFC Cancellation Notice", "PFC Reinstatement Notice", "Remittance");
        verifyNestedFolders(jsonPath, "Submission", "Application", "Budget", "Construction Timeline", "Contract", "EPA Report",
                "Financial Report", "Geotechnical Compliance Letter", "Geotechnical Evaluation", "Geotechnical Report", "Loss Runs",
                "SOV", "Supplemental", "Websites");
        verifyNestedFolders(jsonPath, "Underwriting", "Account profiles/rater", "Actuary", "Audit Report", "Inspection Report", "Referral");
        verifyNestedFolders(jsonPath, "External Correspondence", "Binder to Broker", "Bind Order Request", "Correspondence",
                "Endorsement to Broker", "Indication Email", "Policy to Broker", "Quote to Broker", "Submission Email");
        verifyNestedFolders(jsonPath, "Internal Correspondence", "Approval Request", "ASUCABinds", "Book/Issue Endorsement",
                "Endorsement Request", "UW Approval");
        System.out.println("folder" + folder);


    }
    private void verifyNestedFolders(JsonPath jsonPath, String parentFolder, String... expectedSubFolders) {
        // Get all instances of the parent folder
        List<String> parentFolderIds = jsonPath.getList(
                "data.folders.findAll { it.folderName == '" + parentFolder + "' }.id"
        );

        // For each instance of the parent folder, check its subfolders
        for (String parentId : parentFolderIds) {
            List<String> subFolderNames = jsonPath.getList(
                    "data.folders.find { it.id == '" + parentId + "' }.folders.folderName"
            );
            assertThat(subFolderNames, containsInAnyOrder(expectedSubFolders));
        }
    }

    @When("Get files list {string}")
    public void get_files_list(String endPoint) throws Throwable {
        CommonSteps.response = httpHelper.requestGet(endPoint.replace(":folderID", this.folder));
        file = CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].documents[0].documentId");


    }

    @When("download original submissions file {string}")
    public void download_files_list(String endPoint) throws Throwable {
        CommonSteps.response = httpHelper.requestGet(endPoint.replace(":documentID", file));


    }

    @When("Add process date in files {string}")
    public void add_process_date_in_files(String endPoint) throws Throwable {
        CommonSteps.response = httpHelper.requestPost(endPoint.replace(":documentIds", file));

    }
    @When( "Create folder {string}")
    public void createFolder(String endPoint) throws Throwable {
//        String Request="{{\"folderName\":\"test\",\"submissionId\":\""+Constant.s+"\",\"parentFolderId\":null}}";
//        CommonSteps.response = httpHelper.requestPost(endPoint,));

    }


    @Then("validate the hidden fields for {string}")
    public void validate_the_hidden_fields_for(String string) throws Throwable {
        ArrayList<String> hidden = CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("hiddenCells");
        System.out.println("Hidden fields "+hidden);
        Assert.assertFalse(hidden.contains("surplusLineInfo.filingDoneBy"));

    }
    @Then("validate the locked fields for product {string} {string} {string}")
    public void validate_the_Locked_fields_forProducts(String string, String productType, String productCode) throws Throwable {
        ArrayList <String> Locked = CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("lockedCells");
        System.out.println(Locked);
        if (productType.equalsIgnoreCase("projectExcess")) {
            Assert.assertFalse(Locked.contains("projectDetails.name"));
            Assert.assertFalse(Locked.contains("projectDetails.street"));
            Assert.assertFalse(Locked.contains("projectDetails.state"));
            Assert.assertFalse(Locked.contains("projectDetails.zipCode"));
            Assert.assertFalse(Locked.contains("projectDetails.city"));

        }
        if (productType.equalsIgnoreCase("projectPrimary")) {
            Assert.assertFalse(Locked.contains("projectDetails.name"));
            Assert.assertFalse(Locked.contains("projectDetails.street"));
            Assert.assertFalse(Locked.contains("projectDetails.state"));
            Assert.assertFalse(Locked.contains("projectDetails.zipCode"));
            Assert.assertFalse(Locked.contains("projectDetails.city"));
            Assert.assertFalse(Locked.contains("deductibleDetails.sirAmount"));
        }
        if (productType.equalsIgnoreCase("primary")) {
                        Assert.assertFalse(Locked.contains("deductibleDetails.sirAmount"));
        }

            else if(productType.equalsIgnoreCase("nyPrimary")){
            Assert.assertFalse(Locked.contains("deductibleDetails.sirAmount"));

        }

        else if (productType.equalsIgnoreCase("excess")) {
            Assert.assertTrue(Locked.contains("scheduleOfValues"));
            Assert.assertTrue(Locked.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertTrue(Locked.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
            Assert.assertTrue(Locked.contains("exposureDetails.personalAdvertisingInjuryLimit"));
            Assert.assertTrue(Locked.contains("exposureDetails.damageToPremisesRented"));
            Assert.assertTrue(Locked.contains("exposureDetails.medicalPaymentsLimit"));
            Assert.assertTrue(Locked.contains("insuredInfo.generalContractor"));

             Assert.assertTrue(Locked.contains("definedStatus"));

            Assert.assertTrue(Locked.contains("projectDetails.name"));
            Assert.assertTrue(Locked.contains("projectDetails.street"));
            Assert.assertTrue(Locked.contains("projectDetails.state"));
            Assert.assertTrue(Locked.contains("projectDetails.zipCode"));
            Assert.assertTrue(Locked.contains("projectDetails.city"));
            if("XAUT".equalsIgnoreCase(productCode)) {
                Assert.assertFalse("issue in nyftzClass for "+productCode,Locked.contains("nyftzClass"));
            }
            else {                Assert.assertTrue(Locked.contains("nyftzClass"));
            }
            Assert.assertTrue(Locked.contains("deductibleDetails.retentionType"));
            Assert.assertTrue(Locked.contains("deductibleDetails.deductibleAmount"));
            Assert.assertTrue(Locked.contains("deductibleDetails.sirAmount"));

        }
        else if(productType.equalsIgnoreCase("primary")){
            Assert.assertTrue(Locked.contains("scheduleOfValues"));
            Assert.assertTrue(Locked.contains("definedStatus"));
            Assert.assertTrue(Locked.contains("insuredInfo.owner"));
            Assert.assertTrue(Locked.contains("exposureDetails.policyAggregateLimit"));
            Assert.assertTrue(Locked.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertTrue(Locked.contains("exposureDetails.eachOccurrenceLimitExcess"));

            Assert.assertTrue(Locked.contains("projectDetails.name"));
            Assert.assertTrue(Locked.contains("projectDetails.street"));
            Assert.assertTrue(Locked.contains("projectDetails.state"));
            Assert.assertTrue(Locked.contains("projectDetails.zipCode"));
            Assert.assertTrue(Locked.contains("projectDetails.city"));
            Assert.assertTrue(Locked.contains("nyftzClass"));

            Assert.assertFalse(Locked.contains("deductibleDetails.retentionType"));
            Assert.assertFalse(Locked.contains("deductibleDetails.deductibleAmount"));
            Assert.assertFalse(Locked.contains("deductibleDetails.sirAmount"));

        }
        else if(productType.equalsIgnoreCase("nyExcess")){
 Assert.assertFalse(Locked.contains("projectDetails.name"));
            Assert.assertFalse(Locked.contains("projectDetails.street"));
            Assert.assertFalse(Locked.contains("projectDetails.state"));
            Assert.assertFalse(Locked.contains("projectDetails.zipCode"));
            Assert.assertFalse(Locked.contains("projectDetails.city"));
        }
        else if(productType.equalsIgnoreCase("nyPrimary")){
            Assert.assertFalse(Locked.contains("projectDetails.name"));
            Assert.assertFalse(Locked.contains("projectDetails.street"));
            Assert.assertFalse(Locked.contains("projectDetails.state"));
            Assert.assertFalse(Locked.contains("projectDetails.zipCode"));
            Assert.assertFalse(Locked.contains("projectDetails.city"));
        }}

    @Then("validate the hidden fields for product {string} {string} {string}")
    public void validate_the_hidden_fields_forProducts(String string, String productType, String productCode) throws Throwable {
        ArrayList<String> hidden = CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("hiddenCells");
        System.out.println(hidden);
        if (productType.equalsIgnoreCase("projectExcess")) {
            Assert.assertTrue("'numberOfUnits' should be Hidden for product:"+productCode, hidden.contains("numberOfUnits"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL' should be visible for productCode: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));

            if ("XANN".equalsIgnoreCase(productCode)) {
                Assert.assertFalse("'priorPolicyNumber' should be visible for productCode: " + productCode, hidden.contains("priorPolicyNumber"));
                Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));

            } else {
                Assert.assertFalse("'constructionCost' should be visible for product:"+productCode, hidden.contains("constructionCost"));
                Assert.assertTrue("'priorPolicyNumber' should be hidden for productCode: " + productCode, hidden.contains("priorPolicyNumber"));
            }
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be Visible", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'definedStatus' should be hidden", hidden.contains("definedStatus"));
            Assert.assertFalse("'projectDetails.name' should be visible", hidden.contains("projectDetails.name"));
            Assert.assertFalse("'projectDetails.street' should be visible", hidden.contains("projectDetails.street"));
            Assert.assertFalse("'projectDetails.state' should be visible", hidden.contains("projectDetails.state"));
            Assert.assertFalse("'projectDetails.zipCode' should be visible", hidden.contains("projectDetails.zipCode"));
            Assert.assertFalse("'projectDetails.city' should be visible", hidden.contains("projectDetails.city"));
            Assert.assertFalse("'projectDetails' should be visible for product:"+productCode, hidden.contains("projectDetails"));
            Assert.assertTrue("'nyftzClass' should be hidden", hidden.contains("nyftzClass"));
            Assert.assertTrue("'eachOccurrenceLimitPrimary' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertFalse("'personalAdvertisingInjuryLimit' should not be hidden", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
            Assert.assertFalse("'damageToPremisesRented' should not be hidden", hidden.contains("exposureDetails.damageToPremisesRented"));
            Assert.assertFalse("'medicalPaymentsLimit' should note be hidden", hidden.contains("exposureDetails.medicalPaymentsLimit"));
            Assert.assertFalse("'generalAggregateLimit' should note be hidden", hidden.contains("exposureDetails.generalAggregateLimit"));
            Assert.assertFalse("'deductibleDetails.retentionType' should be visible", hidden.contains("deductibleDetails.retentionType"));
            Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible", hidden.contains("deductibleDetails.deductibleAmount"));
            Assert.assertFalse("'deductibleDetails.sirAmount' should be visible", hidden.contains("deductibleDetails.sirAmount"));
            Assert.assertTrue("'productsCompletedOperationsAggregateLimit' should be hidden", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));

        }

        if (productType.equalsIgnoreCase("projectPrimary")) {
            Assert.assertTrue("'numberOfUnits' should be Hidden for product:"+productCode, hidden.contains("numberOfUnits"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL' should be visible productCode: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));

            if ("PANN".equalsIgnoreCase(productCode)) {
                Assert.assertFalse("'priorPolicyNumber' should be visible for productCode: " + productCode, hidden.contains("priorPolicyNumber"));
                Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));

            } else {
                Assert.assertTrue("'priorPolicyNumber' should be hidden for productCode: " + productCode, hidden.contains("priorPolicyNumber"));
                Assert.assertFalse("'constructionCost' should be visible for product:"+productCode, hidden.contains("constructionCost"));
            }
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be visible productCode:", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertFalse("'projectDetails' should be visible for product:"+productCode, hidden.contains("projectDetails"));

            Assert.assertTrue("'exposureDetails.eachOccurrenceLimitExcess' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertFalse("'eachOccurrenceLimitPrimary' should be visible productCode:", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertFalse("'productsCompletedOperationsAggregateLimit' should be visible productCode:", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
            Assert.assertFalse("'personalAdvertisingInjuryLimit' should be visible productCode:", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
            Assert.assertFalse("'damageToPremisesRented' should be visible productCode:", hidden.contains("exposureDetails.damageToPremisesRented"));
            Assert.assertFalse("'medicalPaymentsLimit' should be visible productCode:", hidden.contains("exposureDetails.medicalPaymentsLimit"));


            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'definedStatus' should be hidden", hidden.contains("definedStatus"));
            Assert.assertFalse("'projectDetails.name' should be visible", hidden.contains("projectDetails.name"));
            Assert.assertFalse("'projectDetails.street' should be visible", hidden.contains("projectDetails.street"));
            Assert.assertFalse("'projectDetails.state' should be visible", hidden.contains("projectDetails.state"));
            Assert.assertFalse("'projectDetails.zipCode' should be visible", hidden.contains("projectDetails.zipCode"));
            Assert.assertFalse("'projectDetails.city' should be visible", hidden.contains("projectDetails.city"));
            Assert.assertTrue("'nyftzClass' should be hidden", hidden.contains("nyftzClass"));
            Assert.assertFalse("'deductibleDetails.retentionType' should be visible", hidden.contains("deductibleDetails.retentionType"));
            Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible", hidden.contains("deductibleDetails.deductibleAmount"));
            Assert.assertFalse("'deductibleDetails.sirAmount' should be visible", hidden.contains("deductibleDetails.sirAmount"));
        }

        if (productType.equalsIgnoreCase("excess")) {
            Assert.assertFalse("'numberOfUnits' should be visible for product:"+productCode, hidden.contains("numberOfUnits"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible productCode:", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL' should be visible productCode: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be visible productCode:", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'eachOccurrenceLimitPrimary' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));
            Assert.assertTrue("'productsCompletedOperationsAggregateLimit' should be hidden", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
              Assert.assertTrue("'insuredInfo.generalContractor' should be hidden", hidden.contains("insuredInfo.generalContractor"));
            Assert.assertTrue("'projectDetails.name' should be hidden", hidden.contains("projectDetails.name"));
            Assert.assertTrue("'projectDetails.street' should be hidden", hidden.contains("projectDetails.street"));
            Assert.assertTrue("'projectDetails.state' should be hidden", hidden.contains("projectDetails.state"));
            Assert.assertTrue("'projectDetails.zipCode' should be hidden", hidden.contains("projectDetails.zipCode"));
            Assert.assertTrue("'projectDetails.city' should be hidden", hidden.contains("projectDetails.city"));
            Assert.assertTrue("'projectDetails' should be Hidden for product:"+productCode, hidden.contains("projectDetails"));

            if ("XAUT".equalsIgnoreCase(productCode)) {

                        Assert.assertFalse("'exposureDetails.policyAggregateLimit' should be visible for productCode: " + productCode, hidden.contains("exposureDetails.policyAggregateLimit"));
                        Assert.assertFalse("'nyftzClass' should be visible for productCode: " + productCode, hidden.contains("nyftzClass"));
                Assert.assertTrue("'productsCompletedOperationsAggregateLimit' should be hidden", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
                Assert.assertTrue("'personalAdvertisingInjuryLimit' should be hidden", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
                Assert.assertTrue("'damageToPremisesRented' should be hidden", hidden.contains("exposureDetails.damageToPremisesRented"));
                Assert.assertTrue("'medicalPaymentsLimit' should be hidden", hidden.contains("exposureDetails.medicalPaymentsLimit"));

                Assert.assertTrue("'deductibleDetails.retentionType' should be hidden", hidden.contains("deductibleDetails.retentionType"));
                Assert.assertTrue("'deductibleDetails.deductibleAmount' should be hidden", hidden.contains("deductibleDetails.deductibleAmount"));
                Assert.assertTrue("'deductibleDetails.sirAmount' should be hidden", hidden.contains("deductibleDetails.sirAmount"));


            } else {
                Assert.assertTrue("'nyftzClass' should be hidden: " + productCode, hidden.contains("nyftzClass"));
                Assert.assertTrue("'productsCompletedOperationsAggregateLimit' should be Hidden productCode:", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
                Assert.assertFalse("'personalAdvertisingInjuryLimit' should be visible productCode:", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
                Assert.assertFalse("'damageToPremisesRented' should be visible productCode:", hidden.contains("exposureDetails.damageToPremisesRented"));
                Assert.assertFalse("'medicalPaymentsLimit' should be visible productCode:", hidden.contains("exposureDetails.medicalPaymentsLimit"));

                Assert.assertFalse("'deductibleDetails.retentionType' should be visible productCode:", hidden.contains("deductibleDetails.retentionType"));
                Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible productCode:", hidden.contains("deductibleDetails.deductibleAmount"));
                Assert.assertFalse("'deductibleDetails.sirAmount' should be visible productCode:", hidden.contains("deductibleDetails.sirAmount"));
            }
            Assert.assertFalse("'exposureDetails.otherAggregateLimit'should be visible productCode:", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'eachOccurrenceLimitPrimary' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));



        }
        if (productType.equalsIgnoreCase("primary")) {
            Assert.assertTrue("'projectDetails' should be Hidden for product:"+productCode, hidden.contains("projectDetails"));
            Assert.assertFalse("'numberOfUnits' should be visible for product:"+productCode, hidden.contains("numberOfUnits"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL' should be visible for productCode: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));
            Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be hidden", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'definedStatus' should be hidden", hidden.contains("definedStatus"));
            Assert.assertTrue("'insuredInfo.owner' should be hidden", hidden.contains("insuredInfo.owner"));
            Assert.assertTrue("'policyAggregateLimit' should be hidden", hidden.contains("exposureDetails.policyAggregateLimit"));
            Assert.assertTrue("'projectDetails.name' should be hidden", hidden.contains("projectDetails.name"));
            Assert.assertTrue("'projectDetails.street' should be hidden", hidden.contains("projectDetails.street"));
            Assert.assertTrue("'projectDetails.state' should be hidden", hidden.contains("projectDetails.state"));
            Assert.assertTrue("'projectDetails.zipCode' should be hidden", hidden.contains("projectDetails.zipCode"));
            Assert.assertTrue("'projectDetails.city' should be hidden", hidden.contains("projectDetails.city"));
            Assert.assertTrue("'nyftzClass' should be hidden", hidden.contains("nyftzClass"));

            Assert.assertTrue("'exposureDetails.eachOccurrenceLimitExcess' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be hidden", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertFalse("'eachOccurrenceLimitPrimary' should not be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertFalse("'productsCompletedOperationsAggregateLimit' should not be hidden", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
            Assert.assertFalse("'personalAdvertisingInjuryLimit' should not be hidden", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
            Assert.assertFalse("'damageToPremisesRented' should not be hidden", hidden.contains("exposureDetails.damageToPremisesRented"));
            Assert.assertFalse("'medicalPaymentsLimit' should not be hidden", hidden.contains("exposureDetails.medicalPaymentsLimit"));
            Assert.assertFalse("'generalAggregateLimit' should not be hidden", hidden.contains("exposureDetails.generalAggregateLimit"));
            Assert.assertTrue("'exposureDetails.policyAggregateLimit' should  be hidden", hidden.contains("exposureDetails.policyAggregateLimit"));

            Assert.assertFalse("'deductibleDetails.retentionType' should be visible", hidden.contains("deductibleDetails.retentionType"));
            Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible", hidden.contains("deductibleDetails.deductibleAmount"));
            Assert.assertFalse("'deductibleDetails.sirAmount' should be visible", hidden.contains("deductibleDetails.sirAmount"));
        }

        if (productType.equalsIgnoreCase("nyExcess")) {
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL'should be visible for product type: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible for product type:", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));
            Assert.assertTrue("'policyAggregateLimit' should be hidden", hidden.contains("exposureDetails.policyAggregateLimit"));
            Assert.assertFalse("'eachOccurrenceLimitExcess' should be visible for product type:", hidden.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL' should be visible for product type: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be visible for product type:", hidden.contains("exposureDetails.otherAggregateLimit"));

            if ("XNAG".equalsIgnoreCase(productCode)) {
                Assert.assertTrue("'projectDetails.name' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.name"));
                Assert.assertTrue("'projectDetails.street' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.street"));
                Assert.assertTrue("'projectDetails.state' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.state"));
                Assert.assertTrue("'projectDetails.zipCode' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.zipCode"));
                Assert.assertTrue("'projectDetails.city' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.city"));
                Assert.assertTrue("'insuredInfo.owner' should be hidden for productCode: " + productCode, hidden.contains("insuredInfo.owner"));
                Assert.assertTrue("'insuredInfo.generalContractor' should be hidden for productCode: " + productCode, hidden.contains("insuredInfo.generalContractor"));
                Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
                Assert.assertTrue("'eachOccurrenceLimitPrimary' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
                Assert.assertFalse("'personalAdvertisingInjuryLimit' should be visible for product type:", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
                Assert.assertFalse("'damageToPremisesRented' should be visible for product type:", hidden.contains("exposureDetails.damageToPremisesRented"));
                Assert.assertFalse("'medicalPaymentsLimit' should be visible for product type:", hidden.contains("exposureDetails.medicalPaymentsLimit"));
                Assert.assertFalse("'generalAggregateLimit' should be visible for product type:", hidden.contains("exposureDetails.generalAggregateLimit"));

                Assert.assertFalse("'deductibleDetails.retentionType' should be visible", hidden.contains("deductibleDetails.retentionType"));
                Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible", hidden.contains("deductibleDetails.deductibleAmount"));
                Assert.assertFalse("'deductibleDetails.sirAmount' should be visible", hidden.contains("deductibleDetails.sirAmount"));
                Assert.assertFalse("'numberOfUnits' should be visible for product:"+productCode, hidden.contains("numberOfUnits"));
                Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));
                Assert.assertTrue("'projectDetails' should be Hidden for product:"+productCode, hidden.contains("projectDetails"));

            } else {
                Assert.assertTrue("'numberOfUnits' should be Hidden for product:"+productCode, hidden.contains("numberOfUnits"));
                Assert.assertFalse("'constructionCost' should be visible for product:"+productCode, hidden.contains("constructionCost"));
                Assert.assertFalse("'projectDetails.name' should be visible for productCode: " + productCode, hidden.contains("projectDetails.name"));
                Assert.assertFalse("'projectDetails.street' should be visible for productCode: " + productCode, hidden.contains("projectDetails.street"));
                Assert.assertFalse("'projectDetails.state' should be visible for productCode: " + productCode, hidden.contains("projectDetails.state"));
                Assert.assertFalse("'projectDetails.zipCode' should be visible for productCode: " + productCode, hidden.contains("projectDetails.zipCode"));
                Assert.assertFalse("'projectDetails.city' should be visible for productCode: " + productCode, hidden.contains("projectDetails.city"));
                Assert.assertFalse("'insuredInfo.owner' should be visible for productCode: " + productCode, hidden.contains("insuredInfo.owner"));
                Assert.assertFalse("'insuredInfo.generalContractor' should be visible for productCode: " + productCode, hidden.contains("insuredInfo.generalContractor"));

                Assert.assertFalse("'personalAdvertisingInjuryLimit' should be visible for product type:", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
                Assert.assertFalse("'damageToPremisesRented'should be visible for product type:", hidden.contains("exposureDetails.damageToPremisesRented"));
                Assert.assertFalse("'medicalPaymentsLimit' should be visible for product type:", hidden.contains("exposureDetails.medicalPaymentsLimit"));
                Assert.assertFalse("'generalAggregateLimit' should be visible for product type:", hidden.contains("exposureDetails.generalAggregateLimit"));

                Assert.assertFalse("'deductibleDetails.retentionType' should be visible for product type:", hidden.contains("deductibleDetails.retentionType"));
                Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible for product type:", hidden.contains("deductibleDetails.deductibleAmount"));
                Assert.assertFalse("'deductibleDetails.sirAmount' should be visible for product type:", hidden.contains("deductibleDetails.sirAmount"));
                Assert.assertFalse("'projectDetails' should be visible for product:"+productCode, hidden.contains("projectDetails"));

            }
            Assert.assertFalse("'nyftzClass' should be visible", hidden.contains("nyftzClass"));
        }

        if (productType.equalsIgnoreCase("nyPrimary")) {
            Assert.assertFalse("'exposureDetails.attachmentTotalLimitsInclGL'  should be visible for product type: " + productCode, hidden.contains("exposureDetails.attachmentTotalLimitsInclGL"));
            Assert.assertTrue("'scheduleOfValues' should be hidden", hidden.contains("scheduleOfValues"));
            Assert.assertTrue("'definedStatus' should be hidden", hidden.contains("definedStatus"));
            Assert.assertTrue("'policyAggregateLimit' should be hidden", hidden.contains("exposureDetails.policyAggregateLimit"));
            Assert.assertTrue("'eachOccurrenceLimitExcess' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be visible for product type:", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertFalse("'exposureDetails.numberOfGeneralAggregateReinstatements' should be visible for product type:", hidden.contains("exposureDetails.numberOfGeneralAggregateReinstatements"));

            if ("PNAG".equalsIgnoreCase(productCode)) {
                Assert.assertTrue("'projectDetails.name' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.name"));
                Assert.assertTrue("'projectDetails.street' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.street"));
                Assert.assertTrue("'projectDetails.state' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.state"));
                Assert.assertTrue("'projectDetails.zipCode' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.zipCode"));
                Assert.assertTrue("'projectDetails.city' should be hidden for productCode: " + productCode, hidden.contains("projectDetails.city"));
                Assert.assertTrue("'insuredInfo.owner' should be hidden for productCode: " + productCode, hidden.contains("insuredInfo.owner"));
                Assert.assertTrue("'insuredInfo.generalContractor' should be hidden for productCode: " + productCode, hidden.contains("insuredInfo.generalContractor"));
                Assert.assertFalse("'numberOfUnits' should be visible for product:"+productCode, hidden.contains("numberOfUnits"));
                Assert.assertTrue("'constructionCost' should be Hidden for product:"+productCode, hidden.contains("constructionCost"));
                Assert.assertTrue("'projectDetails' should be Hidden for product:"+productCode, hidden.contains("projectDetails"));
            } else {
                Assert.assertTrue("'numberOfUnits' should be Hidden for product:"+productCode, hidden.contains("numberOfUnits"));
                Assert.assertFalse("'projectDetails.name' should be visible for productCode: " + productCode, hidden.contains("projectDetails.name"));
                Assert.assertFalse("'projectDetails.street' should be visible for productCode: " + productCode, hidden.contains("projectDetails.street"));
                Assert.assertFalse("'projectDetails.state' should be visible for productCode: " + productCode, hidden.contains("projectDetails.state"));
                Assert.assertFalse("'projectDetails.zipCode' should be visible for productCode: " + productCode, hidden.contains("projectDetails.zipCode"));
                Assert.assertFalse("'projectDetails.city' should be visible for productCode: " + productCode, hidden.contains("projectDetails.city"));
                Assert.assertFalse("'insuredInfo.owner' should be visible for productCode: " + productCode, hidden.contains("insuredInfo.owner"));
                Assert.assertFalse("'insuredInfo.generalContractor' should be visible for productCode: " + productCode, hidden.contains("insuredInfo.generalContractor"));
                Assert.assertFalse("'constructionCost' should be visible for product:"+productCode, hidden.contains("constructionCost"));
                Assert.assertFalse("'projectDetails' should be visible for product:"+productCode, hidden.contains("projectDetails"));

            }
            Assert.assertTrue("'exposureDetails.eachOccurrenceLimitExcess' should be hidden", hidden.contains("exposureDetails.eachOccurrenceLimitExcess"));
            Assert.assertFalse("'exposureDetails.otherAggregateLimit' should be visible for product type:", hidden.contains("exposureDetails.otherAggregateLimit"));
            Assert.assertFalse("'eachOccurrenceLimitPrimary' should be visible productCode:", hidden.contains("exposureDetails.eachOccurrenceLimitPrimary"));
            Assert.assertFalse("'productsCompletedOperationsAggregateLimit'should be visible for product type:", hidden.contains("exposureDetails.productsCompletedOperationsAggregateLimit"));
            Assert.assertFalse("'personalAdvertisingInjuryLimit' should be visible for product type:", hidden.contains("exposureDetails.personalAdvertisingInjuryLimit"));
            Assert.assertFalse("'damageToPremisesRented' should be visible productCode:", hidden.contains("exposureDetails.damageToPremisesRented"));
            Assert.assertFalse("'medicalPaymentsLimit' should be visible productCode:", hidden.contains("exposureDetails.medicalPaymentsLimit"));
            Assert.assertFalse("'deductibleDetails.retentionType' should be visible", hidden.contains("deductibleDetails.retentionType"));
            Assert.assertFalse("'deductibleDetails.deductibleAmount' should be visible", hidden.contains("deductibleDetails.deductibleAmount"));
            Assert.assertFalse("'deductibleDetails.sirAmount' should be visible", hidden.contains("deductibleDetails.sirAmount"));
            Assert.assertFalse("'nyftzClass' should be visible", hidden.contains("nyftzClass"));
        }

        //Prior policy number check
        if ("XANN".equalsIgnoreCase(productCode)||"XCAN".equalsIgnoreCase(productCode)||"XOAN".equalsIgnoreCase(productCode)||"EIAN".equalsIgnoreCase(productCode)||"XAUT".equalsIgnoreCase(productCode)||"XNAG".equalsIgnoreCase(productCode)||"PANN".equalsIgnoreCase(productCode)||"PIAN".equalsIgnoreCase(productCode)||"PCAN".equalsIgnoreCase(productCode)||"POAN".equalsIgnoreCase(productCode)||"PNAG".equalsIgnoreCase(productCode)) {
            Assert.assertFalse("'priorPolicyNumber' should be visible for product:"+productCode, hidden.contains("priorPolicyNumber"));

        } else {
            Assert.assertTrue("'priorPolicyNumber' should be Hidden for product:"+productCode, hidden.contains("priorPolicyNumber"));
        }

    }


}
