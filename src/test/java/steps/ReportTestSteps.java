package steps;

import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import pojo.graphql.GraphQLQuery;
import utils.Constant;

public class ReportTestSteps extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(ReportTestSteps.class);
    //    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    Constant constant=new Constant();
    @When("the user send POST request {string} to generate Report")
    public void the_user_send_post_request_to_generate_report(String endPoint, String requestBody) throws Throwable {
        CommonSteps.response=httpHelper.requestPost(endPoint,requestBody);

    }
}
