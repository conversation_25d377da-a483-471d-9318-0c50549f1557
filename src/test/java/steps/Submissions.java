package steps;

import helpers.PolicyHelper;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import pojo.graphql.GraphQLQuery;
import steps.brokrageHub.BrokerageTestSteps;
import utils.Constant;

public class Submissions extends StepsTemplate  {
    private static final Logger log = LogManager.getLogger(BrokerageTestSteps.class);
    //    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    Constant constant=new Constant();
    String Submissionid;
    PolicyHelper policyHelper=new PolicyHelper();

    public class PostSubmissionSteps {

        private String submissionPayload;
        private Response response;

        @Given("I have the submission payload {string} {string} {string}")
        public void i_have_the_submission_payload(String productName,String productState,String productType) throws Throwable {
            policyHelper.validateMoveSubmissionToInforce(productName, productState, productType);
        }
        }

            @When("save submission id")
    public void save_submission_id() {
        Submissionid=httpResponse.getBody().path("result[0].submissionId");
    }



}
