package steps;

import helpers.PolicyHelper;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import pojo.graphql.GraphQLQuery;
import steps.brokrageHub.BrokerageTestSteps;
import utils.Constant;

public class PolicySteps extends StepsTemplate {
    private static final Logger log = LogManager.getLogger(BrokerageTestSteps.class);
//    public Response response;
    GraphQLQuery mutation =new GraphQLQuery();
    Constant constant=new Constant();
    PolicyHelper policyHelper=new PolicyHelper();

    static String  SubmissionId,policyID;
    @When("POST {string} to save submissions ID")
    public void post_to_save_submissions_id(String endPoint, String requestBody) throws Throwable {
        CommonSteps.response=httpHelper.requestPost(endPoint,requestBody);
        SubmissionId= CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].submissionId");
        policyID= CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].policyId");


    }
    @When("Get policy data {string}")
    public void getsubmissions_id(String endPoint) throws Throwable {
        CommonSteps.response=httpHelper.requestGet(endPoint.replace(":submissionsID", SubmissionId));

    }
    @When("Get endorsement transactions {string}")
    public void get_endorsement_transactions(String endPoint) throws Throwable {
        CommonSteps.response=httpHelper.requestGet(endPoint.replace(":policyID", policyID));


    }


    @Given("I have the submission payload {string} {string} {string}")
    public void i_have_the_submission_payload(String productName,String productState,String productType) throws Throwable {
        policyHelper.validateMoveSubmissionToInforce(productName,productState,productType);
    }
    @When("POST {string} to Test Template")
    public void Post_Template_Test(String endPoint, String requestBody) throws Throwable {
        CommonSteps.response=httpHelper.requestPost(endPoint,requestBody);
        SubmissionId= CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].submissionId");
        policyID= CommonSteps.response.then().log().ifStatusCodeIsEqualTo(200).extract().path("data[0].policyId");

    }



    @When("assign submission")
    public void assignSubmission() {
    }

    @And("move submission to Quote")
    public void moveSubmissionToQuote() {
    }

    @And("validate the folder is synced in quote")
    public void validateTheFolderIsSyncedInQuote() {
    }

    @And("create policy with in force status")
    public void createPolicyWithInForceStatus() {
    }

    @And("validate the folder is synced in infoce and invoice is created")
    public void validateTheFolderIsSyncedInInfoceAndInvoiceIsCreated() {
    }

    @And("create endorsement")
    public void createEndorsement() {
    }

    @And("validate policy status after endorsement")
    public void validatePolicyStatusAfterEndorsement() {

    }
}
