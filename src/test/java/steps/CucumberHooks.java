package steps;

import helpers.KeycloakLogin;
import helpers.PropertiesHelper;
import io.cucumber.java.After;
import io.cucumber.java.Before;
import io.cucumber.java.Scenario;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class CucumberHooks {

    private static final Logger logger = LogManager.getLogger(CucumberHooks.class);
    private static boolean dunit = false;

    @Before(order =0)
    public void beforeAll(Scenario scenario) throws Throwable {
        logger.info("Executing scenario= " + scenario.getName());
        readAndSetProperties();

        if(!dunit) { //this gets executed only once before all scenarios
            dunit = true;
            KeycloakLogin keycloakLogin = new KeycloakLogin();
            keycloakLogin.performLogin();
        }
    }


    public void readAndSetProperties() {

        logger.info("Loading properties file..");
        PropertiesHelper propertiesHelper = new PropertiesHelper();
        PropertiesHelper.getMailProperty("message.body");
        if (null != System.getProperty("tier")&&null != System.getProperty("submission.client.id")
        ) {
            PropertiesHelper.setProperty("tier", System.getProperty("tier"));
            PropertiesHelper.setProperty("ssh.key.qa", System.getProperty("ssh.key"));
            PropertiesHelper.setProperty("submission.client.id", System.getProperty("submission.client.id"));
            PropertiesHelper.setProperty("db.username.qa", System.getProperty("db.username."+PropertiesHelper.getProperty("tier").toLowerCase()));
            PropertiesHelper.setProperty("db.password.qa", System.getProperty("db.password."+PropertiesHelper.getProperty("tier").toLowerCase()));
            logger.info("submissions client id =" + System.getProperty("submission.client.id"));
            logger.info("SlackToken =" + System.getProperty("slackToken"));

        }
    }
// TO-DO: To be deleted after Pstgress migration deployed on prod
    @Before(order =1, value="@emailAdapter")
    public void openSessions() throws Throwable {
        readAndSetProperties();
//        if(SSHConnector.getSSHSessions()==null){
//            SSHConnector.connectBastionServer();
//            System.out.println("Starting Bastion from cucumber hooks");
//        }
//        if(SQLSessions.getSession()==null){
//            logger.info("JUnit before Class hook started from cucumber hooks;");
//            SQLSessions.setSQLSessions();
//        }
    }
// TO-DO: To be deleted after Pstgress migration deployed on prod
    @After(value="@emailAdapter")
    public static void closeSessions() throws Throwable {

//        if(SQLSessions.getSession()!=null){
//            logger.info(" gracefully shutting down db connections");
//            ReadDB.closeDBSession();
//        }
//        if(SSHConnector.getSSHSessions()!=null){
//            SSHConnector.closeBastionConnections();
//            ReadDB.closeDBSession();
//            System.out.println("gracefully shutting Bastion from cucumber hooks");        }


    }

}
