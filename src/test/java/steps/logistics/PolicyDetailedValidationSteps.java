package steps.logistics;

import helpers.HttpHelper;
import helpers.PolicySubmissionHelper;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import pojo.logistics.PolicySubmissionRequest;
import steps.CommonSteps;
import steps.StepsTemplate;

import java.util.List;
import java.util.Map;

public class PolicyDetailedValidationSteps extends StepsTemplate {

    private static final Logger log = LogManager.getLogger(PolicyDetailedValidationSteps.class);
    private String requestPayload;
    private final String POLICY_SUBMISSION_ENDPOINT = "/core/policy-service/submissions";

    @Given("I have a valid policy submission payload")
    public void i_have_valid_policy_submission_payload() {
        PolicySubmissionRequest request = PolicySubmissionHelper.createValidSubmissionRequest();
        requestPayload = PolicySubmissionHelper.toJsonString(request);
        log.info("Valid policy submission payload prepared");
    }

    @Given("I have policy submission payload with LOB {string} and class {string}")
    public void i_have_policy_submission_payload_with_lob_and_class(String lob, String classType) {
        PolicySubmissionRequest request = PolicySubmissionHelper.createSubmissionRequestWithLobAndClass(lob, classType);
        requestPayload = PolicySubmissionHelper.toJsonString(request);
        log.info("Policy submission payload prepared with LOB: {} and class: {}", lob, classType);
    }

    @When("I send POST request to create policy submission")
    public void i_send_post_request_to_create_policy_submission() throws Throwable {
        CommonSteps.response = httpHelper.requestPost(POLICY_SUBMISSION_ENDPOINT, requestPayload);
        log.info("POST request sent to create policy submission endpoint");
    }

    @When("I send POST request to create policy submission without authorization")
    public void i_send_post_request_to_create_policy_submission_without_authorization() throws Throwable {
        HttpHelper unauthorizedHelper = new HttpHelper(false);
        CommonSteps.response = unauthorizedHelper.requestPost(POLICY_SUBMISSION_ENDPOINT, requestPayload);
        log.info("POST request sent to create policy submission endpoint without authorization");
    }

    @Then("validate detailed policy response structure")
    public void validate_detailed_policy_response_structure() {
        // Validate top-level response structure
        Assert.assertNotNull("Response data should not be null",
            CommonSteps.response.jsonPath().get("data"));
        Assert.assertNotNull("Success flag should not be null",
            CommonSteps.response.jsonPath().get("success"));
        Assert.assertNotNull("Message should not be null",
            CommonSteps.response.jsonPath().get("message"));
        Assert.assertNotNull("Timestamp should not be null",
            CommonSteps.response.jsonPath().get("timestamp"));

        Boolean success = CommonSteps.response.jsonPath().getBoolean("success");
        Assert.assertTrue("Success should be true", success);

        String message = CommonSteps.response.jsonPath().getString("message");
        Assert.assertEquals("Message should indicate success", "Request Processed Successfully", message);

        log.info("Detailed policy response structure validated successfully");
    }

    @Then("validate policy core information")
    public void validate_policy_core_information() {
        // Validate core policy fields
        Assert.assertNotNull("Submission ID should not be null",
            CommonSteps.response.jsonPath().get("data.submissionId"));
        Assert.assertNotNull("Status should not be null",
            CommonSteps.response.jsonPath().get("data.status"));
        Assert.assertNotNull("Class should not be null",
            CommonSteps.response.jsonPath().get("data.class"));
        Assert.assertNotNull("LOB should not be null",
            CommonSteps.response.jsonPath().get("data.lob"));
        Assert.assertNotNull("Client ID should not be null",
            CommonSteps.response.jsonPath().get("data.clientId"));
        Assert.assertNotNull("Business type should not be null",
            CommonSteps.response.jsonPath().get("data.businessType"));

        // Validate proposal information
        Assert.assertNotNull("Proposal number should not be null",
            CommonSteps.response.jsonPath().get("data.proposalNumber"));
        Assert.assertNotNull("Proposal date should not be null",
            CommonSteps.response.jsonPath().get("data.proposalDate"));
        Assert.assertNotNull("Underwriter name should not be null",
            CommonSteps.response.jsonPath().get("data.underwriterName"));

        log.info("Policy core information validated successfully");
    }

    @Then("validate insured information details")
    public void validate_insured_information_details() {
        // Validate insured info structure
        Assert.assertNotNull("Insured info should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo"));

        // Validate basic insured fields
        Assert.assertNotNull("Business name should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.businessName"));
        Assert.assertNotNull("Insured ID should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.insuredId"));
        Assert.assertNotNull("Insured client ID should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.insuredClientId"));
        Assert.assertNotNull("State should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.state"));

        // Validate address information
        Assert.assertNotNull("City should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.city"));
        Assert.assertNotNull("Zipcode should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.zipcode"));

        // Validate dates
        Assert.assertNotNull("Effective date should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.effectiveDate"));
        Assert.assertNotNull("Expiry date should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.expiryDate"));

        // Validate company information
        Assert.assertNotNull("Company name should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.companyName"));
        Assert.assertNotNull("Company address should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.companyAddress"));

        log.info("Insured information details validated successfully");
    }

    @Then("validate premium and rating information")
    public void validate_premium_and_rating_information() {
        // Validate premium fields
        Assert.assertNotNull("Premium should not be null",
            CommonSteps.response.jsonPath().get("data.premium"));
        Assert.assertNotNull("Rate should not be null",
            CommonSteps.response.jsonPath().get("data.rate"));

        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");

        Assert.assertTrue("Premium should be non-negative", premium >= 0);
        Assert.assertTrue("Rate should be non-negative", rate >= 0);

        // Validate premium summary
        Assert.assertNotNull("Premium summary should not be null",
            CommonSteps.response.jsonPath().get("data.premiumSummary"));

        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("data.premiumSummary");
        Assert.assertTrue("Premium summary should contain totalPremium",
            premiumSummary.containsKey("totalPremium"));
        Assert.assertTrue("Premium summary should contain brokerCommission",
            premiumSummary.containsKey("brokerCommission"));
        Assert.assertTrue("Premium summary should contain triaPremium",
            premiumSummary.containsKey("triaPremium"));

        log.info("Premium and rating information validated successfully");
    }

    @Then("validate commodity types information")
    public void validate_commodity_types_information() {
        List<Map<String, Object>> commodityTypes = CommonSteps.response.jsonPath().getList("data.commodityTypes");
        Assert.assertNotNull("Commodity types should not be null", commodityTypes);
        Assert.assertFalse("Commodity types should not be empty", commodityTypes.isEmpty());

        // Validate each commodity type has required fields
        for (Map<String, Object> commodity : commodityTypes) {
            Assert.assertTrue("Commodity should have label", commodity.containsKey("label"));
            Assert.assertTrue("Commodity should have key", commodity.containsKey("key"));
            Assert.assertTrue("Commodity should have percentage", commodity.containsKey("percentage"));

            String label = (String) commodity.get("label");
            String key = (String) commodity.get("key");
            Assert.assertNotNull("Commodity label should not be null", label);
            Assert.assertNotNull("Commodity key should not be null", key);
        }

        log.info("Commodity types information validated successfully. Found {} commodity types", commodityTypes.size());
    }

    @Then("validate geographical information")
    public void validate_geographical_information() {
        // Validate geographical areas
        List<Map<String, Object>> geographicalAreas = CommonSteps.response.jsonPath().getList("data.geographicalArea");
        Assert.assertNotNull("Geographical areas should not be null", geographicalAreas);
        Assert.assertFalse("Geographical areas should not be empty", geographicalAreas.isEmpty());

        for (Map<String, Object> area : geographicalAreas) {
            Assert.assertTrue("Area should have label", area.containsKey("label"));
            Assert.assertTrue("Area should have key", area.containsKey("key"));
        }

        // Validate geographical scope
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);
        Assert.assertTrue("Geographical scope should have title", geographicalScope.containsKey("title"));
        Assert.assertTrue("Geographical scope should have statement", geographicalScope.containsKey("statement"));

        log.info("Geographical information validated successfully");
    }

    @Then("validate conveyance information")
    public void validate_conveyance_information() {
        Map<String, Object> conveyance = CommonSteps.response.jsonPath().getMap("data.conveyance");
        Assert.assertNotNull("Conveyance should not be null", conveyance);

        // Validate conveyance types
        Assert.assertTrue("Conveyance should have ocean", conveyance.containsKey("ocean"));
        Assert.assertTrue("Conveyance should have aircraft", conveyance.containsKey("aircraft"));
        Assert.assertTrue("Conveyance should have domesticTruckOrRail", conveyance.containsKey("domesticTruckOrRail"));
        Assert.assertTrue("Conveyance should have internationTruckOrRail", conveyance.containsKey("internationTruckOrRail"));

        // Validate ocean conveyance structure
        Map<String, Object> ocean = (Map<String, Object>) conveyance.get("ocean");
        if (ocean != null) {
            Assert.assertTrue("Ocean should have enabled flag", ocean.containsKey("enabled"));
        }

        log.info("Conveyance information validated successfully");
    }

    @Then("validate terms and conditions structure")
    public void validate_terms_and_conditions_structure() {
        Map<String, Object> termsAndConditions = CommonSteps.response.jsonPath().getMap("data.termsAndConditions");
        Assert.assertNotNull("Terms and conditions should not be null", termsAndConditions);

        // Validate different class types have terms
        if (termsAndConditions.containsKey("SI")) {
            List<Map<String, Object>> siTerms = (List<Map<String, Object>>) termsAndConditions.get("SI");
            Assert.assertNotNull("SI terms should not be null", siTerms);

            for (Map<String, Object> term : siTerms) {
                Assert.assertTrue("Term should have value", term.containsKey("value"));
                Assert.assertTrue("Term should have id", term.containsKey("id"));
            }
        }

        if (termsAndConditions.containsKey("CL")) {
            List<Map<String, Object>> clTerms = (List<Map<String, Object>>) termsAndConditions.get("CL");
            Assert.assertNotNull("CL terms should not be null", clTerms);
        }

        if (termsAndConditions.containsKey("general")) {
            List<Map<String, Object>> generalTerms = (List<Map<String, Object>>) termsAndConditions.get("general");
            Assert.assertNotNull("General terms should not be null", generalTerms);
        }

        log.info("Terms and conditions structure validated successfully");
    }

    @Then("validate additional insured information")
    public void validate_additional_insured_information() {
        List<Map<String, Object>> additionalInsured = CommonSteps.response.jsonPath().getList("data.additionalInsuredInfo");
        Assert.assertNotNull("Additional insured info should not be null", additionalInsured);

        if (!additionalInsured.isEmpty()) {
            for (Map<String, Object> insured : additionalInsured) {
                Assert.assertTrue("Additional insured should have id", insured.containsKey("id"));
                Assert.assertTrue("Additional insured should have name", insured.containsKey("name"));
                Assert.assertTrue("Additional insured should have address", insured.containsKey("address"));
                Assert.assertTrue("Additional insured should have city", insured.containsKey("city"));
                Assert.assertTrue("Additional insured should have state", insured.containsKey("state"));
                Assert.assertTrue("Additional insured should have zipcode", insured.containsKey("zipcode"));
            }
        }

        log.info("Additional insured information validated successfully");
    }

    @Then("validate broker information")
    public void validate_broker_information() {
        Map<String, Object> brokerInfo = CommonSteps.response.jsonPath().getMap("data.brokerInfo");
        Assert.assertNotNull("Broker info should not be null", brokerInfo);

        Assert.assertTrue("Broker info should have brokerOfRecord", brokerInfo.containsKey("brokerOfRecord"));
        Assert.assertTrue("Broker info should have brokerContact", brokerInfo.containsKey("brokerContact"));

        log.info("Broker information validated successfully");
    }

    @Then("validate loss history information")
    public void validate_loss_history_information() {
        Map<String, Object> lossHistory = CommonSteps.response.jsonPath().getMap("data.lossHistory");
        Assert.assertNotNull("Loss history should not be null", lossHistory);

        Assert.assertTrue("Loss history should have totalRaterPremium", lossHistory.containsKey("totalRaterPremium"));
        Assert.assertTrue("Loss history should have raterPremiumLossPick", lossHistory.containsKey("raterPremiumLossPick"));
        Assert.assertTrue("Loss history should have lossAdjustedPremiumLossPick", lossHistory.containsKey("lossAdjustedPremiumLossPick"));

        // Validate nested loss details
        Assert.assertTrue("Loss history should have shipperInterestCargo", lossHistory.containsKey("shipperInterestCargo"));
        Assert.assertTrue("Loss history should have cargoLiabilities", lossHistory.containsKey("cargoLiabilities"));
        Assert.assertTrue("Loss history should have warehouseLegalLiability", lossHistory.containsKey("warehouseLegalLiability"));

        log.info("Loss history information validated successfully");
    }

    @Then("validate valuation information")
    public void validate_valuation_information() {
        Map<String, Object> valuation = CommonSteps.response.jsonPath().getMap("data.valuation");
        Assert.assertNotNull("Valuation should not be null", valuation);

        Assert.assertTrue("Valuation should have title", valuation.containsKey("title"));
        Assert.assertTrue("Valuation should have statement", valuation.containsKey("statement"));
        Assert.assertTrue("Valuation should have statement2", valuation.containsKey("statement2"));
        Assert.assertTrue("Valuation should have statement3", valuation.containsKey("statement3"));

        String title = (String) valuation.get("title");
        Assert.assertEquals("Valuation title should be correct", "3. Valuation", title);

        log.info("Valuation information validated successfully");
    }

    @Then("the response should contain LOB {string}")
    public void response_should_contain_lob(String expectedLob) {
        List<String> lobList = CommonSteps.response.jsonPath().getList("data.lob");
        Assert.assertNotNull("LOB list should not be null", lobList);
        Assert.assertTrue("LOB list should contain " + expectedLob, lobList.contains(expectedLob));
        log.info("Validated response contains LOB: {}", expectedLob);
    }

    @Then("the response should contain class {string}")
    public void response_should_contain_class(String expectedClass) {
        List<String> classList = CommonSteps.response.jsonPath().getList("data.class");
        Assert.assertNotNull("Class list should not be null", classList);
        Assert.assertTrue("Class list should contain " + expectedClass, classList.contains(expectedClass));
        log.info("Validated response contains class: {}", expectedClass);
    }

    @Then("the response should contain creation timestamp")
    public void response_should_contain_creation_timestamp() {
        Assert.assertNotNull("Created at should not be null",
            CommonSteps.response.jsonPath().get("data.createdAt"));
        Assert.assertNotNull("Data ID timestamp should not be null",
            CommonSteps.response.jsonPath().get("data._id.timestamp"));
        Assert.assertNotNull("Data ID date should not be null",
            CommonSteps.response.jsonPath().get("data._id.date"));
        log.info("Creation timestamp validation completed");
    }

    @Then("the response should contain update timestamp")
    public void response_should_contain_update_timestamp() {
        Assert.assertNotNull("Updated at should not be null",
            CommonSteps.response.jsonPath().get("data.updatedAt"));
        log.info("Update timestamp validation completed");
    }

    @Then("the response should contain created by information")
    public void response_should_contain_created_by_information() {
        Assert.assertNotNull("Created by should not be null",
            CommonSteps.response.jsonPath().get("data.createdBy"));
        String createdBy = CommonSteps.response.jsonPath().getString("data.createdBy");
        Assert.assertFalse("Created by should not be empty", createdBy.isEmpty());
        log.info("Created by information validation completed");
    }

    @Then("the response should contain updated by information")
    public void response_should_contain_updated_by_information() {
        Assert.assertNotNull("Updated by should not be null",
            CommonSteps.response.jsonPath().get("data.updatedBy"));
        String updatedBy = CommonSteps.response.jsonPath().getString("data.updatedBy");
        Assert.assertFalse("Updated by should not be empty", updatedBy.isEmpty());
        log.info("Updated by information validation completed");
    }

    @Then("the response should contain business type {string}")
    public void response_should_contain_business_type(String expectedBusinessType) {
        String businessType = CommonSteps.response.jsonPath().getString("data.businessType");
        Assert.assertNotNull("Business type should not be null", businessType);
        Assert.assertEquals("Business type should match", expectedBusinessType, businessType);
        log.info("Business type validation completed: {}", businessType);
    }

    @Then("the response should contain valid proposal number")
    public void response_should_contain_valid_proposal_number() {
        String proposalNumber = CommonSteps.response.jsonPath().getString("data.proposalNumber");
        Assert.assertNotNull("Proposal number should not be null", proposalNumber);
        Assert.assertFalse("Proposal number should not be empty", proposalNumber.isEmpty());
        Assert.assertTrue("Proposal number should follow expected format",
            proposalNumber.matches("\\d{3}_L\\d+"));
        log.info("Proposal number validation completed: {}", proposalNumber);
    }

    @Then("the response should contain underwriter name")
    public void response_should_contain_underwriter_name() {
        String underwriterName = CommonSteps.response.jsonPath().getString("data.underwriterName");
        Assert.assertNotNull("Underwriter name should not be null", underwriterName);
        Assert.assertFalse("Underwriter name should not be empty", underwriterName.isEmpty());
        log.info("Underwriter name validation completed: {}", underwriterName);
    }

    @Then("the premium should be greater than zero")
    public void premium_should_be_greater_than_zero() {
        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertTrue("Premium should be greater than zero", premium > 0);
        log.info("Premium validation completed: {}", premium);
    }

    @Then("the rate should be greater than zero")
    public void rate_should_be_greater_than_zero() {
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertTrue("Rate should be greater than zero", rate > 0);
        log.info("Rate validation completed: {}", rate);
    }

    @Then("validate specific rate value {double}")
    public void validate_specific_rate_value(Double expectedRate) {
        Double actualRate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Assert.assertNotNull("Rate should not be null", actualRate);
        Assert.assertEquals("Rate should match expected value", expectedRate, actualRate, 0.001);
        log.info("Rate validation completed. Expected: {}, Actual: {}", expectedRate, actualRate);
    }

    @Then("validate specific premium value {double}")
    public void validate_specific_premium_value(Double expectedPremium) {
        Double actualPremium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Assert.assertNotNull("Premium should not be null", actualPremium);
        Assert.assertEquals("Premium should match expected value", expectedPremium, actualPremium, 0.01);
        log.info("Premium validation completed. Expected: {}, Actual: {}", expectedPremium, actualPremium);
    }

    @Then("validate rate and premium values")
    public void validate_rate_and_premium_values() {
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");

        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertNotNull("Premium should not be null", premium);

        // Validate expected values based on the provided JSON
        Assert.assertEquals("Rate should be 0.208", 0.208, rate, 0.001);
        Assert.assertEquals("Premium should be 51974", 51974.0, premium, 0.01);

        log.info("Rate and premium validation completed. Rate: {}, Premium: {}", rate, premium);
    }

    @Then("validate rate is approximately {double}")
    public void validate_rate_is_approximately(Double expectedRate) {

        Double actualRate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Assert.assertNotNull("Rate should not be null", actualRate);

        // Allow for small floating point differences
        double tolerance = 0.001;
        Assert.assertTrue("Rate should be approximately " + expectedRate + " (±" + tolerance + "), but was " + actualRate,
            Math.abs(actualRate - expectedRate) <= tolerance);

        log.info("Rate approximately validation completed. Expected: {} (±{}), Actual: {}",
            expectedRate, tolerance, actualRate);
    }
    @Then("validate rate is approximately {double} in calculate premium call")
    public void validate_rate_is_approximately_Calculate_premium(Double expectedRate) {

        Double actualRate = CommonSteps.response.jsonPath().getDouble("rate");
        Assert.assertNotNull("Rate should not be null", actualRate);

        // Allow for small floating point differences
        double tolerance = 0.001;
        Assert.assertTrue("Rate should be approximately " + expectedRate + " (±" + tolerance + "), but was " + actualRate,
                Math.abs(actualRate - expectedRate) <= tolerance);

        log.info("Rate approximately validation completed. Expected: {} (±{}), Actual: {}",
                expectedRate, tolerance, actualRate);
    }
    @Then("validate premium is approximately {double}")
    public void validate_premium_is_approximately(Double expectedPremium) {
        Double actualPremium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Assert.assertNotNull("Premium should not be null", actualPremium);

        // Allow for small differences in premium calculation
        double tolerance = 1.0; // Allow ±1 unit difference
        Assert.assertTrue("Premium should be approximately " + expectedPremium + " (±" + tolerance + "), but was " + actualPremium,
            Math.abs(actualPremium - expectedPremium) <= tolerance);

        log.info("Premium approximately validation completed. Expected: {} (±{}), Actual: {}",
            expectedPremium, tolerance, actualPremium);
    }

    @Then("validate premium is approximately in calculate premium call {double}")
    public void validate_premium_is_approximately_calculate_premium_call(Double expectedPremium) {
        Double actualPremium = CommonSteps.response.jsonPath().getDouble("premium");
        Assert.assertNotNull("Premium should not be null", actualPremium);

        // Allow for small differences in premium calculation
        double tolerance = 1.0; // Allow ±1 unit difference
        Assert.assertTrue("Premium should be approximately " + expectedPremium + " (±" + tolerance + "), but was " + actualPremium,
                Math.abs(actualPremium - expectedPremium) <= tolerance);

        log.info("Premium approximately validation completed. Expected: {} (±{}), Actual: {}",
                expectedPremium, tolerance, actualPremium);
    }

    @Then("validate rate and premium relationship")
    public void validate_rate_and_premium_relationship() {
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");
        String gfrNext12Months = CommonSteps.response.jsonPath().getString("data.gfrNext12Months");

        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertNotNull("GFR Next 12 Months should not be null", gfrNext12Months);

        // Basic business logic validation
        Assert.assertTrue("Rate should be between 0 and 1", rate >= 0 && rate <= 1);
        Assert.assertTrue("Premium should be positive", premium > 0);

        // If GFR is available, validate rate calculation logic
        try {
            Double gfr = Double.parseDouble(gfrNext12Months);
            Double calculatedPremium = gfr * rate;

            // Allow for some tolerance in calculation due to rounding and other factors
            double tolerance = premium * 0.1; // 10% tolerance
            Assert.assertTrue("Calculated premium should be within reasonable range of actual premium. " +
                "Calculated: " + calculatedPremium + ", Actual: " + premium + ", Tolerance: ±" + tolerance,
                Math.abs(calculatedPremium - premium) <= tolerance);

            log.info("Rate and premium relationship validated. Rate: {}, Premium: {}, GFR: {}, Calculated Premium: {}",
                rate, premium, gfr, calculatedPremium);
        } catch (NumberFormatException e) {
            log.warn("Could not parse GFR value for calculation validation: {}", gfrNext12Months);
        }
    }

    @Then("validate premium calculation components")
    public void validate_premium_calculation_components() {
        // Validate premium calculation breakdown
        Map<String, Object> premiumCalculation = CommonSteps.response.jsonPath().getMap("data.premiumCalculation");
        if (premiumCalculation != null) {
            Assert.assertTrue("Premium calculation should have shipperInterestCargo",
                premiumCalculation.containsKey("shipperInterestCargo"));
            log.info("Premium calculation components validated");
        }

        // Validate premium summary totals
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("data.premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);

        Object totalPremium = premiumSummary.get("totalPremium");
        Object brokerCommission = premiumSummary.get("brokerCommission");

        if (totalPremium != null && brokerCommission != null) {
            Double total = ((Number) totalPremium).doubleValue();
            Double commission = ((Number) brokerCommission).doubleValue();

            Assert.assertTrue("Total premium should be non-negative", total >= 0);
            Assert.assertTrue("Broker commission should be non-negative", commission >= 0);

            log.info("Premium calculation components validated. Total: {}, Commission: {}", total, commission);
        }
    }

    @Then("validate premium summary contains all required fields")
    public void validate_premium_summary_contains_all_required_fields() {
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("data.premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);

        // Validate all expected premium summary fields
        String[] requiredFields = {
            "totalPremium", "brokerCommission", "triaPremium",
            "shipperInterestPremiumSummary", "partnerNetPremium",
            "warehouseLegalLiabilityPremiumSummary", "cargoLiabilitiesPremiumSummary",
            "excessWarehouseOperatorLegalLiabilityPremiumSummary", "blueHavenUWCommission"
        };

        for (String field : requiredFields) {
            Assert.assertTrue("Premium summary should contain " + field,
                premiumSummary.containsKey(field));
        }

        log.info("Premium summary fields validation completed");
    }

    @Then("the geographical scope should contain worldwide coverage")
    public void geographical_scope_should_contain_worldwide_coverage() {
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);

        String statement = (String) geographicalScope.get("statement");
        Assert.assertNotNull("Geographical scope statement should not be null", statement);
        Assert.assertTrue("Statement should mention worldwide coverage",
            statement.toLowerCase().contains("world"));

        log.info("Geographical scope worldwide coverage validation completed");
    }

    @Then("the geographical scope should contain exclusions")
    public void geographical_scope_should_contain_exclusions() {
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);

        String statement2 = (String) geographicalScope.get("statement2");
        Assert.assertNotNull("Geographical scope exclusions should not be null", statement2);
        Assert.assertTrue("Statement2 should mention exclusions",
            statement2.toLowerCase().contains("excluding"));

        log.info("Geographical scope exclusions validation completed");
    }

    @Then("all conveyance types should be properly configured")
    public void all_conveyance_types_should_be_properly_configured() {
        Map<String, Object> conveyance = CommonSteps.response.jsonPath().getMap("data.conveyance");
        Assert.assertNotNull("Conveyance should not be null", conveyance);

        // Validate that all expected conveyance types are present
        String[] conveyanceTypes = {"ocean", "aircraft", "domesticTruckOrRail", "internationTruckOrRail"};

        for (String type : conveyanceTypes) {
            Assert.assertTrue("Conveyance should contain " + type, conveyance.containsKey(type));

            Object conveyanceType = conveyance.get(type);
            if (conveyanceType instanceof Map) {
                Map<String, Object> typeConfig = (Map<String, Object>) conveyanceType;
                Assert.assertTrue("Conveyance type " + type + " should have enabled flag",
                    typeConfig.containsKey("enabled"));
            }
        }

        log.info("All conveyance types configuration validation completed");
    }

    // Additional step definitions that might be needed
    @Then("the response should contain submission details")
    public void response_should_contain_submission_details() {
        Assert.assertNotNull("Submission ID should not be null",
            CommonSteps.response.jsonPath().get("data.submissionId"));
        Assert.assertNotNull("Insured info should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo"));
        Assert.assertNotNull("Business name should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.businessName"));
        log.info("Submission details validation completed");
    }

    @Then("the policy status should be {string}")
    public void policy_status_should_be(String expectedStatus) {
        String actualStatus = CommonSteps.response.jsonPath().getString("data.status");
        Assert.assertNotNull("Status should not be null", actualStatus);
        Assert.assertEquals("Status should match expected value", expectedStatus, actualStatus);
        log.info("Policy status validation completed: {}", actualStatus);
    }

    @Then("validate policy submission fields")
    public void validate_policy_submission_fields() {
        // Validate core submission fields
        Assert.assertNotNull("Submission ID should not be null",
            CommonSteps.response.jsonPath().get("data.submissionId"));
        Assert.assertNotNull("Client ID should not be null",
            CommonSteps.response.jsonPath().get("data.clientId"));
        Assert.assertNotNull("Created at should not be null",
            CommonSteps.response.jsonPath().get("data.createdAt"));
        Assert.assertNotNull("Updated at should not be null",
            CommonSteps.response.jsonPath().get("data.updatedAt"));

        log.info("Policy submission fields validation completed");
    }

    @Then("validate receipts and shipper interest fields")
    public void validate_receipts_and_shipper_interest_fields() {
        // Validate receipts fields
        Assert.assertNotNull("Receipts as broker agent amount should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsBrokerAgentAmount"));
        Assert.assertNotNull("Receipts as broker agent percentage should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsBrokerAgentPercentage"));
        Assert.assertNotNull("Receipts as indirect air carrier amount should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsIndirectAirCarrierAmount"));
        Assert.assertNotNull("Receipts as indirect air carrier percentage should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsIndirectAirCarrierPercentage"));
        Assert.assertNotNull("Receipts as motor truck carrier amount should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsMotorTruckCarrierAmount"));
        Assert.assertNotNull("Receipts as motor truck carrier percentage should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsMotorTruckCarrierPercentage"));
        Assert.assertNotNull("Receipts as NVOCC amount should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsNVOCCAmount"));
        Assert.assertNotNull("Receipts as NVOCC percentage should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsNVOCCPercentage"));
        Assert.assertNotNull("Receipts as warehouse operator amount should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsWarehouseOperatorAmount"));
        Assert.assertNotNull("Receipts as warehouse operator percentage should not be null",
            CommonSteps.response.jsonPath().get("data.receiptsAsWarehouseOperatorPercentage"));

        // Validate shipper interest fields
        Assert.assertNotNull("Shipper interest adjustment period should not be null",
            CommonSteps.response.jsonPath().get("data.shipperInterestAdjustmentPeriod"));
        Assert.assertNotNull("Shipper interest adjustment rate should not be null",
            CommonSteps.response.jsonPath().get("data.shipperInterestAdjustmentRate"));
        Assert.assertNotNull("Shipper interest cargo inland deposit should not be null",
            CommonSteps.response.jsonPath().get("data.shipperInterestCargoInlandDeposit"));

        log.info("Receipts and shipper interest fields validation completed");
    }

    @Then("validate specific receipts values")
    public void validate_specific_receipts_values() {
        // Validate specific receipts amounts
        String brokerAgentAmount = CommonSteps.response.jsonPath().getString("data.receiptsAsBrokerAgentAmount");
        String brokerAgentPercentage = CommonSteps.response.jsonPath().getString("data.receiptsAsBrokerAgentPercentage");

        Assert.assertEquals("Broker agent amount should be 3000000", "3000000", brokerAgentAmount);
        Assert.assertEquals("Broker agent percentage should be 12", "12", brokerAgentPercentage);

        // Validate that all receipts amounts are the same
        String[] receiptAmountFields = {
            "data.receiptsAsBrokerAgentAmount",
            "data.receiptsAsIndirectAirCarrierAmount",
            "data.receiptsAsMotorTruckCarrierAmount",
            "data.receiptsAsNVOCCAmount",
            "data.receiptsAsWarehouseOperatorAmount"
        };

        for (String field : receiptAmountFields) {
            String amount = CommonSteps.response.jsonPath().getString(field);
            Assert.assertEquals("All receipt amounts should be 3000000", "3000000", amount);
        }

        // Validate that all receipts percentages are the same
        String[] receiptPercentageFields = {
            "data.receiptsAsBrokerAgentPercentage",
            "data.receiptsAsIndirectAirCarrierPercentage",
            "data.receiptsAsMotorTruckCarrierPercentage",
            "data.receiptsAsNVOCCPercentage",
            "data.receiptsAsWarehouseOperatorPercentage"
        };

        for (String field : receiptPercentageFields) {
            String percentage = CommonSteps.response.jsonPath().getString(field);
            Assert.assertEquals("All receipt percentages should be 12", "12", percentage);
        }

        log.info("Specific receipts values validation completed");
    }

    @Then("validate shipper interest specific values")
    public void validate_shipper_interest_specific_values() {
        String adjustmentPeriod = CommonSteps.response.jsonPath().getString("data.shipperInterestAdjustmentPeriod");
        String adjustmentRate = CommonSteps.response.jsonPath().getString("data.shipperInterestAdjustmentRate");
        String cargoInlandDeposit = CommonSteps.response.jsonPath().getString("data.shipperInterestCargoInlandDeposit");

        Assert.assertEquals("Adjustment period should be Monthly", "Monthly", adjustmentPeriod);
        Assert.assertEquals("Adjustment rate should be As per Rate Matrix", "As per Rate Matrix", adjustmentRate);
        Assert.assertEquals("Cargo inland deposit should be $0", "$0", cargoInlandDeposit);

        log.info("Shipper interest specific values validation completed");
    }
}
