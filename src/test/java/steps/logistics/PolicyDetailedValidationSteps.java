package steps.logistics;

import io.cucumber.java.en.Then;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import steps.CommonSteps;
import steps.StepsTemplate;

import java.util.List;
import java.util.Map;

public class PolicyDetailedValidationSteps extends StepsTemplate {

    private static final Logger log = LogManager.getLogger(PolicyDetailedValidationSteps.class);

    @Then("validate detailed policy response structure")
    public void validate_detailed_policy_response_structure() {
        // Validate top-level response structure
        Assert.assertNotNull("Response data should not be null",
            CommonSteps.response.jsonPath().get("data"));
        Assert.assertNotNull("Success flag should not be null",
            CommonSteps.response.jsonPath().get("success"));
        Assert.assertNotNull("Message should not be null",
            CommonSteps.response.jsonPath().get("message"));
        Assert.assertNotNull("Timestamp should not be null",
            CommonSteps.response.jsonPath().get("timestamp"));

        Boolean success = CommonSteps.response.jsonPath().getBoolean("success");
        Assert.assertTrue("Success should be true", success);

        String message = CommonSteps.response.jsonPath().getString("message");
        Assert.assertEquals("Message should indicate success", "Request Processed Successfully", message);

        log.info("Detailed policy response structure validated successfully");
    }

    @Then("validate policy core information")
    public void validate_policy_core_information() {
        // Validate core policy fields
        Assert.assertNotNull("Submission ID should not be null",
            CommonSteps.response.jsonPath().get("data.submissionId"));
        Assert.assertNotNull("Status should not be null",
            CommonSteps.response.jsonPath().get("data.status"));
        Assert.assertNotNull("Class should not be null",
            CommonSteps.response.jsonPath().get("data.class"));
        Assert.assertNotNull("LOB should not be null",
            CommonSteps.response.jsonPath().get("data.lob"));
        Assert.assertNotNull("Client ID should not be null",
            CommonSteps.response.jsonPath().get("data.clientId"));
        Assert.assertNotNull("Business type should not be null",
            CommonSteps.response.jsonPath().get("data.businessType"));

        // Validate proposal information
        Assert.assertNotNull("Proposal number should not be null",
            CommonSteps.response.jsonPath().get("data.proposalNumber"));
        Assert.assertNotNull("Proposal date should not be null",
            CommonSteps.response.jsonPath().get("data.proposalDate"));
        Assert.assertNotNull("Underwriter name should not be null",
            CommonSteps.response.jsonPath().get("data.underwriterName"));

        log.info("Policy core information validated successfully");
    }

    @Then("validate insured information details")
    public void validate_insured_information_details() {
        // Validate insured info structure
        Assert.assertNotNull("Insured info should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo"));

        // Validate basic insured fields
        Assert.assertNotNull("Business name should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.businessName"));
        Assert.assertNotNull("Insured ID should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.insuredId"));
        Assert.assertNotNull("Insured client ID should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.insuredClientId"));
        Assert.assertNotNull("State should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.state"));

        // Validate address information
        Assert.assertNotNull("City should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.city"));
        Assert.assertNotNull("Zipcode should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.zipcode"));

        // Validate dates
        Assert.assertNotNull("Effective date should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.effectiveDate"));
        Assert.assertNotNull("Expiry date should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.expiryDate"));

        // Validate company information
        Assert.assertNotNull("Company name should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.companyName"));
        Assert.assertNotNull("Company address should not be null",
            CommonSteps.response.jsonPath().get("data.insuredInfo.companyAddress"));

        log.info("Insured information details validated successfully");
    }

    @Then("validate premium and rating information")
    public void validate_premium_and_rating_information() {
        // Validate premium fields
        Assert.assertNotNull("Premium should not be null",
            CommonSteps.response.jsonPath().get("data.premium"));
        Assert.assertNotNull("Rate should not be null",
            CommonSteps.response.jsonPath().get("data.rate"));

        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");

        Assert.assertTrue("Premium should be non-negative", premium >= 0);
        Assert.assertTrue("Rate should be non-negative", rate >= 0);

        // Validate premium summary
        Assert.assertNotNull("Premium summary should not be null",
            CommonSteps.response.jsonPath().get("data.premiumSummary"));

        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("data.premiumSummary");
        Assert.assertTrue("Premium summary should contain totalPremium",
            premiumSummary.containsKey("totalPremium"));
        Assert.assertTrue("Premium summary should contain brokerCommission",
            premiumSummary.containsKey("brokerCommission"));
        Assert.assertTrue("Premium summary should contain triaPremium",
            premiumSummary.containsKey("triaPremium"));

        log.info("Premium and rating information validated successfully");
    }

    @Then("validate commodity types information")
    public void validate_commodity_types_information() {
        List<Map<String, Object>> commodityTypes = CommonSteps.response.jsonPath().getList("data.commodityTypes");
        Assert.assertNotNull("Commodity types should not be null", commodityTypes);
        Assert.assertFalse("Commodity types should not be empty", commodityTypes.isEmpty());

        // Validate each commodity type has required fields
        for (Map<String, Object> commodity : commodityTypes) {
            Assert.assertTrue("Commodity should have label", commodity.containsKey("label"));
            Assert.assertTrue("Commodity should have key", commodity.containsKey("key"));
            Assert.assertTrue("Commodity should have percentage", commodity.containsKey("percentage"));

            String label = (String) commodity.get("label");
            String key = (String) commodity.get("key");
            Assert.assertNotNull("Commodity label should not be null", label);
            Assert.assertNotNull("Commodity key should not be null", key);
        }

        log.info("Commodity types information validated successfully. Found {} commodity types", commodityTypes.size());
    }

    @Then("validate geographical information")
    public void validate_geographical_information() {
        // Validate geographical areas
        List<Map<String, Object>> geographicalAreas = CommonSteps.response.jsonPath().getList("data.geographicalArea");
        Assert.assertNotNull("Geographical areas should not be null", geographicalAreas);
        Assert.assertFalse("Geographical areas should not be empty", geographicalAreas.isEmpty());

        for (Map<String, Object> area : geographicalAreas) {
            Assert.assertTrue("Area should have label", area.containsKey("label"));
            Assert.assertTrue("Area should have key", area.containsKey("key"));
        }

        // Validate geographical scope
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);
        Assert.assertTrue("Geographical scope should have title", geographicalScope.containsKey("title"));
        Assert.assertTrue("Geographical scope should have statement", geographicalScope.containsKey("statement"));

        log.info("Geographical information validated successfully");
    }

    @Then("validate conveyance information")
    public void validate_conveyance_information() {
        Map<String, Object> conveyance = CommonSteps.response.jsonPath().getMap("data.conveyance");
        Assert.assertNotNull("Conveyance should not be null", conveyance);

        // Validate conveyance types
        Assert.assertTrue("Conveyance should have ocean", conveyance.containsKey("ocean"));
        Assert.assertTrue("Conveyance should have aircraft", conveyance.containsKey("aircraft"));
        Assert.assertTrue("Conveyance should have domesticTruckOrRail", conveyance.containsKey("domesticTruckOrRail"));
        Assert.assertTrue("Conveyance should have internationTruckOrRail", conveyance.containsKey("internationTruckOrRail"));

        // Validate ocean conveyance structure
        Map<String, Object> ocean = (Map<String, Object>) conveyance.get("ocean");
        if (ocean != null) {
            Assert.assertTrue("Ocean should have enabled flag", ocean.containsKey("enabled"));
        }

        log.info("Conveyance information validated successfully");
    }

    @Then("validate terms and conditions structure")
    public void validate_terms_and_conditions_structure() {
        Map<String, Object> termsAndConditions = CommonSteps.response.jsonPath().getMap("data.termsAndConditions");
        Assert.assertNotNull("Terms and conditions should not be null", termsAndConditions);

        // Validate different class types have terms
        if (termsAndConditions.containsKey("SI")) {
            List<Map<String, Object>> siTerms = (List<Map<String, Object>>) termsAndConditions.get("SI");
            Assert.assertNotNull("SI terms should not be null", siTerms);

            for (Map<String, Object> term : siTerms) {
                Assert.assertTrue("Term should have value", term.containsKey("value"));
                Assert.assertTrue("Term should have id", term.containsKey("id"));
            }
        }

        if (termsAndConditions.containsKey("CL")) {
            List<Map<String, Object>> clTerms = (List<Map<String, Object>>) termsAndConditions.get("CL");
            Assert.assertNotNull("CL terms should not be null", clTerms);
        }

        if (termsAndConditions.containsKey("general")) {
            List<Map<String, Object>> generalTerms = (List<Map<String, Object>>) termsAndConditions.get("general");
            Assert.assertNotNull("General terms should not be null", generalTerms);
        }

        log.info("Terms and conditions structure validated successfully");
    }

    @Then("validate additional insured information")
    public void validate_additional_insured_information() {
        List<Map<String, Object>> additionalInsured = CommonSteps.response.jsonPath().getList("data.additionalInsuredInfo");
        Assert.assertNotNull("Additional insured info should not be null", additionalInsured);

        if (!additionalInsured.isEmpty()) {
            for (Map<String, Object> insured : additionalInsured) {
                Assert.assertTrue("Additional insured should have id", insured.containsKey("id"));
                Assert.assertTrue("Additional insured should have name", insured.containsKey("name"));
                Assert.assertTrue("Additional insured should have address", insured.containsKey("address"));
                Assert.assertTrue("Additional insured should have city", insured.containsKey("city"));
                Assert.assertTrue("Additional insured should have state", insured.containsKey("state"));
                Assert.assertTrue("Additional insured should have zipcode", insured.containsKey("zipcode"));
            }
        }

        log.info("Additional insured information validated successfully");
    }

    @Then("validate broker information")
    public void validate_broker_information() {
        Map<String, Object> brokerInfo = CommonSteps.response.jsonPath().getMap("data.brokerInfo");
        Assert.assertNotNull("Broker info should not be null", brokerInfo);

        Assert.assertTrue("Broker info should have brokerOfRecord", brokerInfo.containsKey("brokerOfRecord"));
        Assert.assertTrue("Broker info should have brokerContact", brokerInfo.containsKey("brokerContact"));

        log.info("Broker information validated successfully");
    }

    @Then("validate loss history information")
    public void validate_loss_history_information() {
        Map<String, Object> lossHistory = CommonSteps.response.jsonPath().getMap("data.lossHistory");
        Assert.assertNotNull("Loss history should not be null", lossHistory);

        Assert.assertTrue("Loss history should have totalRaterPremium", lossHistory.containsKey("totalRaterPremium"));
        Assert.assertTrue("Loss history should have raterPremiumLossPick", lossHistory.containsKey("raterPremiumLossPick"));
        Assert.assertTrue("Loss history should have lossAdjustedPremiumLossPick", lossHistory.containsKey("lossAdjustedPremiumLossPick"));

        // Validate nested loss details
        Assert.assertTrue("Loss history should have shipperInterestCargo", lossHistory.containsKey("shipperInterestCargo"));
        Assert.assertTrue("Loss history should have cargoLiabilities", lossHistory.containsKey("cargoLiabilities"));
        Assert.assertTrue("Loss history should have warehouseLegalLiability", lossHistory.containsKey("warehouseLegalLiability"));

        log.info("Loss history information validated successfully");
    }

    @Then("validate valuation information")
    public void validate_valuation_information() {
        Map<String, Object> valuation = CommonSteps.response.jsonPath().getMap("data.valuation");
        Assert.assertNotNull("Valuation should not be null", valuation);

        Assert.assertTrue("Valuation should have title", valuation.containsKey("title"));
        Assert.assertTrue("Valuation should have statement", valuation.containsKey("statement"));
        Assert.assertTrue("Valuation should have statement2", valuation.containsKey("statement2"));
        Assert.assertTrue("Valuation should have statement3", valuation.containsKey("statement3"));

        String title = (String) valuation.get("title");
        Assert.assertEquals("Valuation title should be correct", "3. Valuation", title);

        log.info("Valuation information validated successfully");
    }

    @Then("the response should contain LOB {string}")
    public void response_should_contain_lob(String expectedLob) {
        List<String> lobList = CommonSteps.response.jsonPath().getList("data.lob");
        Assert.assertNotNull("LOB list should not be null", lobList);
        Assert.assertTrue("LOB list should contain " + expectedLob, lobList.contains(expectedLob));
        log.info("Validated response contains LOB: {}", expectedLob);
    }

    @Then("the response should contain class {string}")
    public void response_should_contain_class(String expectedClass) {
        List<String> classList = CommonSteps.response.jsonPath().getList("data.class");
        Assert.assertNotNull("Class list should not be null", classList);
        Assert.assertTrue("Class list should contain " + expectedClass, classList.contains(expectedClass));
        log.info("Validated response contains class: {}", expectedClass);
    }

    @Then("the response should contain creation timestamp")
    public void response_should_contain_creation_timestamp() {
        Assert.assertNotNull("Created at should not be null",
            CommonSteps.response.jsonPath().get("data.createdAt"));
        Assert.assertNotNull("Data ID timestamp should not be null",
            CommonSteps.response.jsonPath().get("data._id.timestamp"));
        Assert.assertNotNull("Data ID date should not be null",
            CommonSteps.response.jsonPath().get("data._id.date"));
        log.info("Creation timestamp validation completed");
    }

    @Then("the response should contain update timestamp")
    public void response_should_contain_update_timestamp() {
        Assert.assertNotNull("Updated at should not be null",
            CommonSteps.response.jsonPath().get("data.updatedAt"));
        log.info("Update timestamp validation completed");
    }

    @Then("the response should contain created by information")
    public void response_should_contain_created_by_information() {
        Assert.assertNotNull("Created by should not be null",
            CommonSteps.response.jsonPath().get("data.createdBy"));
        String createdBy = CommonSteps.response.jsonPath().getString("data.createdBy");
        Assert.assertFalse("Created by should not be empty", createdBy.isEmpty());
        log.info("Created by information validation completed");
    }

    @Then("the response should contain updated by information")
    public void response_should_contain_updated_by_information() {
        Assert.assertNotNull("Updated by should not be null",
            CommonSteps.response.jsonPath().get("data.updatedBy"));
        String updatedBy = CommonSteps.response.jsonPath().getString("data.updatedBy");
        Assert.assertFalse("Updated by should not be empty", updatedBy.isEmpty());
        log.info("Updated by information validation completed");
    }

    @Then("the response should contain business type {string}")
    public void response_should_contain_business_type(String expectedBusinessType) {
        String businessType = CommonSteps.response.jsonPath().getString("data.businessType");
        Assert.assertNotNull("Business type should not be null", businessType);
        Assert.assertEquals("Business type should match", expectedBusinessType, businessType);
        log.info("Business type validation completed: {}", businessType);
    }

    @Then("the response should contain valid proposal number")
    public void response_should_contain_valid_proposal_number() {
        String proposalNumber = CommonSteps.response.jsonPath().getString("data.proposalNumber");
        Assert.assertNotNull("Proposal number should not be null", proposalNumber);
        Assert.assertFalse("Proposal number should not be empty", proposalNumber.isEmpty());
        Assert.assertTrue("Proposal number should follow expected format",
            proposalNumber.matches("\\d{3}_L\\d+"));
        log.info("Proposal number validation completed: {}", proposalNumber);
    }

    @Then("the response should contain underwriter name")
    public void response_should_contain_underwriter_name() {
        String underwriterName = CommonSteps.response.jsonPath().getString("data.underwriterName");
        Assert.assertNotNull("Underwriter name should not be null", underwriterName);
        Assert.assertFalse("Underwriter name should not be empty", underwriterName.isEmpty());
        log.info("Underwriter name validation completed: {}", underwriterName);
    }

    @Then("the premium should be greater than zero")
    public void premium_should_be_greater_than_zero() {
        Double premium = CommonSteps.response.jsonPath().getDouble("data.premium");
        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertTrue("Premium should be greater than zero", premium > 0);
        log.info("Premium validation completed: {}", premium);
    }

    @Then("the rate should be greater than zero")
    public void rate_should_be_greater_than_zero() {
        Double rate = CommonSteps.response.jsonPath().getDouble("data.rate");
        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertTrue("Rate should be greater than zero", rate > 0);
        log.info("Rate validation completed: {}", rate);
    }

    @Then("validate premium summary contains all required fields")
    public void validate_premium_summary_contains_all_required_fields() {
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("data.premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);

        // Validate all expected premium summary fields
        String[] requiredFields = {
            "totalPremium", "brokerCommission", "triaPremium",
            "shipperInterestPremiumSummary", "partnerNetPremium",
            "warehouseLegalLiabilityPremiumSummary", "cargoLiabilitiesPremiumSummary",
            "excessWarehouseOperatorLegalLiabilityPremiumSummary", "blueHavenUWCommission"
        };

        for (String field : requiredFields) {
            Assert.assertTrue("Premium summary should contain " + field,
                premiumSummary.containsKey(field));
        }

        log.info("Premium summary fields validation completed");
    }

    @Then("the geographical scope should contain worldwide coverage")
    public void geographical_scope_should_contain_worldwide_coverage() {
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);

        String statement = (String) geographicalScope.get("statement");
        Assert.assertNotNull("Geographical scope statement should not be null", statement);
        Assert.assertTrue("Statement should mention worldwide coverage",
            statement.toLowerCase().contains("world"));

        log.info("Geographical scope worldwide coverage validation completed");
    }

    @Then("the geographical scope should contain exclusions")
    public void geographical_scope_should_contain_exclusions() {
        Map<String, Object> geographicalScope = CommonSteps.response.jsonPath().getMap("data.geographicalScope");
        Assert.assertNotNull("Geographical scope should not be null", geographicalScope);

        String statement2 = (String) geographicalScope.get("statement2");
        Assert.assertNotNull("Geographical scope exclusions should not be null", statement2);
        Assert.assertTrue("Statement2 should mention exclusions",
            statement2.toLowerCase().contains("excluding"));

        log.info("Geographical scope exclusions validation completed");
    }

    @Then("all conveyance types should be properly configured")
    public void all_conveyance_types_should_be_properly_configured() {
        Map<String, Object> conveyance = CommonSteps.response.jsonPath().getMap("data.conveyance");
        Assert.assertNotNull("Conveyance should not be null", conveyance);

        // Validate that all expected conveyance types are present
        String[] conveyanceTypes = {"ocean", "aircraft", "domesticTruckOrRail", "internationTruckOrRail"};

        for (String type : conveyanceTypes) {
            Assert.assertTrue("Conveyance should contain " + type, conveyance.containsKey(type));

            Object conveyanceType = conveyance.get(type);
            if (conveyanceType instanceof Map) {
                Map<String, Object> typeConfig = (Map<String, Object>) conveyanceType;
                Assert.assertTrue("Conveyance type " + type + " should have enabled flag",
                    typeConfig.containsKey("enabled"));
            }
        }

        log.info("All conveyance types configuration validation completed");
    }
}
