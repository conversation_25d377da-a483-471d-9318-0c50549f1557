package steps.logistics;

import helpers.HttpHelper;
import helpers.PolicySearchHelper;
import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import pojo.logistics.PolicySearchRequest;
import steps.CommonSteps;
import steps.StepsTemplate;

import java.util.List;
import java.util.Map;

public class PolicySearchSteps extends StepsTemplate {
    
    private static final Logger log = LogManager.getLogger(PolicySearchSteps.class);
    private String requestPayload;
    private String invalidRequestPayload;
    private Map<String, String> queryParams;
    private final String POLICY_SEARCH_ENDPOINT = "/core/policy-service/submissions/filter";
    
    @Given("I have valid policy search filter criteria")
    public void i_have_valid_policy_search_filter_criteria() {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Valid policy search filter criteria prepared");
    }
    
    @Given("I have policy search filter with product codes {string}")
    public void i_have_policy_search_filter_with_product_codes(String productCodes) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithProductCodes(productCodes);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with product codes: {}", productCodes);
    }
    
    @Given("I have policy search filter with status {string}")
    public void i_have_policy_search_filter_with_status(String status) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithStatus(status);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with status: {}", status);
    }
    
    @Given("I have policy search filter with page number {int} and page size {int}")
    public void i_have_policy_search_filter_with_pagination(int pageNumber, int pageSize) {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(pageNumber, pageSize);
        log.info("Policy search filter prepared with pagination: page={}, size={}", pageNumber, pageSize);
    }
    
    @Given("I have policy search filter with free text {string}")
    public void i_have_policy_search_filter_with_free_text(String freeText) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithFreeText(freeText);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with free text: {}", freeText);
    }
    
    @Given("I have policy search filter with underwriters {string}")
    public void i_have_policy_search_filter_with_underwriters(String underwriters) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithUnderwriters(underwriters);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with underwriters: {}", underwriters);
    }
    
    @Given("I have empty policy search filter criteria")
    public void i_have_empty_policy_search_filter_criteria() {
        PolicySearchRequest request = PolicySearchHelper.createEmptySearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Empty policy search filter criteria prepared");
    }
    
    @Given("I have policy search filter with invalid pagination parameters")
    public void i_have_policy_search_filter_with_invalid_pagination() {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createInvalidPaginationParams();
        log.info("Policy search filter prepared with invalid pagination parameters");
    }
    
    @Given("I have policy search filter with multiple criteria:")
    public void i_have_policy_search_filter_with_multiple_criteria(DataTable dataTable) {
        Map<String, String> criteria = dataTable.asMap(String.class, String.class);
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithMultipleCriteria(
            criteria.get("freeText"),
            criteria.get("productCode"),
            criteria.get("status"),
            criteria.get("underwriters")
        );
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with multiple criteria: {}", criteria);
    }
    
    @Given("I have policy search filter with free text containing special characters")
    public void i_have_policy_search_filter_with_special_characters() {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithSpecialCharacters();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with special characters in free text");
    }
    
    @When("I send POST request to search policies with pagination")
    public void i_send_post_request_to_search_policies() throws Throwable {
        String endpointWithParams = POLICY_SEARCH_ENDPOINT + "?pageNumber=" + queryParams.get("pageNumber") + 
                                   "&pageSize=" + queryParams.get("pageSize");
        CommonSteps.response = httpHelper.requestPost(endpointWithParams, requestPayload);
        log.info("POST request sent to search policies endpoint with pagination");
    }
    
    @When("I send POST request to search policies without authorization")
    public void i_send_post_request_without_authorization() throws Throwable {
        HttpHelper unauthorizedHelper = new HttpHelper(false);
        String endpointWithParams = POLICY_SEARCH_ENDPOINT + "?pageNumber=" + queryParams.get("pageNumber") + 
                                   "&pageSize=" + queryParams.get("pageSize");
        CommonSteps.response = unauthorizedHelper.requestPost(endpointWithParams, requestPayload);
        log.info("POST request sent to search policies endpoint without authorization");
    }
    
    @Then("the response should contain policy search results")
    public void response_should_contain_policy_search_results() {
        // Validate that response contains expected policy search structure
        Assert.assertNotNull("Response data should not be null", 
            CommonSteps.response.jsonPath().get("data"));
        
        List<Object> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);
        
        log.info("Policy search results validated successfully. Found {} policies", policies.size());
    }
    
    @Then("validate policy search response structure")
    public void validate_policy_search_response_structure() {
        // Validate pagination fields
        Assert.assertNotNull("Total elements should not be null", 
            CommonSteps.response.jsonPath().get("totalElements"));
        Assert.assertNotNull("Total pages should not be null", 
            CommonSteps.response.jsonPath().get("totalPages"));
        Assert.assertNotNull("Page number should not be null", 
            CommonSteps.response.jsonPath().get("pageNumber"));
        Assert.assertNotNull("Page size should not be null", 
            CommonSteps.response.jsonPath().get("pageSize"));
        
        log.info("Policy search response structure validated successfully");
    }
    
    @Then("the response should contain policies with product codes {string}")
    public void response_should_contain_policies_with_product_codes(String expectedProductCodes) {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);
        Assert.assertFalse("Policies list should not be empty", policies.isEmpty());
        
        // Validate that at least one policy contains the expected product codes
        boolean foundMatchingPolicy = false;
        for (Map<String, Object> policy : policies) {
            List<String> productCodes = (List<String>) policy.get("productCode");
            if (productCodes != null && PolicySearchHelper.containsProductCodes(productCodes, expectedProductCodes)) {
                foundMatchingPolicy = true;
                break;
            }
        }
        
        Assert.assertTrue("Should find at least one policy with product codes: " + expectedProductCodes, 
            foundMatchingPolicy);
        log.info("Validated policies contain expected product codes: {}", expectedProductCodes);
    }
    
    @Then("the response should contain policies with status {string}")
    public void response_should_contain_policies_with_status(String expectedStatus) {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);
        
        if (!policies.isEmpty()) {
            // Validate that policies have the expected status
            String[] expectedStatuses = expectedStatus.split(",");
            for (Map<String, Object> policy : policies) {
                String policyStatus = (String) policy.get("status");
                boolean statusMatches = false;
                for (String status : expectedStatuses) {
                    if (status.trim().equals(policyStatus)) {
                        statusMatches = true;
                        break;
                    }
                }
                Assert.assertTrue("Policy status should match expected status. Found: " + policyStatus + 
                    ", Expected one of: " + expectedStatus, statusMatches);
            }
        }
        
        log.info("Validated policies contain expected status: {}", expectedStatus);
    }
