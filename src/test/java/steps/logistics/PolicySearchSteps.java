package steps.logistics;

import helpers.HttpHelper;
import helpers.PolicySearchHelper;
import io.cucumber.datatable.DataTable;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import pojo.logistics.PolicySearchRequest;
import steps.CommonSteps;
import steps.StepsTemplate;

import java.util.List;
import java.util.Map;

public class PolicySearchSteps extends StepsTemplate {

    private static final Logger log = LogManager.getLogger(PolicySearchSteps.class);
    private String requestPayload;
    private String invalidRequestPayload;
    private Map<String, String> queryParams;
    private final String POLICY_SEARCH_ENDPOINT = "/core/policy-service/submissions/filter";

    @Given("I have valid policy search filter criteria")
    public void i_have_valid_policy_search_filter_criteria() {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Valid policy search filter criteria prepared");
    }

    @Given("I have policy search filter with product codes {string}")
    public void i_have_policy_search_filter_with_product_codes(String productCodes) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithProductCodes(productCodes);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with product codes: {}", productCodes);
    }

    @Given("I have policy search filter with status {string}")
    public void i_have_policy_search_filter_with_status(String status) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithStatus(status);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with status: {}", status);
    }

    @Given("I have policy search filter with page number {int} and page size {int}")
    public void i_have_policy_search_filter_with_pagination(int pageNumber, int pageSize) {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(pageNumber, pageSize);
        log.info("Policy search filter prepared with pagination: page={}, size={}", pageNumber, pageSize);
    }

    @Given("I have policy search filter with free text {string}")
    public void i_have_policy_search_filter_with_free_text(String freeText) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithFreeText(freeText);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with free text: {}", freeText);
    }

    @Given("I have policy search filter with underwriters {string}")
    public void i_have_policy_search_filter_with_underwriters(String underwriters) {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithUnderwriters(underwriters);
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with underwriters: {}", underwriters);
    }

    @Given("I have empty policy search filter criteria")
    public void i_have_empty_policy_search_filter_criteria() {
        PolicySearchRequest request = PolicySearchHelper.createEmptySearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Empty policy search filter criteria prepared");
    }

    @Given("I have policy search filter with invalid pagination parameters")
    public void i_have_policy_search_filter_with_invalid_pagination() {
        PolicySearchRequest request = PolicySearchHelper.createValidSearchRequest();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createInvalidPaginationParams();
        log.info("Policy search filter prepared with invalid pagination parameters");
    }

    @Given("I have policy search filter with multiple criteria:")
    public void i_have_policy_search_filter_with_multiple_criteria(DataTable dataTable) {
        Map<String, String> criteria = dataTable.asMap(String.class, String.class);
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithMultipleCriteria(
            criteria.get("freeText"),
            criteria.get("productCode"),
            criteria.get("status"),
            criteria.get("underwriters")
        );
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with multiple criteria: {}", criteria);
    }

    @Given("I have policy search filter with free text containing special characters")
    public void i_have_policy_search_filter_with_special_characters() {
        PolicySearchRequest request = PolicySearchHelper.createSearchRequestWithSpecialCharacters();
        requestPayload = PolicySearchHelper.toJsonString(request);
        queryParams = PolicySearchHelper.createPaginationParams(0, 30);
        log.info("Policy search filter prepared with special characters in free text");
    }

    @When("I send POST request to search policies with pagination")
    public void i_send_post_request_to_search_policies() throws Throwable {
        String endpointWithParams = POLICY_SEARCH_ENDPOINT + "?pageNumber=" + queryParams.get("pageNumber") +
                                   "&pageSize=" + queryParams.get("pageSize");
        CommonSteps.response = httpHelper.requestPost(endpointWithParams, requestPayload);
        log.info("POST request sent to search policies endpoint with pagination");
    }

    @When("I send POST request to search policies without authorization")
    public void i_send_post_request_without_authorization() throws Throwable {
        HttpHelper unauthorizedHelper = new HttpHelper(false);
        String endpointWithParams = POLICY_SEARCH_ENDPOINT + "?pageNumber=" + queryParams.get("pageNumber") +
                                   "&pageSize=" + queryParams.get("pageSize");
        CommonSteps.response = unauthorizedHelper.requestPost(endpointWithParams, requestPayload);
        log.info("POST request sent to search policies endpoint without authorization");
    }

    @Then("the response should contain policy search results")
    public void response_should_contain_policy_search_results() {
        // Validate that response contains expected policy search structure
        Assert.assertNotNull("Response data should not be null",
            CommonSteps.response.jsonPath().get("data"));

        List<Object> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        log.info("Policy search results validated successfully. Found {} policies", policies.size());
    }

    @Then("validate policy search response structure")
    public void validate_policy_search_response_structure() {
        // Validate pagination fields
        Assert.assertNotNull("Total elements should not be null",
            CommonSteps.response.jsonPath().get("totalElements"));
        Assert.assertNotNull("Total pages should not be null",
            CommonSteps.response.jsonPath().get("totalPages"));
        Assert.assertNotNull("Page number should not be null",
            CommonSteps.response.jsonPath().get("pageNumber"));
        Assert.assertNotNull("Page size should not be null",
            CommonSteps.response.jsonPath().get("pageSize"));

        log.info("Policy search response structure validated successfully");
    }

    @Then("the response should contain policies with product codes {string}")
    public void response_should_contain_policies_with_product_codes(String expectedProductCodes) {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);
        Assert.assertFalse("Policies list should not be empty", policies.isEmpty());

        // Validate that at least one policy contains the expected product codes
        boolean foundMatchingPolicy = false;
        for (Map<String, Object> policy : policies) {
            List<String> productCodes = (List<String>) policy.get("productCode");
            if (productCodes != null && PolicySearchHelper.containsProductCodes(productCodes, expectedProductCodes)) {
                foundMatchingPolicy = true;
                break;
            }
        }

        Assert.assertTrue("Should find at least one policy with product codes: " + expectedProductCodes,
            foundMatchingPolicy);
        log.info("Validated policies contain expected product codes: {}", expectedProductCodes);
    }

    @Then("the response should contain policies with status {string}")
    public void response_should_contain_policies_with_status(String expectedStatus) {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        if (!policies.isEmpty()) {
            // Validate that policies have the expected status
            String[] expectedStatuses = expectedStatus.split(",");
            for (Map<String, Object> policy : policies) {
                String policyStatus = (String) policy.get("status");
                boolean statusMatches = false;
                for (String status : expectedStatuses) {
                    if (status.trim().equals(policyStatus)) {
                        statusMatches = true;
                        break;
                    }
                }
                Assert.assertTrue("Policy status should match expected status. Found: " + policyStatus +
                    ", Expected one of: " + expectedStatus, statusMatches);
            }
        }

        log.info("Validated policies contain expected status: {}", expectedStatus);
    }

    @Then("the response should respect pagination with page number {int} and page size {int}")
    public void response_should_respect_pagination(int expectedPageNumber, int expectedPageSize) {
        Integer actualPageNumber = CommonSteps.response.jsonPath().getInt("pageNumber");
        Integer actualPageSize = CommonSteps.response.jsonPath().getInt("pageSize");
        Integer numberOfElements = CommonSteps.response.jsonPath().getInt("numberOfElements");

        Assert.assertEquals("Page number should match", expectedPageNumber, actualPageNumber.intValue());
        Assert.assertEquals("Page size should match", expectedPageSize, actualPageSize.intValue());
        Assert.assertTrue("Number of elements should not exceed page size",
            numberOfElements <= expectedPageSize);

        log.info("Pagination validated: page={}, size={}, elements={}",
            actualPageNumber, actualPageSize, numberOfElements);
    }

    @Then("the response should contain policies matching free text search")
    public void response_should_contain_policies_matching_free_text() {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        // For free text search, we just validate that we get a response
        // The actual matching logic depends on the backend implementation
        log.info("Free text search returned {} policies", policies.size());
    }

    @Then("the response should contain policies with specified underwriters")
    public void response_should_contain_policies_with_underwriters() {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        // Validate that policies have underwriter information
        for (Map<String, Object> policy : policies) {
            Object underwriter = policy.get("underwriter");
            // Underwriter can be null or empty, so we just check the field exists
            Assert.assertTrue("Policy should have underwriter field", policy.containsKey("underwriter"));
        }

        log.info("Validated policies contain underwriter information");
    }

    @Then("the response should contain all available policies")
    public void response_should_contain_all_available_policies() {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        Integer totalElements = CommonSteps.response.jsonPath().getInt("totalElements");
        Assert.assertNotNull("Total elements should not be null", totalElements);
        Assert.assertTrue("Total elements should be non-negative", totalElements >= 0);

        log.info("Empty filter search returned {} total policies", totalElements);
    }

    @Then("the response should contain validation error message")
    public void response_should_contain_validation_error_message() {
        // Validate error response structure
        Object errors = CommonSteps.response.jsonPath().get("errors");
        Object message = CommonSteps.response.jsonPath().get("message");
        Object error = CommonSteps.response.jsonPath().get("error");

        Assert.assertTrue("Response should contain error information",
            errors != null || message != null || error != null);

        log.info("Validation error message found in response");
    }

    @Then("validate each policy in response contains required fields")
    public void validate_each_policy_contains_required_fields() {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        for (Map<String, Object> policy : policies) {
            // Validate required fields
            Assert.assertTrue("Policy should have submissionId", policy.containsKey("submissionId"));
            Assert.assertTrue("Policy should have status", policy.containsKey("status"));
            Assert.assertTrue("Policy should have insuredInfo", policy.containsKey("insuredInfo"));
            Assert.assertTrue("Policy should have productCode", policy.containsKey("productCode"));

            // Validate insuredInfo structure
            Map<String, Object> insuredInfo = (Map<String, Object>) policy.get("insuredInfo");
            if (insuredInfo != null) {
                Assert.assertTrue("InsuredInfo should have businessName",
                    insuredInfo.containsKey("businessName"));
            }
        }

        log.info("Validated required fields in {} policies", policies.size());
    }

    @Then("validate pagination metadata in response")
    public void validate_pagination_metadata() {
        // Validate all pagination fields are present and valid
        Integer totalElements = CommonSteps.response.jsonPath().getInt("totalElements");
        Integer totalPages = CommonSteps.response.jsonPath().getInt("totalPages");
        Integer pageNumber = CommonSteps.response.jsonPath().getInt("pageNumber");
        Integer pageSize = CommonSteps.response.jsonPath().getInt("pageSize");
        Boolean first = CommonSteps.response.jsonPath().getBoolean("first");
        Boolean last = CommonSteps.response.jsonPath().getBoolean("last");
        Integer numberOfElements = CommonSteps.response.jsonPath().getInt("numberOfElements");

        Assert.assertNotNull("Total elements should not be null", totalElements);
        Assert.assertNotNull("Total pages should not be null", totalPages);
        Assert.assertNotNull("Page number should not be null", pageNumber);
        Assert.assertNotNull("Page size should not be null", pageSize);
        Assert.assertNotNull("First flag should not be null", first);
        Assert.assertNotNull("Last flag should not be null", last);
        Assert.assertNotNull("Number of elements should not be null", numberOfElements);

        // Validate logical relationships
        Assert.assertTrue("Total elements should be non-negative", totalElements >= 0);
        Assert.assertTrue("Total pages should be non-negative", totalPages >= 0);
        Assert.assertTrue("Page number should be non-negative", pageNumber >= 0);
        Assert.assertTrue("Page size should be positive", pageSize > 0);
        Assert.assertTrue("Number of elements should not exceed page size", numberOfElements <= pageSize);

        log.info("Pagination metadata validated successfully");
    }

    @Then("validate total count in response")
    public void validate_total_count() {
        Integer totalElements = CommonSteps.response.jsonPath().getInt("totalElements");
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");

        Assert.assertNotNull("Total elements should not be null", totalElements);
        Assert.assertNotNull("Policies list should not be null", policies);

        // If this is the first page and we have fewer elements than page size,
        // then numberOfElements should equal the size of the data array
        Integer numberOfElements = CommonSteps.response.jsonPath().getInt("numberOfElements");
        Assert.assertEquals("Number of elements should match data array size",
            numberOfElements.intValue(), policies.size());

        log.info("Total count validation completed. Total: {}, Current page: {}",
            totalElements, policies.size());
    }

    @Then("the response should contain policies matching all criteria")
    public void response_should_contain_policies_matching_all_criteria() {
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        // For combined criteria, we validate that we get a response
        // The actual filtering logic validation would require knowing the exact test data
        log.info("Combined criteria search returned {} policies", policies.size());
    }

    @Then("the response should handle special characters correctly")
    public void response_should_handle_special_characters() {
        // Validate that the API doesn't crash with special characters
        Assert.assertTrue("Response should be successful with special characters",
            CommonSteps.response.statusCode() == 200);

        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");
        Assert.assertNotNull("Policies list should not be null", policies);

        log.info("Special characters handled correctly in search");
    }

    @Then("validate response contains expected number of results")
    public void validate_response_contains_expected_results() {
        Integer pageSize = CommonSteps.response.jsonPath().getInt("pageSize");
        Integer numberOfElements = CommonSteps.response.jsonPath().getInt("numberOfElements");
        List<Map<String, Object>> policies = CommonSteps.response.jsonPath().getList("data");

        Assert.assertNotNull("Page size should not be null", pageSize);
        Assert.assertNotNull("Number of elements should not be null", numberOfElements);
        Assert.assertNotNull("Policies list should not be null", policies);

        // Validate that the number of returned policies matches numberOfElements
        Assert.assertEquals("Returned policies count should match numberOfElements",
            numberOfElements.intValue(), policies.size());

        // Validate that we don't exceed page size
        Assert.assertTrue("Number of elements should not exceed page size",
            numberOfElements <= pageSize);

        log.info("Response contains expected number of results: {}", numberOfElements);
    }
}
