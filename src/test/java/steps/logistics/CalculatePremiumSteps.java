package steps.logistics;

import helpers.CalculatePremiumHelper;
import helpers.HttpHelper;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import pojo.logistics.CalculatePremiumRequest;
import pojo.logistics.GeographicalArea;
import steps.CommonSteps;
import steps.StepsTemplate;
import utils.Constant;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class CalculatePremiumSteps extends StepsTemplate {

    private static final Logger log = LogManager.getLogger(CalculatePremiumSteps.class);
    private String requestPayload;
    private String invalidRequestPayload;
    private final String CALCULATE_PREMIUM_ENDPOINT = "/rater/quote-service/rate/calculate-premium";

    @Given("I have a valid submission payload for premium calculation")
    public void i_have_valid_submission_payload() {
        CalculatePremiumRequest request = CalculatePremiumHelper.createValidRequest();
        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("Valid submission payload prepared for premium calculation");
    }

    @Given("I have submission payload with LOB {string} and class {string}")
    public void i_have_submission_payload_with_lob_and_class(String lob, String classType) {
        CalculatePremiumRequest request = CalculatePremiumHelper.createRequestWithLobAndClass(lob, classType);
        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("Submission payload prepared with LOB: {} and class: {}", lob, classType);
    }

    @Given("I have an invalid submission payload for premium calculation")
    public void i_have_invalid_submission_payload() {
        invalidRequestPayload = CalculatePremiumHelper.createInvalidRequest();
        log.info("Invalid submission payload prepared for premium calculation");
    }

    @Given("I have submission payload with CL class and multiple LOBs {string}")
    public void i_have_submission_payload_with_cl_class_and_multiple_lobs(String lobs) {
        CalculatePremiumRequest request = CalculatePremiumHelper.createRequestWithLobAndClass(lobs, "CL");

        // Set specific values for CL class with multiple LOBs based on the curl data
        request.setRate(0.184);
        request.setPremium(4608.0);
        request.setSubmissionId("971a3837-0eb8-4132-841a-fc890b2299a9");

        // Override receipts values for CL class scenario
        request.setReceiptsAsBrokerAgentAmount("2500000");
        request.setReceiptsAsBrokerAgentPercentage("10");
        request.setReceiptsAsIndirectAirCarrierAmount("2500000");
        request.setReceiptsAsIndirectAirCarrierPercentage("10");
        request.setReceiptsAsMotorTruckCarrierAmount("2500000");
        request.setReceiptsAsMotorTruckCarrierPercentage("10");
        request.setReceiptsAsNVOCCAmount("2500000");
        request.setReceiptsAsNVOCCPercentage("10");
        request.setReceiptsAsWarehouseOperatorAmount("2500000");
        request.setReceiptsAsWarehouseOperatorPercentage("10");

        // Override shipper interest values for CL class
        request.setShipperInterestAdjustmentPeriod("N/A");
        request.setShipperInterestAdjustmentRate("N/A");
        request.setShipperInterestCargoInlandDeposit("Not Covered");
        request.setShipperInterestWarehouseStorageDeposit("Not Covered");

        // Set cargo liability premium from curl data
        request.setCargoLiabilityPremium(28470.0);
        request.setWhllPremium(4608.0);

        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("CL class submission payload prepared with multiple LOBs: {}", lobs);
    }

    @Given("I have submission payload with geographical area percentages")
    public void i_have_submission_payload_with_geographical_area_percentages() {
        CalculatePremiumRequest request = CalculatePremiumHelper.createValidRequest();

        // Set specific values based on the curl data for geographical area percentages
        request.setRate(0.184);
        request.setPremium(4608.0);
        request.setClassType(Arrays.asList("CL"));
        request.setLob(Arrays.asList("NVOCC", "IAC", "MTC", "FFLL", "FFE&O", "WHLL"));

        // Set geographical areas with percentages
        List<GeographicalArea> geoAreas = Arrays.asList(
            new GeographicalArea("US & Canada", "10", "6dcffcb6-2a6c-47a4-b3a7-0a12c0ff304b"),
            new GeographicalArea("Europe", "10", "1222a6f2-b555-4800-b011-8d6b6ae7af2e"),
            new GeographicalArea("Asia", "50", "ed81914c-b86c-418c-8273-97b06b4482ff"),
            new GeographicalArea("Africa", "10", "d50ec20e-3a70-456e-804e-214f47bad02d"),
            new GeographicalArea("LATAM", "10", "76048f16-9925-4e64-85dd-8cbb735c8cb8"),
            new GeographicalArea("Australia", "10", "30dc4ba1-3e14-40a6-9711-5fe224d59782")
        );
        request.setGeographicalArea(geoAreas);

        // Override receipts values for this scenario
        request.setReceiptsAsBrokerAgentAmount("2500000");
        request.setReceiptsAsBrokerAgentPercentage("10");
        request.setReceiptsAsIndirectAirCarrierAmount("2500000");
        request.setReceiptsAsIndirectAirCarrierPercentage("10");
        request.setReceiptsAsMotorTruckCarrierAmount("2500000");
        request.setReceiptsAsMotorTruckCarrierPercentage("10");
        request.setReceiptsAsNVOCCAmount("2500000");
        request.setReceiptsAsNVOCCPercentage("10");
        request.setReceiptsAsWarehouseOperatorAmount("2500000");
        request.setReceiptsAsWarehouseOperatorPercentage("10");

        // Set cargo liability premium from curl data
        request.setCargoLiabilityPremium(28470.0);
        request.setWhllPremium(4608.0);

        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("Submission payload prepared with geographical area percentages");
    }

    @When("I send POST request to calculate premium endpoint")
    public void i_send_post_request_to_calculate_premium() throws Throwable {
        CommonSteps.response = httpHelper.requestPost(CALCULATE_PREMIUM_ENDPOINT, requestPayload != null ? requestPayload : invalidRequestPayload);
        log.info("POST request sent to calculate premium endpoint");
    }

    @When("I send POST request to calculate premium endpoint without authorization")
    public void i_send_post_request_without_authorization() throws Throwable {
        HttpHelper unauthorizedHelper = new HttpHelper(false);
        CommonSteps.response = unauthorizedHelper.requestPost(CALCULATE_PREMIUM_ENDPOINT, requestPayload);
        log.info("POST request sent to calculate premium endpoint without authorization");
    }

    @Then("the response should contain premium calculation details")
    public void response_should_contain_premium_calculation_details() {
        // Validate that response contains expected premium calculation fields
        Assert.assertNotNull("Premium field should not be null",
            CommonSteps.response.jsonPath().get("premium"));
        Assert.assertNotNull("Rate field should not be null",
            CommonSteps.response.jsonPath().get("rate"));
        Assert.assertNotNull("Premium summary should not be null",
            CommonSteps.response.jsonPath().get("premiumSummary"));

        log.info("Premium calculation details validated successfully");
    }

    @Then("validate premium calculation fields")
    public void validate_premium_calculation_fields() {
        // Validate specific premium calculation fields
        Object premium = CommonSteps.response.jsonPath().get("premium");
        Object rate = CommonSteps.response.jsonPath().get("rate");
        Object rollOverPremium = CommonSteps.response.jsonPath().get("rollOverPremium");
        Object rollOverRate = CommonSteps.response.jsonPath().get("rollOverRate");

        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertNotNull("Roll over premium should not be null", rollOverPremium);
        Assert.assertNotNull("Roll over rate should not be null", rollOverRate);

        // Validate premium summary fields
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);
        Assert.assertTrue("Premium summary should contain totalPremium",
            premiumSummary.containsKey("totalPremium"));
        Assert.assertTrue("Premium summary should contain brokerCommission",
            premiumSummary.containsKey("brokerCommission"));

        log.info("All premium calculation fields validated successfully");
    }

    @Then("the premium should be calculated for LOB {string}")
    public void premium_should_be_calculated_for_lob(String expectedLob) {
        // Validate that premium is calculated for the specified LOB
        List<String> lobList = CommonSteps.response.jsonPath().getList("lob");
        Assert.assertNotNull("LOB list should not be null", lobList);

        String[] expectedLobs = expectedLob.split(",");
        for (String lob : expectedLobs) {
            Assert.assertTrue("LOB " + lob.trim() + " should be present in response",
                lobList.contains(lob.trim()));
        }

        // Validate that premium is greater than 0
        Double premium = CommonSteps.response.jsonPath().getDouble("premium");
        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertTrue("Premium should be greater than 0", premium > 0);

        log.info("Premium calculation validated for LOB: {}", expectedLob);
    }

    @Then("the response should contain error message")
    public void response_should_contain_error_message() {
        // Validate error response structure
        Object errors = CommonSteps.response.jsonPath().get("errors");
        Object message = CommonSteps.response.jsonPath().get("message");

        Assert.assertTrue("Response should contain either errors or message field",
            errors != null || message != null);

        log.info("Error message validation completed");
    }

    @Then("the calculate premium response should contain class {string}")
    public void calculate_premium_response_should_contain_class(String expectedClass) {
        List<String> classList = CommonSteps.response.jsonPath().getList("class");
        Assert.assertNotNull("Class list should not be null", classList);
        Assert.assertTrue("Class list should contain " + expectedClass, classList.contains(expectedClass));
        log.info("Validated calculate premium response contains class: {}", expectedClass);
    }

    @Then("validate multiple LOB coverage {string}")
    public void validate_multiple_lob_coverage(String expectedLobs) {
        List<String> lobList = CommonSteps.response.jsonPath().getList("lob");
        Assert.assertNotNull("LOB list should not be null", lobList);

        String[] expectedLobArray = expectedLobs.split(",");
        for (String expectedLob : expectedLobArray) {
            String trimmedLob = expectedLob.trim();
            Assert.assertTrue("LOB list should contain " + trimmedLob, lobList.contains(trimmedLob));
        }

        log.info("Validated multiple LOB coverage: {}", expectedLobs);
    }

    @Then("validate receipts values for CL class")
    public void validate_receipts_values_for_cl_class() {
        // Validate receipts amounts for CL class (should be 2500000)
        String brokerAgentAmount = CommonSteps.response.jsonPath().getString("receiptsAsBrokerAgentAmount");
        String brokerAgentPercentage = CommonSteps.response.jsonPath().getString("receiptsAsBrokerAgentPercentage");

        Assert.assertEquals("Broker agent amount should be 2500000 for CL class", "2500000", brokerAgentAmount);
        Assert.assertEquals("Broker agent percentage should be 10 for CL class", "10", brokerAgentPercentage);

        // Validate shipper interest fields for CL class
        String adjustmentPeriod = CommonSteps.response.jsonPath().getString("shipperInterestAdjustmentPeriod");
        String adjustmentRate = CommonSteps.response.jsonPath().getString("shipperInterestAdjustmentRate");
        String cargoInlandDeposit = CommonSteps.response.jsonPath().getString("shipperInterestCargoInlandDeposit");

        Assert.assertEquals("Adjustment period should be N/A for CL class", "N/A", adjustmentPeriod);
        Assert.assertEquals("Adjustment rate should be N/A for CL class", "N/A", adjustmentRate);
        Assert.assertEquals("Cargo inland deposit should be Not Covered for CL class", "Not Covered", cargoInlandDeposit);

        log.info("Receipts values for CL class validated successfully");
    }

    @Then("validate geographical area percentages")
    public void validate_geographical_area_percentages() {
        List<Map<String, Object>> geographicalAreas = CommonSteps.response.jsonPath().getList("geographicalArea");
        Assert.assertNotNull("Geographical areas should not be null", geographicalAreas);
        Assert.assertFalse("Geographical areas should not be empty", geographicalAreas.isEmpty());

        // Validate that geographical areas have percentages
        boolean foundPercentages = false;
        for (Map<String, Object> area : geographicalAreas) {
            String percentage = (String) area.get("percentage");
            if (percentage != null && !percentage.isEmpty()) {
                foundPercentages = true;
                break;
            }
        }

        Assert.assertTrue("At least one geographical area should have percentage", foundPercentages);
        log.info("Geographical area percentages validated successfully");
    }

    @Then("validate premium summary breakdown")
    public void validate_premium_summary_breakdown() {
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);

        // Validate specific premium summary fields
        Assert.assertTrue("Premium summary should contain totalPremium", premiumSummary.containsKey("totalPremium"));
        Assert.assertTrue("Premium summary should contain cargoLiabilitiesPremiumSummary", premiumSummary.containsKey("cargoLiabilitiesPremiumSummary"));
        Assert.assertTrue("Premium summary should contain warehouseLegalLiabilityPremiumSummary", premiumSummary.containsKey("warehouseLegalLiabilityPremiumSummary"));
        Assert.assertTrue("Premium summary should contain brokerCommission", premiumSummary.containsKey("brokerCommission"));
        Assert.assertTrue("Premium summary should contain blueHavenUWCommission", premiumSummary.containsKey("blueHavenUWCommission"));

        log.info("Premium summary breakdown validated successfully");
    }

    @Then("validate cargo liability premium {double}")
    public void validate_cargo_liability_premium(Double expectedPremium) {
        Double actualPremium = CommonSteps.response.jsonPath().getDouble("premiumSummary.cargoLiabilitiesPremiumSummary");
        Assert.assertNotNull("Cargo liability premium should not be null", actualPremium);
        Assert.assertEquals("Cargo liability premium should match expected value", expectedPremium, actualPremium, 0.01);
        log.info("Cargo liability premium validated: Expected {}, Actual {}", expectedPremium, actualPremium);
    }

    @Then("validate warehouse legal liability premium {double}")
    public void validate_warehouse_legal_liability_premium(Double expectedPremium) {
        Double actualPremium = CommonSteps.response.jsonPath().getDouble("premiumSummary.warehouseLegalLiabilityPremiumSummary");
        Assert.assertNotNull("Warehouse legal liability premium should not be null", actualPremium);
        Assert.assertEquals("Warehouse legal liability premium should match expected value", expectedPremium, actualPremium, 0.01);
        log.info("Warehouse legal liability premium validated: Expected {}, Actual {}", expectedPremium, actualPremium);
    }

    @Then("validate total premium {double}")
    public void validate_total_premium(Double expectedTotalPremium) {
        Double actualTotalPremium = CommonSteps.response.jsonPath().getDouble("premiumSummary.totalPremium");
        Assert.assertNotNull("Total premium should not be null", actualTotalPremium);
        Assert.assertEquals("Total premium should match expected value", expectedTotalPremium, actualTotalPremium, 0.01);
        log.info("Total premium validated: Expected {}, Actual {}", expectedTotalPremium, actualTotalPremium);
    }


}
