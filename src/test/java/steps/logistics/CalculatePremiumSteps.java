package steps.logistics;

import helpers.CalculatePremiumHelper;
import helpers.HttpHelper;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import io.restassured.response.Response;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.junit.Assert;
import org.junit.jupiter.api.Assertions;
import pojo.logistics.CalculatePremiumRequest;
import steps.CommonSteps;
import steps.StepsTemplate;
import utils.Constant;

import java.util.List;
import java.util.Map;

public class CalculatePremiumSteps extends StepsTemplate {

    private static final Logger log = LogManager.getLogger(CalculatePremiumSteps.class);
    private String requestPayload;
    private String invalidRequestPayload;
    private final String CALCULATE_PREMIUM_ENDPOINT = "/rater/quote-service/rate/calculate-premium";

    @Given("I have a valid submission payload for premium calculation")
    public void i_have_valid_submission_payload() {
        CalculatePremiumRequest request = CalculatePremiumHelper.createValidRequest();
        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("Valid submission payload prepared for premium calculation");
    }

    @Given("I have submission payload with LOB {string} and class {string}")
    public void i_have_submission_payload_with_lob_and_class(String lob, String classType) {
        CalculatePremiumRequest request = CalculatePremiumHelper.createRequestWithLobAndClass(lob, classType);
        requestPayload = CalculatePremiumHelper.toJsonString(request);
        log.info("Submission payload prepared with LOB: {} and class: {}", lob, classType);
    }

    @Given("I have an invalid submission payload for premium calculation")
    public void i_have_invalid_submission_payload() {
        invalidRequestPayload = CalculatePremiumHelper.createInvalidRequest();
        log.info("Invalid submission payload prepared for premium calculation");
    }

    @When("I send POST request to calculate premium endpoint")
    public void i_send_post_request_to_calculate_premium() throws Throwable {
        CommonSteps.response = httpHelper.requestPost(CALCULATE_PREMIUM_ENDPOINT, requestPayload != null ? requestPayload : invalidRequestPayload);
        log.info("POST request sent to calculate premium endpoint");
    }

    @When("I send POST request to calculate premium endpoint without authorization")
    public void i_send_post_request_without_authorization() throws Throwable {
        HttpHelper unauthorizedHelper = new HttpHelper(false);
        CommonSteps.response = unauthorizedHelper.requestPost(CALCULATE_PREMIUM_ENDPOINT, requestPayload);
        log.info("POST request sent to calculate premium endpoint without authorization");
    }

    @Then("the response should contain premium calculation details")
    public void response_should_contain_premium_calculation_details() {
        // Validate that response contains expected premium calculation fields
        Assert.assertNotNull("Premium field should not be null",
            CommonSteps.response.jsonPath().get("premium"));
        Assert.assertNotNull("Rate field should not be null",
            CommonSteps.response.jsonPath().get("rate"));
        Assert.assertNotNull("Premium summary should not be null",
            CommonSteps.response.jsonPath().get("premiumSummary"));

        log.info("Premium calculation details validated successfully");
    }

    @Then("validate premium calculation fields")
    public void validate_premium_calculation_fields() {
        // Validate specific premium calculation fields
        Object premium = CommonSteps.response.jsonPath().get("premium");
        Object rate = CommonSteps.response.jsonPath().get("rate");
        Object rollOverPremium = CommonSteps.response.jsonPath().get("rollOverPremium");
        Object rollOverRate = CommonSteps.response.jsonPath().get("rollOverRate");

        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertNotNull("Rate should not be null", rate);
        Assert.assertNotNull("Roll over premium should not be null", rollOverPremium);
        Assert.assertNotNull("Roll over rate should not be null", rollOverRate);

        // Validate premium summary fields
        Map<String, Object> premiumSummary = CommonSteps.response.jsonPath().getMap("premiumSummary");
        Assert.assertNotNull("Premium summary should not be null", premiumSummary);
        Assert.assertTrue("Premium summary should contain totalPremium",
            premiumSummary.containsKey("totalPremium"));
        Assert.assertTrue("Premium summary should contain brokerCommission",
            premiumSummary.containsKey("brokerCommission"));

        log.info("All premium calculation fields validated successfully");
    }

    @Then("the premium should be calculated for LOB {string}")
    public void premium_should_be_calculated_for_lob(String expectedLob) {
        // Validate that premium is calculated for the specified LOB
        List<String> lobList = CommonSteps.response.jsonPath().getList("lob");
        Assert.assertNotNull("LOB list should not be null", lobList);

        String[] expectedLobs = expectedLob.split(",");
        for (String lob : expectedLobs) {
            Assert.assertTrue("LOB " + lob.trim() + " should be present in response",
                lobList.contains(lob.trim()));
        }

        // Validate that premium is greater than 0
        Double premium = CommonSteps.response.jsonPath().getDouble("premium");
        Assert.assertNotNull("Premium should not be null", premium);
        Assert.assertTrue("Premium should be greater than 0", premium > 0);

        log.info("Premium calculation validated for LOB: {}", expectedLob);
    }

    @Then("the response should contain error message")
    public void response_should_contain_error_message() {
        // Validate error response structure
        Object errors = CommonSteps.response.jsonPath().get("errors");
        Object message = CommonSteps.response.jsonPath().get("message");

        Assert.assertTrue("Response should contain either errors or message field",
            errors != null || message != null);

        log.info("Error message validation completed");
    }


}
