import io.cucumber.junit.Cucumber;
import io.cucumber.junit.CucumberOptions;
import org.junit.runner.RunWith;

@RunWith(Cucumber.class)
@CucumberOptions(
        plugin = {"pretty", "html:target/cucumber-html-report.html", "json:target/cucumber.json"},
        features = "src/test/resources/features/logistics/PolicySearch.feature",
        glue = "steps",
        tags = "@policySearch"
)
public class policySearchTestRunner {
}
