@endorsements @beSmokeTest @beRegression
Feature: Endorsement Service

  Scenario Outline: Search Endorsements by filters
    When POST "/core/policy-service/policies/filter?pageNumber=<pageNumber>&pageSize=<pageSize>" to save submissions ID
 """
  {"productTypes":[<productTypes>],"statuses":[<statuses>],"underwriters":[<underwriters>]}
"""
    Then the status code should be 200
    And the response time is less then <3000>
    When Get endorsement transactions "/core/policy-service/policies/:policyID/endorsements?pageNumber=0&pageSize=15"
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes | statuses | underwriters | pageNumber | pageSize |
      |              |          | "backend"    | 0          | 1        |

  Scenario Outline: Fetch policy status
    When POST "/core/policy-service/policies/filter?pageNumber=<pageNumber>&pageSize=<pageSize>" to save submissions ID
 """
  {"productTypes":[<productTypes>],"statuses":[<statuses>],"underwriters":[<underwriters>]}
"""
    Then the status code should be 200
    And the response time is less then <3000>
    When Get endorsement transactions "/core/policy-service/policies/:policyID/status"
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes | statuses | underwriters | pageNumber | pageSize |
      |              |          | "backend"    | 0          | 1        |
