@refer @beSmokeTest @beRegression
  @dataTest
Feature: Test reference drop-down data service

  Scenario : Get risk state
    When POST "core/reference-data-service/values/search?page=0&size=100&entityType=riskStates"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "excess" "<productCode>"

  Scenario: Verify personalAndAdvertisingInjuryLimit response for Excess
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=personalAndAdvertisingInjuryLimit&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the personalAndAdvertisingInjuryLimit response matches expected

  Scenario: Verify generalAggregateLimit response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=generalAggregateLimit&searchText=&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the entityType=generalAggregateLimit&searchText= response matches expected


  Scenario: Verify medicalPaymentLimit response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=medicalPaymentLimit&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the medicalPaymentLimit response matches expected

  Scenario: Verify damageToPermisesRentedToYou response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=damageToPermisesRentedToYou&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the damageToPermisesRentedToYou response matches expected


  Scenario: Verify eachOccuranceExcess response
    When POST "/core/reference-data-service/values/search?page=0&size=50&entityType=eachOccuranceExcess&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the eachOccuranceExcess response matches expected


  Scenario: Verify eachOccurancePrimary response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=eachOccurancePrimary&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the eachOccurancePrimary response matches expected


  Scenario: Verify otherAggregateLimit response
    When POST "/core/reference-data-service/values/search?page=0&size=40&entityType=otherAggregateLimit&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the otherAggregateLimit response matches expected


  Scenario: Verify productsCompletedOperationsAggregate response for Excess
    When POST "/core/reference-data-service/values/search?page=0&size=40&entityType=productsCompletedOperationsAggregate&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the productsCompletedOperationsAggregate response matches expected for Excess

  Scenario: Verify productsCompletedOperationsAggregateLimit response for Primary
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=productsCompletedOperationsAggregateLimit&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the productsCompletedOperationsAggregate response matches expected for Primary


  Scenario: Verify retentionType response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=retentionType&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the retentionType response matches expected



  Scenario: Verify deductibleAmount response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=deductibleAmount&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the deductibleAmount response matches expected

  Scenario: Verify SIR Amount response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=SIR Amount&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the SIR Amount response matches expected

  Scenario: Verify # of General Aggregate Reinstatements response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=generalAggregateReinstatements&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the # of General Aggregate Reinstatements response matches expected

  Scenario: Verify # of General Aggregate exposureBasis response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=exposureBasis&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the # of General Aggregate exposureBasis response matches expected

  Scenario: Verify # of Carrier Excess exposureBasis response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=excessCarrier&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the excessCarrier response matches expected

  Scenario: Verify # of Carrier primary exposureBasis response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=primaryCarrier&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the primaryCarrier response matches expected

  Scenario: Verify Treaty exposureBasis response
    When POST "/core/reference-data-service/values/search?page=0&size=20&entityType=Treaty&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the Treaty response matches expected

    @riskStates
  Scenario: Verify riskState response
    When POST "/core/reference-data-service/values/search?page=0&size=100&entityType=riskStates&searchText="
    """
    {"metaDataFilters":[]}
    """
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the riskState response matches expected