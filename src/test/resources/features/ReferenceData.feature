@refer @beSmokeTest @beRegression
Feature: Test reference data service

  @template1
  Scenario Outline: GET Policy Hidden Fields for POLICY_DETAIL Excess <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "excess" "<productCode>"
    Examples:
      | productCode | state                |
      | XCAN        | ISSUED               |
      | XOAN        | ISSUED               |
      | XAUT        | ISSUED               |
      | EIAN        | ISSUED               |
      | XCAN        | BOUND                |
      | XOAN        | BOUND                |
      | XAUT        | BOUND                |
      | EIAN        | BOUND                |
      | XCAN        | ENDORSEMENT_MONETARY |
      | XOAN        | ENDORSEMENT_MONETARY |
      | XAUT        | ENDORSEMENT_MONETARY |
      | EIAN        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "primary" "<productCode>"
    Examples:
      | productCode | state                |
      | PCAN        | ISSUED               |
      | POAN        | ISSUED               |
      | PIAN        | ISSUED               |
      | PCAN        | BOUND                |
      | POAN        | BOUND                |
      | PIAN        | BOUND                |
      | PCAN        | ENDORSEMENT_MONETARY |
      | POAN        | ENDORSEMENT_MONETARY |
      | PIAN        | ENDORSEMENT_MONETARY |

  @template
  Scenario Outline: GET Policy Hidden Fields for POLICY_DETAIL Excess Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XANN        | ISSUED               |
      | EIWN        | ISSUED               |
      | EIPS        | ISSUED               |
      | XRPS        | ISSUED               |
      | XRWU        | ISSUED               |
      | XOWI        | ISSUED               |
      | XWUP        | ISSUED               |
      | XWRP        | ISSUED               |
      | XPRS        | ISSUED               |
      | XANN        | BOUND                |
      | EIWN        | BOUND                |
      | EIPS        | BOUND                |
      | XRPS        | BOUND                |
      | XRWU        | BOUND                |
      | XOWI        | BOUND                |
      | XWUP        | BOUND                |
      | XWRP        | BOUND                |
      | XPRS        | BOUND                |
      | XANN        | ENDORSEMENT_MONETARY |
      | EIWN        | ENDORSEMENT_MONETARY |
      | EIPS        | ENDORSEMENT_MONETARY |
      | XRPS        | ENDORSEMENT_MONETARY |
      | XRWU        | ENDORSEMENT_MONETARY |
      | XOWI        | ENDORSEMENT_MONETARY |
      | XWUP        | ENDORSEMENT_MONETARY |
      | XWRP        | ENDORSEMENT_MONETARY |
      | XPRS        | ENDORSEMENT_MONETARY |


  @template @primary
  Scenario Outline: GET Policy Hidden Fields for POLICY_DETAIL Primary Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PANN        | ISSUED               |
      | PIWU        | ISSUED               |
      | PIPS        | ISSUED               |
      | PRPS        | ISSUED               |
      | PRWU        | ISSUED               |
      | POWI        | ISSUED               |
      | PPRS        | ISSUED               |
      | PWUP        | ISSUED               |
      | PWRP        | ISSUED               |
      | PANN        | BOUND                |
      | PIWU        | BOUND                |
      | PIPS        | BOUND                |
      | PRPS        | BOUND                |
      | PRWU        | BOUND                |
      | POWI        | BOUND                |
      | PPRS        | BOUND                |
      | PWUP        | BOUND                |
      | PWRP        | BOUND                |
      | PANN        | ENDORSEMENT_MONETARY |
      | PIWU        | ENDORSEMENT_MONETARY |
      | PIPS        | ENDORSEMENT_MONETARY |
      | PRPS        | ENDORSEMENT_MONETARY |
      | PRWU        | ENDORSEMENT_MONETARY |
      | POWI        | ENDORSEMENT_MONETARY |
      | PPRS        | ENDORSEMENT_MONETARY |
      | PWUP        | ENDORSEMENT_MONETARY |
      | PWRP        | ENDORSEMENT_MONETARY |


  @template
  Scenario Outline: GET Policy Hidden Fields for POLICY_DETAIL NY EXCESS <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XNPS        | ISSUED               |
      | XNWU        | ISSUED               |
      | XNAG        | ISSUED               |
      | XNPS        | BOUND                |
      | XNWU        | BOUND                |
      | XNAG        | BOUND                |
      | XNPS        | ENDORSEMENT_MONETARY |
      | XNWU        | ENDORSEMENT_MONETARY |
      | XNAG        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for POLICY_DETAIL NY Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=POLICY_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PNPS        | ISSUED               |
      | PNWU        | ISSUED               |
      | PNAG        | ISSUED               |
      | PNPS        | BOUND                |
      | PNWU        | BOUND                |
      | PNAG        | BOUND                |
      | PNPS        | ENDORSEMENT_MONETARY |
      | PNWU        | ENDORSEMENT_MONETARY |
      | PNAG        | ENDORSEMENT_MONETARY |



################################### BINDING_DETAIL ###################################
  @template
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL Excess <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "excess" "<productCode>"
    Examples:
      | productCode | state                |
      | XCAN        | ISSUED               |
      | XOAN        | ISSUED               |
      | XAUT        | ISSUED               |
      | EIAN        | ISSUED               |
      | XCAN        | BOUND                |
      | XOAN        | BOUND                |
      | XAUT        | BOUND                |
      | EIAN        | BOUND                |
      | XCAN        | ENDORSEMENT_MONETARY |
      | XOAN        | ENDORSEMENT_MONETARY |
      | XAUT        | ENDORSEMENT_MONETARY |
      | EIAN        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "primary" "<productCode>"
    Examples:
      | productCode | state                |
      | PCAN        | ISSUED               |
      | POAN        | ISSUED               |
      | PIAN        | ISSUED               |
      | PCAN        | BOUND                |
      | POAN        | BOUND                |
      | PIAN        | BOUND                |
      | PCAN        | ENDORSEMENT_MONETARY |
      | POAN        | ENDORSEMENT_MONETARY |
      | PIAN        | ENDORSEMENT_MONETARY |

  @template
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL Excess Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XANN        | ISSUED               |
      | EIWN        | ISSUED               |
      | EIPS        | ISSUED               |
      | XRPS        | ISSUED               |
      | XRWU        | ISSUED               |
      | XOWI        | ISSUED               |
      | XWUP        | ISSUED               |
      | XWRP        | ISSUED               |
      | XPRS        | ISSUED               |
      | XANN        | BOUND                |
      | EIWN        | BOUND                |
      | EIPS        | BOUND                |
      | XRPS        | BOUND                |
      | XRWU        | BOUND                |
      | XOWI        | BOUND                |
      | XWUP        | BOUND                |
      | XWRP        | BOUND                |
      | XPRS        | BOUND                |
      | XANN        | ENDORSEMENT_MONETARY |
      | EIWN        | ENDORSEMENT_MONETARY |
      | EIPS        | ENDORSEMENT_MONETARY |
      | XRPS        | ENDORSEMENT_MONETARY |
      | XRWU        | ENDORSEMENT_MONETARY |
      | XOWI        | ENDORSEMENT_MONETARY |
      | XWUP        | ENDORSEMENT_MONETARY |
      | XWRP        | ENDORSEMENT_MONETARY |
      | XPRS        | ENDORSEMENT_MONETARY |


  @template @primary
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL Primary Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PANN        | ISSUED               |
      | PIWU        | ISSUED               |
      | PIPS        | ISSUED               |
      | PRPS        | ISSUED               |
      | PRWU        | ISSUED               |
      | POWI        | ISSUED               |
      | PPRS        | ISSUED               |
      | PWUP        | ISSUED               |
      | PWRP        | ISSUED               |
      | PANN        | BOUND                |
      | PIWU        | BOUND                |
      | PIPS        | BOUND                |
      | PRPS        | BOUND                |
      | PRWU        | BOUND                |
      | POWI        | BOUND                |
      | PPRS        | BOUND                |
      | PWUP        | BOUND                |
      | PWRP        | BOUND                |
      | PANN        | ENDORSEMENT_MONETARY |
      | PIWU        | ENDORSEMENT_MONETARY |
      | PIPS        | ENDORSEMENT_MONETARY |
      | PRPS        | ENDORSEMENT_MONETARY |
      | PRWU        | ENDORSEMENT_MONETARY |
      | POWI        | ENDORSEMENT_MONETARY |
      | PPRS        | ENDORSEMENT_MONETARY |
      | PWUP        | ENDORSEMENT_MONETARY |
      | PWRP        | ENDORSEMENT_MONETARY |


  @template
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL NY EXCESS <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XNPS        | ISSUED               |
      | XNWU        | ISSUED               |
      | XNAG        | ISSUED               |
      | XNPS        | BOUND                |
      | XNWU        | BOUND                |
      | XNAG        | BOUND                |
      | XNPS        | ENDORSEMENT_MONETARY |
      | XNWU        | ENDORSEMENT_MONETARY |
      | XNAG        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for BINDING_DETAIL NY Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PNPS        | ISSUED               |
      | PNWU        | ISSUED               |
      | PNAG        | ISSUED               |
      | PNPS        | BOUND                |
      | PNWU        | BOUND                |
      | PNAG        | BOUND                |
      | PNPS        | ENDORSEMENT_MONETARY |
      | PNWU        | ENDORSEMENT_MONETARY |
      | PNAG        | ENDORSEMENT_MONETARY |


    ################################################ENDORSEMENT#################################################
    ################################################ENDORSEMENT#################################################
    ################################################ENDORSEMENT#################################################

  @template
  Scenario Outline: GET Policy Hidden Fields ENDORSEMENT for Excess <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "excess" "<productCode>"
    Examples:
      | productCode | state                |
      | XCAN        | ISSUED               |
      | XOAN        | ISSUED               |
      | XAUT        | ISSUED               |
      | EIAN        | ISSUED               |
      | XCAN        | BOUND                |
      | XOAN        | BOUND                |
      | XAUT        | BOUND                |
      | EIAN        | BOUND                |
      | XCAN        | ENDORSEMENT_MONETARY |
      | XOAN        | ENDORSEMENT_MONETARY |
      | XAUT        | ENDORSEMENT_MONETARY |
      | EIAN        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for ENDORSEMENT Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "primary" "<productCode>"
    Examples:
      | productCode | state                |
      | PCAN        | ISSUED               |
      | POAN        | ISSUED               |
      | PIAN        | ISSUED               |
      | PCAN        | BOUND                |
      | POAN        | BOUND                |
      | PIAN        | BOUND                |
      | PCAN        | ENDORSEMENT_MONETARY |
      | POAN        | ENDORSEMENT_MONETARY |
      | PIAN        | ENDORSEMENT_MONETARY |

  @template
  Scenario Outline: GET Policy Hidden Fields for ENDORSEMENT Excess Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XANN        | ISSUED               |
      | EIWN        | ISSUED               |
      | EIPS        | ISSUED               |
      | XRPS        | ISSUED               |
      | XRWU        | ISSUED               |
      | XOWI        | ISSUED               |
      | XWUP        | ISSUED               |
      | XWRP        | ISSUED               |
      | XPRS        | ISSUED               |
      | XANN        | BOUND                |
      | EIWN        | BOUND                |
      | EIPS        | BOUND                |
      | XRPS        | BOUND                |
      | XRWU        | BOUND                |
      | XOWI        | BOUND                |
      | XWUP        | BOUND                |
      | XWRP        | BOUND                |
      | XPRS        | BOUND                |
      | XANN        | ENDORSEMENT_MONETARY |
      | EIWN        | ENDORSEMENT_MONETARY |
      | EIPS        | ENDORSEMENT_MONETARY |
      | XRPS        | ENDORSEMENT_MONETARY |
      | XRWU        | ENDORSEMENT_MONETARY |
      | XOWI        | ENDORSEMENT_MONETARY |
      | XWUP        | ENDORSEMENT_MONETARY |
      | XWRP        | ENDORSEMENT_MONETARY |
      | XPRS        | ENDORSEMENT_MONETARY |


  @template @primary
  Scenario Outline: GET Policy Hidden Fields for ENDORSEMENT Primary Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "projectPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PANN        | ISSUED               |
      | PIWU        | ISSUED               |
      | PIPS        | ISSUED               |
      | PRPS        | ISSUED               |
      | PRWU        | ISSUED               |
      | POWI        | ISSUED               |
      | PPRS        | ISSUED               |
      | PWUP        | ISSUED               |
      | PWRP        | ISSUED               |
      | PANN        | BOUND                |
      | PIWU        | BOUND                |
      | PIPS        | BOUND                |
      | PRPS        | BOUND                |
      | PRWU        | BOUND                |
      | POWI        | BOUND                |
      | PPRS        | BOUND                |
      | PWUP        | BOUND                |
      | PWRP        | BOUND                |
      | PANN        | ENDORSEMENT_MONETARY |
      | PIWU        | ENDORSEMENT_MONETARY |
      | PIPS        | ENDORSEMENT_MONETARY |
      | PRPS        | ENDORSEMENT_MONETARY |
      | PRWU        | ENDORSEMENT_MONETARY |
      | POWI        | ENDORSEMENT_MONETARY |
      | PPRS        | ENDORSEMENT_MONETARY |
      | PWUP        | ENDORSEMENT_MONETARY |
      | PWRP        | ENDORSEMENT_MONETARY |


  @template
  Scenario Outline: GET Policy Hidden Fields for ENDORSEMENT NY EXCESS <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XNPS        | ISSUED               |
      | XNWU        | ISSUED               |
      | XNAG        | ISSUED               |
      | XNPS        | BOUND                |
      | XNWU        | BOUND                |
      | XNAG        | BOUND                |
      | XNPS        | ENDORSEMENT_MONETARY |
      | XNWU        | ENDORSEMENT_MONETARY |
      | XNAG        | ENDORSEMENT_MONETARY |

  @template @primary
  Scenario Outline: GET Policy Hidden Fields for ENDORSEMENT NY Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the hidden fields for product "<productCode>" "nyPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PNPS        | ISSUED               |
      | PNWU        | ISSUED               |
      | PNAG        | ISSUED               |
      | PNPS        | BOUND                |
      | PNWU        | BOUND                |
      | PNAG        | BOUND                |
      | PNPS        | ENDORSEMENT_MONETARY |
      | PNWU        | ENDORSEMENT_MONETARY |
      | PNAG        | ENDORSEMENT_MONETARY |

    ###################### https://staging-api.submission.concirrusquest.com/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=XANN&state=ENDORSEMENT_NON_MONETARY
@locked @template @primary
  Scenario Outline: GET Policy Hidden Fields for Binding Locked cell NY Primary <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the locked fields for product "<productCode>" "nyPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PNPS        | ENDORSEMENT_NON_MONETARY               |
      | PNWU        | ENDORSEMENT_NON_MONETARY               |
#      | PNAG        | ENDORSEMENT_NON_MONETARY               |
      | PNPS        | ENDORSEMENT_MONETARY |
      | PNWU        | ENDORSEMENT_MONETARY |
#      | PNAG        | ENDORSEMENT_MONETARY |

  @locked @template
  Scenario Outline: GET Policy Hidden Fields for Binding Locked cell NY EXCESS <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the locked fields for product "<productCode>" "nyExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XNPS        | ENDORSEMENT_NON_MONETARY                |
      | XNWU        | ENDORSEMENT_NON_MONETARY                |
#      | XNAG        | ENDORSEMENT_NON_MONETARY                |
      | XNPS        | ENDORSEMENT_MONETARY |
      | XNWU        | ENDORSEMENT_MONETARY |
#      | XNAG        | ENDORSEMENT_MONETARY |

  @locked @template
  Scenario Outline: GET Policy Hidden Fields for Binding Locked cell Excess Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the locked fields for product "<productCode>" "projectExcess" "<productCode>"
    Examples:
      | productCode | state                |
      | XANN        | ENDORSEMENT_NON_MONETARY                |
      | EIWN        | ENDORSEMENT_NON_MONETARY                |
      | EIPS        | ENDORSEMENT_NON_MONETARY                |
      | XRPS        | ENDORSEMENT_NON_MONETARY                |
      | XRWU        | ENDORSEMENT_NON_MONETARY                |
      | XOWI        | ENDORSEMENT_NON_MONETARY                |
      | XWUP        | ENDORSEMENT_NON_MONETARY                |
      | XWRP        | ENDORSEMENT_NON_MONETARY                |
      | XPRS        | ENDORSEMENT_NON_MONETARY                |
      | XANN        | ENDORSEMENT_MONETARY |
      | EIWN        | ENDORSEMENT_MONETARY |
      | EIPS        | ENDORSEMENT_MONETARY |
      | XRPS        | ENDORSEMENT_MONETARY |
      | XRWU        | ENDORSEMENT_MONETARY |
      | XOWI        | ENDORSEMENT_MONETARY |
      | XWUP        | ENDORSEMENT_MONETARY |
      | XWRP        | ENDORSEMENT_MONETARY |
      | XPRS        | ENDORSEMENT_MONETARY |


  @locked @template @primary
  Scenario Outline: GET Policy Hidden Fields for Binding Locked cell Primary Project Types <productCode> <state>
    When Get "/core/reference-data-service/jsonTemplate/validation?type=BINDING_DETAIL&lob=CONSTRUCTION&product=<productCode>&state=<state>"
    Then the status code should be 200
    And the response time is less then <5000>
    And validate the locked fields for product "<productCode>" "projectPrimary" "<productCode>"
    Examples:
      | productCode | state                |
      | PANN        | ENDORSEMENT_NON_MONETARY                |
      | PIWU        | ENDORSEMENT_NON_MONETARY                |
      | PIPS        | ENDORSEMENT_NON_MONETARY                |
      | PRPS        | ENDORSEMENT_NON_MONETARY                |
      | PRWU        | ENDORSEMENT_NON_MONETARY                |
      | POWI        | ENDORSEMENT_NON_MONETARY                |
      | PPRS        | ENDORSEMENT_NON_MONETARY                |
      | PWUP        | ENDORSEMENT_NON_MONETARY                |
      | PWRP        | ENDORSEMENT_NON_MONETARY                |
      | PANN        | ENDORSEMENT_MONETARY |
      | PIWU        | ENDORSEMENT_MONETARY |
      | PIPS        | ENDORSEMENT_MONETARY |
      | PRPS        | ENDORSEMENT_MONETARY |
      | PRWU        | ENDORSEMENT_MONETARY |
      | POWI        | ENDORSEMENT_MONETARY |
      | PPRS        | ENDORSEMENT_MONETARY |
      | PWUP        | ENDORSEMENT_MONETARY |
      | PWRP        | ENDORSEMENT_MONETARY |
#
#@endstate
#  Scenario Outline: GET Policy Hidden Fields for Binding Locked cell Primary Project Types <productCode>
#    When Get "/core/reference-data-service/jsonTemplate/validation?type=ENDORSEMENT&lob=CONSTRUCTION&product=<productCode>&state=<state>"
#    Then the status code should be 200
#    And the response time is less then <5000>
#    And validate the hidden fields for product "<productCode>" "excess" "<productCode>"
#    Examples:
#      | productCode | state                |
#      | PANN        | ENDORSEMENT               |
#      | PIWU        | ENDORSEMENT                |
#      | PIPS        | ENDORSEMENT                |
#      | PRPS        | ENDORSEMENT                |
#      | PRWU        | ENDORSEMENT                |
#      | POWI        | ENDORSEMENT                |
#      | PPRS        | ENDORSEMENT                |
#      | PWUP        | ENDORSEMENT                |
#      | PWRP        | ENDORSEMENT                |
#      | PANN        | ENDORSEMENT |
#      | PIWU        | ENDORSEMENT |
#      | PIPS        | ENDORSEMENT |
#      | PRPS        | ENDORSEMENT |
#      | PRWU        | ENDORSEMENT |
#      | POWI        | ENDORSEMENT |
#      | PPRS        | ENDORSEMENT |
#      | PWUP        | ENDORSEMENT |
#      | PWRP        | ENDORSEMENT |