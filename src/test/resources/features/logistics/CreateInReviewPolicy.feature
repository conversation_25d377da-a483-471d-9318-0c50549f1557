@Logistics @createPolicy @inReview
Feature: Create In-Review Policy

  Scenario: Create a new policy in review status with valid data
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And the response should contain submission details
    And the policy status should be "REVIEW"
    And the response time is less then <3000>
    And validate policy submission fields

  Scenario Outline: Create policy with different LOB and class combinations
    Given I have policy submission payload with LOB "<lob>" and class "<class>"
    When I send POST request to create policy submission
    Then the status code should be 200
    And the policy should be created with LOB "<lob>" and class "<class>"
    And the policy status should be "REVIEW"
    Examples:
      | lob | class   |
      | CAR | CL      |
      | CAR | SI      |
      | CAR | CL,SI   |
      | WRH | CL      |
      | WRH | SI      |

  Scenario: Create policy with invalid submission data
    Given I have an invalid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 400
    And the response should contain error message

  Scenario: Create policy without authorization
    Given I have a valid policy submission payload
    When I send POST request to create policy submission without authorization
    Then the status code should be 401

  Scenario: Create policy with missing required fields
    Given I have policy submission payload with missing required fields
    When I send POST request to create policy submission
    Then the status code should be 400
    And the response should contain validation error message

  Sc<PERSON>rio: Verify policy submission response structure
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And the response should contain all required policy fields
    And validate insured information in response
    And validate business information in response
