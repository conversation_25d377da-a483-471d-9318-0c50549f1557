Feature: Verify Optional Endorsement Forms for Each State

  Scenario Outline: Validate expected endorsement forms appear as "Optional" for state <State>
#    Given I call the forms API for state "<State>" "/reference-data-service/forms/optional"
    Given I call the forms API with:
      | state          | AL                                                                 |
      | classes        | CL,SI                                                              |
      | lobAndConveyance | CAR,NVOCC,MTC,FFLL,IAC,FFE&O,XXWHLL,WRH,ocean,domesticTruckOrRail,internationTruckOrRail |
    Then the status code should be 201
    Then I should see the following forms marked as Optional for state "AL":
      | formName                                                              |
      | CNI_Carriers Legal Liability Policy Form                              |
      | CNI_COI - Template                                                    |
      | CNI_Commodity Index Endorsement                                       |
      | CNI_Communicable Disease Exclusion                                    |
      | CNI_Economic and Trade Sanctions Endorsement                          |
      | CNI_Freight Forwarders Legal Liability Policy Form                    |
      | CNI_Marine Cyber Exclusion                                            |
      | CNI_Policy Declarations                                               |
      | CNI_Policyholder Disclosure Notice of Terrorism Insurance Coverage    |
      | CNI_R.A.C.E. Chemical Biochem Endorsement                             |
      | CNI_S.R. & C.C. Endorsement                                           |
      | CNI_Signature Page                                                    |
      | CNI_TRIA Exclusion                                                    |
      | CNI_War Risks Coverage                                                |

    Examples:
      | State |
      | CO    |
      | IL    |
      | CA    |

  Scenario Outline: Validate form presence based on state, class, and LOB
    Given I call the forms API with 2:
      | state              | <State>         |
      | classes            | <Classes>       |
      | lobAndConveyance   | <LOB>           |
    Then I should see form "<FormName>" in the response for state "<State>"

    Examples:
      | FormName                                       | State | Classes | LOB       |
      | CNI_Carriers Legal Liability Policy Form       | AL    | CL,SI   | NVOCC     |
      | CNI_COI - Template                             | AL    | SI      | WRH       |
      | CNI_Economic and Trade Sanctions Endorsement   | CA    | CL      | FFLL      |