@Logistics @policySearch
Feature: Policy Search API

  Scenario: Search policies with valid filter criteria
    Given I have valid policy search filter criteria
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policy search results
    And the response time is less then <3000>
    And validate policy search response structure

  Scenario Outline: Search policies with different product codes
    Given I have policy search filter with product codes "<productCodes>"
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policies with product codes "<productCodes>"
    Examples:
      | productCodes                                    |
      | CAR                                            |
      | CL,FFE&O                                       |
      | CAR,CL,FFE&O,FFLL,IAC,MTC,NVOCC               |
      | NVOCC,MTC                                      |

  Scenario Outline: Search policies with different status filters
    Given I have policy search filter with status "<status>"
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policies with status "<status>"
    Examples:
      | status           |
      | BOUND            |
      | QUOTED           |
      | REVIEW           |
      | BOUND,QUOTED     |
      | BOUND,QUOTED,REVIEW |

  Scenario Outline: Search policies with pagination parameters
    Given I have policy search filter with page number <pageNumber> and page size <pageSize>
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should respect pagination with page number <pageNumber> and page size <pageSize>
    Examples:
      | pageNumber | pageSize |
      | 0          | 10       |
      | 0          | 30       |
      | 1          | 20       |
      | 2          | 15       |

  Scenario: Search policies with free text search
    Given I have policy search filter with free text "Test Business"
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policies matching free text search

  Scenario: Search policies with underwriter filter
    Given I have policy search filter with underwriters "John Doe,Jane Smith"
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policies with specified underwriters

  Scenario: Search policies with empty filters
    Given I have empty policy search filter criteria
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain all available policies

  Scenario: Search policies with invalid pagination parameters
    Given I have policy search filter with invalid pagination parameters
    When I send POST request to search policies with pagination
    Then the status code should be 400
    And the response should contain validation error message

  Scenario: Search policies without authorization
    Given I have valid policy search filter criteria
    When I send POST request to search policies without authorization
    Then the status code should be 401

  Scenario: Verify policy search response contains required fields
    Given I have valid policy search filter criteria
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And validate each policy in response contains required fields
    And validate pagination metadata in response
    And validate total count in response

  Scenario: Search policies with combined filters
    Given I have policy search filter with multiple criteria:
      | freeText     | Test                    |
      | productCode  | CAR,CL                  |
      | status       | BOUND,REVIEW            |
      | underwriters | John Doe                |
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should contain policies matching all criteria

  Scenario: Search policies with special characters in free text
    Given I have policy search filter with free text containing special characters
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response should handle special characters correctly

  Scenario: Verify policy search performance
    Given I have valid policy search filter criteria
    When I send POST request to search policies with pagination
    Then the status code should be 200
    And the response time is less then <2000>
    And validate response contains expected number of results
