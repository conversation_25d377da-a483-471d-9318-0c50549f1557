@Logistics @policyValidation @detailedValidation
Feature: Detailed Policy Response Validation

  Scenario: Validate comprehensive policy response structure
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate detailed policy response structure
    And validate policy core information
    And validate insured information details
    And validate premium and rating information

  Scenario: Validate policy commodity and geographical information
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate commodity types information
    And validate geographical information
    And validate conveyance information

  Scenario: Validate policy terms and conditions
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate terms and conditions structure
    And validate additional insured information
    And validate broker information

  Scenario: Validate policy financial and loss information
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate premium and rating information
    And validate loss history information
    And validate valuation information

  Scenario: Validate policy response for CAR LOB
    Given I have policy submission payload with LOB "CAR" and class "SI"
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate detailed policy response structure
    And the response should contain LOB "CAR"
    And the response should contain class "SI"
    And validate commodity types information
    And validate conveyance information

  Scenario: Validate policy response contains all required sections
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate detailed policy response structure
    And validate policy core information
    And validate insured information details
    And validate premium and rating information
    And validate commodity types information
    And validate geographical information
    And validate conveyance information
    And validate terms and conditions structure
    And validate additional insured information
    And validate broker information
    And validate loss history information
    And validate valuation information

  Scenario: Validate policy timestamps and audit information
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate detailed policy response structure
    And the response should contain creation timestamp
    And the response should contain update timestamp
    And the response should contain created by information
    And the response should contain updated by information

  Scenario: Validate policy business information
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate policy core information
    And the response should contain business type "New Business"
    And the response should contain valid proposal number
    And the response should contain underwriter name
    And validate insured information details

  Scenario: Validate policy premium calculations
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate premium and rating information
    And the premium should be greater than zero
    And the rate should be greater than zero
    And validate premium summary contains all required fields
    And validate loss history information

  Scenario: Validate policy geographical scope and coverage
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate geographical information
    And the geographical scope should contain worldwide coverage
    And the geographical scope should contain exclusions
    And validate conveyance information
    And all conveyance types should be properly configured
