@Logistics @rateValidation @premiumValidation
Feature: Rate and Premium Validation

  Scenario: Validate specific rate value 0.208
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate specific rate value 0.208
    And the rate should be greater than zero

  Scenario: Validate specific premium value 51974
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate specific premium value 51974.0
    And the premium should be greater than zero

  Scenario: Validate both rate and premium values together
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate rate and premium values
    And validate rate is approximately 0.208
    And validate premium is approximately 51974

  Scenario: Validate rate and premium business logic
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate rate and premium relationship
    And validate premium calculation components

  Scenario Outline: Validate rate tolerance with different values
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate rate is approximately <expectedRate>
    Examples:
      | expectedRate |
      | 0.208        |
      | 0.207        |
      | 0.209        |

  Scenario Outline: Validate premium tolerance with different values
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate premium is approximately <expectedPremium>
    Examples:
      | expectedPremium |
      | 51974           |
      | 51973           |
      | 51975           |

  Scenario: Validate rate and premium for different LOB combinations
    Given I have policy submission payload with LOB "CAR" and class "SI"
    When I send POST request to create policy submission
    Then the status code should be 200
    And the rate should be greater than zero
    And the premium should be greater than zero
    And validate rate and premium relationship

  Scenario: Comprehensive rate and premium validation
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate premium and rating information
    And validate specific rate value 0.208
    And validate specific premium value 51974.0
    And validate rate and premium relationship
    And validate premium calculation components
    And validate premium summary contains all required fields
