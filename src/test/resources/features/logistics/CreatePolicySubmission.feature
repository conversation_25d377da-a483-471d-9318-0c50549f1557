@Logistics @createPolicy @policySubmission
Feature: Create Policy Submission

  Scenario: Create a new policy submission with valid data
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And the response should contain submission details
    And the policy status should be "REVIEW"
    And validate policy submission fields

  Scenario Outline: Create policy submission with different LOB and class combinations
    Given I have policy submission payload with LOB "<lob>" and class "<class>"
    When I send POST request to create policy submission
    Then the status code should be 200
    And the response should contain LOB "<lob>"
    And the response should contain class "<class>"
    And the policy status should be "REVIEW"
    Examples:
      | lob | class |
      | CAR | SI    |
      | CAR | CL    |
      | WRH | SI    |

  Scenario: Create policy submission without authorization
    Given I have a valid policy submission payload
    When I send POST request to create policy submission without authorization
    Then the status code should be 401

  Scenario: Validate comprehensive policy submission response
    Given I have a valid policy submission payload
    When I send POST request to create policy submission
    Then the status code should be 200
    And validate detailed policy response structure
    And validate policy core information
    And validate insured information details
    And validate premium and rating information
