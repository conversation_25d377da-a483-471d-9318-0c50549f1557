@Logistics @calculatePremium
Feature: Calculate Rates and Premium

  Scenario: Calculate premium for logistics with valid data For UW Section
    Given I have a valid submission payload for premium calculation
    When I send POST request to calculate premium endpoint
    Then the status code should be 200
    And the response should contain premium calculation details
    And the response time is less then <3000>
    And validate premium calculation fields
    And validate rate is approximately 0.063 in calculate premium call
    And validate premium is approximately in calculate premium call 75468

  Scenario Outline: Calculate premium for different LOB combinations For <Class>
    Given I have submission payload with LOB "<lob>" and class "<class>"
    When I send POST request to calculate premium endpoint
    Then the status code should be 200
    And the premium should be calculated for LOB "<lob>"
    Examples:
      | lob     | class |
      | CAR     | SI    |
      | WRH     | SI,CL    |
      | CAR,WRH | SI,CL    |
      | CAR,WRH | SI,CL    |
      | CAR,WRH | SI,CL    |


  Scenario: Calculate premium with invalid submission data
    Given I have an invalid submission payload for premium calculation
    When I send POST request to calculate premium endpoint
    Then the status code should be 400
    And the response should contain error message

  Scenario: Calculate premium for CL class with multiple LOBs
    Given I have submission payload with CL class and multiple LOBs "NVOCC,IAC,MTC,FFLL,FFE&O,WHLL"
    When I send POST request to calculate premium endpoint
    Then the status code should be 200
    And the response should contain premium calculation details
    And validate premium calculation fields
    And the response should contain class "CL"
    And validate multiple LOB coverage "NVOCC,IAC,MTC,FFLL,FFE&O,WHLL"
    And validate receipts values for CL class
    And validate rate is approximately 0.184

  Scenario: Calculate premium with geographical area percentages
    Given I have submission payload with geographical area percentages
    When I send POST request to calculate premium endpoint
    Then the status code should be 200
    And the response should contain premium calculation details
    And validate geographical area percentages
    And validate premium summary breakdown
    And validate cargo liability premium 28470
    And validate warehouse legal liability premium 4608
    And validate total premium 33078
