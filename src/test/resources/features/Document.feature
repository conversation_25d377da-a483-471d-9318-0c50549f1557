@documentService @beSmokeTest @beRegression
Feature:Test Document Service

  Scenario Outline: Fetch Submissions and core system folders
    When POST "/core/policy-service/policies/filter?pageNumber=<pageNumber>&pageSize=<pageSize>" to save submissions ID
 """
  {"productTypes":[<productTypes>],"statuses":[<statuses>],"underwriters":[<underwriters>]}
"""
    Then the status code should be 200
    And the response time is less then <3000>
    When Get folders list "/data-lineage/api/v1/submission/:submissionID/folders?showFileDetails=true"
    Then the status code should be 200
    And the response time is less then <3000>
    And the response should contain "message" as "Request Processed Successfully"
    And the total number of items should be 50
    And the response should have a folder named "Claims"
    And the folder "Claims" should have a subfolder named "ASU Loss Runs"
    And the folder "Accounting" should have a document count of 4
    Examples:
      | productTypes |statuses|underwriters|pageNumber|pageSize|
      |       |        ||0            |1      |

  Scenario: Create Folder under Folder
    When Create folder "/data-lineage/api/v1/submission/folder"
    Then the status code should be 200
    And the response time is less then <3000>


  Scenario Outline: Upload files under Folder
    When Get files list "data-lineage/api/v1/submission?:folderID"
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes |statuses|underwriters|pageNumber|pageSize|
      |       |        |"backend"|0            |1      |


  Scenario Outline: Fetch files under Folder
    When Get files list "data-lineage/api/v1/submission?:folderID"
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes |statuses|underwriters|pageNumber|pageSize|
      |       |        |"backend"|0            |1      |

  Scenario: Download original submission files
    When download original submissions file "/core/document-service/documents/download?files=:documentID&folders="
    Then the status code should be 200
    And the response time is less then <3000>
