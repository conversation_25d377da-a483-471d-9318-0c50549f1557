@beSmokeTest @broker @beRegression
Feature: Broker Test

  Scenario: Test Create Broker With Valid Data
    Given the user send POST request "/core/brokerage-service/brokerage" to create Brokerage
    And the status code should be 201
    And save the brokerage id
    And the user send POST request "/core/brokerage-service/office" to create Office
    And the status code should be 201
    And save office ID
    When the user send POST request "/core/brokerage-service/broker" to create <PERSON>roke<PERSON>
    Then the status code should be 201
    And save the broker id
    And the response time is less then <3000>

  Scenario: Test Update Broker
    Given user request authentication
    When the user send POST request "/core/brokerage-service/broker" to update <PERSON>roker
    Then the status code should be 201
    And the response time is less then <3000>

  Scenario: Test Search Broker
    Given user request authentication
    When the user send POST request "/core/brokerage-service/broker/search?pageNumber=0&pageSize=30" to search Broker
    Then the status code should be 200
    And the response time is less then <3000>

  Scenario: Test delete Broker
    When the user send POST request "/core/brokerage-service/office" to delete Office
    Then the status code should be 201
    And the response time is less then <3000>
    And user request authentication
    And the user send POST request "/core/brokerage-service/brokerage" to delete Brokerage
    And the status code should be 204