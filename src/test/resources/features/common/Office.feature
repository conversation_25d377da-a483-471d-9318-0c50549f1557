@beSmokeTest @office @beRegression
Feature: Office Test

  Scenario: Test Create Office With Valid Data
    Given the user send POST request "/core/brokerage-service/brokerage" to create Brokerage
    Then the status code should be 201
    And save the brokerage id
    When the user send POST request "/core/brokerage-service/office" to create Office
    Then the status code should be 201
    And save office ID
    And the response time is less then <3000>

  Scenario: Test Update Office With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/office" to update Office
    Then the status code should be 201
    And the response time is less then <3000>
  @beSmokeTest1
  Scenario: Test Search Office With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/office" to search Office
    Then the status code should be 201
    And the response time is less then <3000>

  Scenario: Test delete Office With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/office" to delete Office
    Then the status code should be 204
    And the response time is less then <3000>
    And user request authentication
    And the user send POST request "/core/brokerage-service/brokerage" to delete Brokerage
    And the status code should be 204