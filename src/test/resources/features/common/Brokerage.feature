@beSmokeTest @brokerage @beRegression
Feature: Brokerage Test

  Scenario: Test Create Brokerage With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/brokerage" to create Brokerage
    Then the status code should be 201
    And save the brokerage id
    And the response time is less then <3000>

  Scenario: Test Update Brokerage With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/brokerage" to update Brokerage
    Then the status code should be 201
    And the response time is less then <3000>

  @beSmokeTest1
  Scenario: Test Search Brokerage With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/broker/search?pageNumber=0&pageSize=30" to search Brokerage
    Then the status code should be 200
    And the response time is less then <3000>

  Scenario: Test Delete Brokerage With Valid Data
    Given user request authentication
    When the user send POST request "/core/brokerage-service/brokerage" to delete Brokerage
    Then the status code should be 204
    And the response time is less then <3000>