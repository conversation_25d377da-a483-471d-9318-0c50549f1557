@beSmokeTest @insuredTest @beRegression
Feature: Insured Test

  Scenario: Test Create Insured
    Given user request authentication
    When the user send POST request "/core/insured-service/insured" to create Insured
    Then the status code should be 201
    And save the Insured id
    And the response time is less then <3000>

  Scenario: Test Update Insured
    Given user request authentication
    When the user send POST request "/core/insured-service/insured" to update Insured
    Then the status code should be 201
    And the response time is less then <3000>

  Scenario: Test Delete Insured
    Given user request authentication
    When the user send POST request "/core/insured-service/insured/" to delete Insured
    Then the status code should be 204
    And the response time is less then <3000>

  Scenario: Test Search Insured
    Given user request authentication
    When the user send POST request "/core/insured-service/insured/search?pageNumber=0&pageSize=20" to search Insured
    Then the status code should be 200
    And the response time is less then <3000>

