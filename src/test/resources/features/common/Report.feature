@beSmokeTest @reports @beRegression
Feature: Generate Reports

  Scenario Outline: Test <reportType> reports download
    When the user send POST request "/core/reporting-service/reports/requests" to generate Report
    """
    {"startDate": "2023-03-01", "endDate": "2024-03-27", "reportingType": "<reportType>"}
    """
    Then the status code should be 201
    Examples:
      | reportType      |
      | BORDEREAU       |
      | ASU_AGING       |
      | PAYABLE_CARRIER |
      | INVOICE_DETAIL  |
      | ENDORSEMENT_LOG |



