@policy @beSmokeTest @beRegression
Feature: Policy Service

  Scenario Outline: Fetch Submissions and policy data by submissions id
    When POST "/core/policy-service/policies/filter?pageNumber=<pageNumber>&pageSize=<pageSize>" to save submissions ID
 """
  {"productTypes":[<productTypes>],"statuses":[<statuses>],"underwriters":[<underwriters>]}
"""
    Then the status code should be 200
    And the response time is less then <3000>
    When Get policy data "/core/policy-service/submissions/:submissionsID?fetchSubmission=false"
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes |statuses|underwriters|pageNumber|pageSize|
      |       |        |""|0            |1      |


  Scenario Outline: Search Policy By Filters
    When POST "/core/policy-service/policies/filter?pageNumber=<pageNumber>&pageSize=<pageSize>"
 """
  {"productTypes":[<productTypes>],"statuses":[<statuses>],"underwriters":[<underwriters>]}
"""
    Then the status code should be 200
    And the response time is less then <3000>
    Examples:
      | productTypes |statuses|underwriters|pageNumber|pageSize|
      |"XCAN"        |        |"backend"|0            |10      |
      |"XCAN","PCAN","EIPS"        | "CANCELED", "EXPIRED", "IN_FORCE", "ENDORSEMENT"       |"backend"|0            |10      |
      |"XCAN","PCAN","EIPS"        | "CANCELED", "EXPIRED", "IN_FORCE", "ENDORSEMENT"       ||0            |10      |

  @endtoend
  Scenario Outline: End to End Policy creation flow from submissions
    Given I have the submission payload "<productCode>" "<name>" "<type>"
    When assign submission
    And move submission to Quote
    And validate the folder is synced in quote
    And create policy with in force status
    And validate the folder is synced in infoce and invoice is created
    And create endorsement
    And validate policy status after endorsement
#    Then the status code should be 200
#    And the response time is less then <3000>
    Examples:
      | productCode | name                                                | type    |
      | EIAN        | Excess Liability - Infrastructure Annual            | Excess  |
#      | EIPS        | Excess Liability - Infrastructure Project Specific  | Excess  |
#      | EIWN        | Excess Liability - Infrastructure Wrap              | Excess  |
#      | NPX         | Excess Liability - Public Entity                    | Excess  |
#      | PANN        | Primary Liability - Annual Habitational             | Primary |
#      | PCAN        | Primary Liability - Annual Construction             | Primary |
#      | PIAN        | Primary Liability - Infrastructure Annual           | Primary |
#      | PIPS        | Primary Liability - Infrastructure Project Specific | Primary |
#      | PIWU        | Primary Liability - Infrastructure Wrap             | Primary |
#      | PNAG        | Primary Liability - NY Annual Contractors           | Primary |
#      | PNPS        | Primary Liability - NY Project Specific             | Primary |
#      | PNWU        | Primary Liability - NY Wrap                         | Primary |
#      | POAN        | Primary Liability - Annual Other                    | Primary |
#      | POWI        | Primary Liability - Owners Interest                 | Primary |
#      | PPRS        | Primary Liability - Project Specific                | Primary |
#      | PRPS        | Primary Liability - Rolling Project Specific        | Primary |
#      | PRWU        | Primary Liability - Rolling Wrap                    | Primary |
#      | PWRP        | Primary Liability - Wrap Residential                | Primary |
#      | PWUP        | Primary Liability - Wrap                            | Primary |
#      | XANN        | Excess Liability - Annual Habitational              | Excess  |
#      | XAUT        | Excess Liability - Auto                             | Excess  |
#      | XCAN        | Excess Liability - Annual Construction              | Excess  |
#      | XNAG        | Excess Liability - NY Annual Construction           | Excess  |
#      | XNPS        | Excess Liability - NY Project Specific              | Excess  |
#      | XNWU        | Excess Liability - NY Wrap                          | Excess  |
#      | XOAN        | Excess Liability - Annual Other                     | Excess  |
#      | XOWI        | Excess Liability - Owners Interest                  | Excess  |
#      | XPRS        | Excess Liability - Project Specific                 | Excess  |
#      | XRPS        | Excess Liability - Rolling Project Specific         | Excess  |
#      | XRWU        | Excess Liability - Rolling Wrap                     | Excess  |
#      | XWRP        | Excess Liability - Wrap Residential                 | Excess  |
#      | XWUP        | Excess Liability - Wrap                             | Excess  |

