#!/usr/bin/env python3
"""
Slack notification script for QA automation test results
"""

import json
import os
import sys
import requests
from datetime import datetime
import argparse

class SlackNotifier:
    def __init__(self, webhook_url):
        self.webhook_url = webhook_url
        
    def parse_cucumber_results(self, json_file):
        """Parse Cucumber JSON results and extract statistics"""
        try:
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            total_scenarios = 0
            passed_scenarios = 0
            failed_scenarios = 0
            skipped_scenarios = 0
            total_features = len(data)
            passed_features = 0
            failed_features = 0
            
            feature_details = []
            
            for feature in data:
                feature_name = feature.get('name', 'Unknown Feature')
                feature_passed = True
                feature_scenarios = {'total': 0, 'passed': 0, 'failed': 0, 'skipped': 0}
                
                for element in feature.get('elements', []):
                    if element.get('type') == 'scenario':
                        total_scenarios += 1
                        feature_scenarios['total'] += 1
                        scenario_status = 'passed'
                        
                        for step in element.get('steps', []):
                            step_result = step.get('result', {})
                            if step_result.get('status') == 'failed':
                                scenario_status = 'failed'
                                feature_passed = False
                                break
                            elif step_result.get('status') == 'skipped':
                                if scenario_status != 'failed':
                                    scenario_status = 'skipped'
                        
                        if scenario_status == 'passed':
                            passed_scenarios += 1
                            feature_scenarios['passed'] += 1
                        elif scenario_status == 'failed':
                            failed_scenarios += 1
                            feature_scenarios['failed'] += 1
                        else:
                            skipped_scenarios += 1
                            feature_scenarios['skipped'] += 1
                
                if feature_passed and feature_scenarios['total'] > 0:
                    passed_features += 1
                elif feature_scenarios['failed'] > 0:
                    failed_features += 1
                    
                feature_details.append({
                    'name': feature_name,
                    'scenarios': feature_scenarios,
                    'passed': feature_passed
                })
            
            pass_percentage = round((passed_scenarios / total_scenarios * 100), 2) if total_scenarios > 0 else 0
            feature_pass_percentage = round((passed_features / total_features * 100), 2) if total_features > 0 else 0
            
            return {
                'total_scenarios': total_scenarios,
                'passed_scenarios': passed_scenarios,
                'failed_scenarios': failed_scenarios,
                'skipped_scenarios': skipped_scenarios,
                'total_features': total_features,
                'passed_features': passed_features,
                'failed_features': failed_features,
                'pass_percentage': pass_percentage,
                'feature_pass_percentage': feature_pass_percentage,
                'feature_details': feature_details
            }
            
        except Exception as e:
            print(f"Error parsing Cucumber results: {e}")
            return None
    
    def get_status_color(self, pass_percentage):
        """Get color based on pass percentage"""
        if pass_percentage >= 90:
            return "good"  # Green
        elif pass_percentage >= 70:
            return "warning"  # Yellow
        else:
            return "danger"  # Red
    
    def get_status_emoji(self, pass_percentage):
        """Get emoji based on pass percentage"""
        if pass_percentage >= 90:
            return "✅"
        elif pass_percentage >= 70:
            return "⚠️"
        else:
            return "❌"
    
    def create_feature_summary(self, feature_details):
        """Create a summary of feature results"""
        if not feature_details:
            return "No feature details available"
        
        summary_lines = []
        for feature in feature_details[:10]:  # Limit to first 10 features
            name = feature['name'][:50] + "..." if len(feature['name']) > 50 else feature['name']
            scenarios = feature['scenarios']
            status_emoji = "✅" if feature['passed'] else "❌"
            summary_lines.append(
                f"{status_emoji} *{name}*: {scenarios['passed']}/{scenarios['total']} scenarios"
            )
        
        if len(feature_details) > 10:
            summary_lines.append(f"... and {len(feature_details) - 10} more features")
        
        return "\n".join(summary_lines)
    
    def send_notification(self, results, environment, branch, build_number, test_suite, report_url=None, github_url=None):
        """Send Slack notification with test results"""
        
        if not results:
            # Send error notification
            payload = {
                "text": "❌ QA Automation Test Results - Error",
                "attachments": [{
                    "color": "danger",
                    "text": "Failed to parse test results. Please check the logs."
                }]
            }
        else:
            status_emoji = self.get_status_emoji(results['pass_percentage'])
            color = self.get_status_color(results['pass_percentage'])
            
            # Create main message
            blocks = [
                {
                    "type": "header",
                    "text": {
                        "type": "plain_text",
                        "text": f"{status_emoji} QA Automation Results - {test_suite.title()} Suite"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Environment:* {environment}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Branch:* {branch}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Build:* #{build_number}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Executed:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC"
                        }
                    ]
                },
                {
                    "type": "divider"
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": "*📊 Test Summary*"
                    }
                },
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Features:* {results['passed_features']}/{results['total_features']} ({results['feature_pass_percentage']}%)"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Scenarios:* {results['passed_scenarios']}/{results['total_scenarios']} ({results['pass_percentage']}%)"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*✅ Passed:* {results['passed_scenarios']}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*❌ Failed:* {results['failed_scenarios']}"
                        }
                    ]
                }
            ]
            
            # Add feature details if available
            if results['feature_details']:
                feature_summary = self.create_feature_summary(results['feature_details'])
                blocks.append({
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*📋 Feature Results*\n{feature_summary}"
                    }
                })
            
            # Add action buttons
            actions = []
            if report_url:
                actions.append({
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "📋 View HTML Report"
                    },
                    "url": report_url
                })
            
            if github_url:
                actions.append({
                    "type": "button",
                    "text": {
                        "type": "plain_text",
                        "text": "🔗 GitHub Actions"
                    },
                    "url": github_url
                })
            
            if actions:
                blocks.append({
                    "type": "actions",
                    "elements": actions
                })
            
            payload = {
                "text": f"QA Automation Test Results - {results['pass_percentage']}% Pass Rate",
                "attachments": [{
                    "color": color,
                    "blocks": blocks
                }]
            }
        
        # Send to Slack
        try:
            response = requests.post(self.webhook_url, json=payload)
            response.raise_for_status()
            print("Slack notification sent successfully!")
            return True
        except Exception as e:
            print(f"Failed to send Slack notification: {e}")
            return False

def main():
    parser = argparse.ArgumentParser(description='Send QA test results to Slack')
    parser.add_argument('--webhook-url', required=True, help='Slack webhook URL')
    parser.add_argument('--results-file', default='target/cucumber.json', help='Cucumber JSON results file')
    parser.add_argument('--environment', default='LOGISTICS-DEV', help='Test environment')
    parser.add_argument('--branch', default='main', help='Git branch')
    parser.add_argument('--build-number', default='1', help='Build number')
    parser.add_argument('--test-suite', default='all', help='Test suite name')
    parser.add_argument('--report-url', help='URL to HTML report')
    parser.add_argument('--github-url', help='URL to GitHub Actions run')
    
    args = parser.parse_args()
    
    # Initialize notifier
    notifier = SlackNotifier(args.webhook_url)
    
    # Parse results
    results = notifier.parse_cucumber_results(args.results_file)
    
    # Send notification
    success = notifier.send_notification(
        results=results,
        environment=args.environment,
        branch=args.branch,
        build_number=args.build_number,
        test_suite=args.test_suite,
        report_url=args.report_url,
        github_url=args.github_url
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
