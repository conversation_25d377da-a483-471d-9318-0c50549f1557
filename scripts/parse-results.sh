#!/bin/bash

# QA Automation Test Results Parser
# This script parses Cucumber JSON results and generates summary statistics

set -e

# Default values
RESULTS_FILE="target/cucumber.json"
OUTPUT_FILE="target/test-summary.json"
ENVIRONMENT="${TIER:-LOGISTICS-DEV}"
BRANCH_NAME="${GITHUB_REF_NAME:-main}"
BUILD_NUMBER="${GITHUB_RUN_NUMBER:-1}"
TEST_SUITE="${TEST_SUITE:-all}"

# Function to display usage
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -f, --file FILE          Cucumber JSON results file (default: target/cucumber.json)"
    echo "  -o, --output FILE        Output summary file (default: target/test-summary.json)"
    echo "  -e, --environment ENV    Test environment (default: LOGISTICS-DEV)"
    echo "  -b, --branch BRANCH      Git branch name (default: main)"
    echo "  -n, --build-number NUM   Build number (default: 1)"
    echo "  -s, --suite SUITE        Test suite name (default: all)"
    echo "  -h, --help               Display this help message"
    exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -f|--file)
            RESULTS_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -e|--environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -b|--branch)
            BRANCH_NAME="$2"
            shift 2
            ;;
        -n|--build-number)
            BUILD_NUMBER="$2"
            shift 2
            ;;
        -s|--suite)
            TEST_SUITE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "Unknown option: $1"
            usage
            ;;
    esac
done

# Check if results file exists
if [[ ! -f "$RESULTS_FILE" ]]; then
    echo "Error: Results file '$RESULTS_FILE' not found!"
    exit 1
fi

echo "Parsing test results from: $RESULTS_FILE"
echo "Environment: $ENVIRONMENT"
echo "Branch: $BRANCH_NAME"
echo "Build: $BUILD_NUMBER"
echo "Test Suite: $TEST_SUITE"

# Parse Cucumber JSON results using Python
python3 << EOF
import json
import sys
from datetime import datetime

def parse_cucumber_results(json_file):
    try:
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        total_scenarios = 0
        passed_scenarios = 0
        failed_scenarios = 0
        skipped_scenarios = 0
        total_features = len(data)
        passed_features = 0
        failed_features = 0
        
        feature_results = []
        failed_scenarios_details = []
        
        for feature in data:
            feature_name = feature.get('name', 'Unknown Feature')
            feature_uri = feature.get('uri', '')
            feature_passed = True
            feature_scenarios = {'total': 0, 'passed': 0, 'failed': 0, 'skipped': 0}
            
            for element in feature.get('elements', []):
                if element.get('type') == 'scenario':
                    scenario_name = element.get('name', 'Unknown Scenario')
                    total_scenarios += 1
                    feature_scenarios['total'] += 1
                    scenario_status = 'passed'
                    failed_step = None
                    
                    for step in element.get('steps', []):
                        step_result = step.get('result', {})
                        if step_result.get('status') == 'failed':
                            scenario_status = 'failed'
                            feature_passed = False
                            failed_step = {
                                'name': step.get('name', ''),
                                'error': step_result.get('error_message', 'No error message')
                            }
                            break
                        elif step_result.get('status') == 'skipped':
                            if scenario_status != 'failed':
                                scenario_status = 'skipped'
                    
                    if scenario_status == 'passed':
                        passed_scenarios += 1
                        feature_scenarios['passed'] += 1
                    elif scenario_status == 'failed':
                        failed_scenarios += 1
                        feature_scenarios['failed'] += 1
                        failed_scenarios_details.append({
                            'feature': feature_name,
                            'scenario': scenario_name,
                            'failed_step': failed_step
                        })
                    else:
                        skipped_scenarios += 1
                        feature_scenarios['skipped'] += 1
            
            if feature_passed and feature_scenarios['total'] > 0:
                passed_features += 1
            elif feature_scenarios['failed'] > 0:
                failed_features += 1
            
            feature_results.append({
                'name': feature_name,
                'uri': feature_uri,
                'scenarios': feature_scenarios,
                'passed': feature_passed
            })
        
        pass_percentage = round((passed_scenarios / total_scenarios * 100), 2) if total_scenarios > 0 else 0
        feature_pass_percentage = round((passed_features / total_features * 100), 2) if total_features > 0 else 0
        
        return {
            'summary': {
                'total_scenarios': total_scenarios,
                'passed_scenarios': passed_scenarios,
                'failed_scenarios': failed_scenarios,
                'skipped_scenarios': skipped_scenarios,
                'total_features': total_features,
                'passed_features': passed_features,
                'failed_features': failed_features,
                'pass_percentage': pass_percentage,
                'feature_pass_percentage': feature_pass_percentage
            },
            'features': feature_results,
            'failed_scenarios': failed_scenarios_details,
            'metadata': {
                'environment': '$ENVIRONMENT',
                'branch': '$BRANCH_NAME',
                'build_number': '$BUILD_NUMBER',
                'test_suite': '$TEST_SUITE',
                'execution_time': datetime.now().isoformat(),
                'results_file': '$RESULTS_FILE'
            }
        }
        
    except Exception as e:
        print(f"Error parsing results: {e}", file=sys.stderr)
        return None

# Parse results
results = parse_cucumber_results('$RESULTS_FILE')

if results:
    # Write summary to output file
    with open('$OUTPUT_FILE', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print summary to console
    summary = results['summary']
    print(f"\n📊 Test Execution Summary")
    print(f"{'='*50}")
    print(f"Environment: {results['metadata']['environment']}")
    print(f"Branch: {results['metadata']['branch']}")
    print(f"Build: {results['metadata']['build_number']}")
    print(f"Test Suite: {results['metadata']['test_suite']}")
    print(f"Execution Time: {results['metadata']['execution_time']}")
    print(f"\n📋 Results:")
    print(f"Features: {summary['passed_features']}/{summary['total_features']} ({summary['feature_pass_percentage']}%)")
    print(f"Scenarios: {summary['passed_scenarios']}/{summary['total_scenarios']} ({summary['pass_percentage']}%)")
    print(f"✅ Passed: {summary['passed_scenarios']}")
    print(f"❌ Failed: {summary['failed_scenarios']}")
    print(f"⏭️ Skipped: {summary['skipped_scenarios']}")
    
    # Print failed scenarios if any
    if results['failed_scenarios']:
        print(f"\n❌ Failed Scenarios:")
        print(f"{'='*50}")
        for failed in results['failed_scenarios'][:5]:  # Show first 5 failures
            print(f"Feature: {failed['feature']}")
            print(f"Scenario: {failed['scenario']}")
            if failed['failed_step']:
                print(f"Failed Step: {failed['failed_step']['name']}")
            print(f"{'─'*30}")
        
        if len(results['failed_scenarios']) > 5:
            print(f"... and {len(results['failed_scenarios']) - 5} more failures")
    
    print(f"\n📄 Detailed results saved to: $OUTPUT_FILE")
    
    # Set exit code based on test results
    if summary['failed_scenarios'] > 0:
        print(f"\n⚠️ Tests completed with failures")
        sys.exit(1)
    else:
        print(f"\n✅ All tests passed!")
        sys.exit(0)
else:
    print("Failed to parse test results", file=sys.stderr)
    sys.exit(1)
EOF

echo "Results parsing completed!"
