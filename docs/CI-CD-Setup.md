# CI/CD Pipeline Setup for QA Automation

This document provides step-by-step instructions to set up the CI/CD pipeline with Slack integration for QA automation test results.

## 🚀 Quick Setup

### 1. GitHub Repository Setup

1. **Enable GitHub Pages** (for hosting HTML reports):
   - Go to repository Settings → Pages
   - Source: GitHub Actions
   - This will host reports at: `https://[username].github.io/[repository]/reports/[build-number]/`

2. **Add Repository Secrets**:
   - Go to repository Settings → Secrets and variables → Actions
   - Add the following secrets:

   ```
   SLACK_WEBHOOK_URL: https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
   ```

### 2. Slack Webhook Setup

1. **Create Slack App**:
   - Go to https://api.slack.com/apps
   - Click "Create New App" → "From scratch"
   - Choose app name and workspace

2. **Enable Incoming Webhooks**:
   - Go to "Incoming Webhooks" in your app settings
   - Turn on "Activate Incoming Webhooks"
   - Click "Add New Webhook to Workspace"
   - Choose the channel for notifications
   - Copy the webhook URL

3. **Add Webhook URL to GitHub Secrets**:
   - Use the copied webhook URL as `SLACK_WEBHOOK_URL` secret

### 3. Pipeline Configuration

The pipeline supports multiple trigger methods:

#### Automatic Triggers:
- **Push to main/develop**: Runs full test suite
- **Pull Request**: Runs regression tests
- **Scheduled**: Daily at 6 AM UTC

#### Manual Triggers:
- **Workflow Dispatch**: Choose test suite and environment

## 📊 Test Suites Available

| Suite | Description | Tags |
|-------|-------------|------|
| `all` | All logistics tests | `@Logistics` |
| `smoke` | Critical path tests | `@smoke` |
| `regression` | Full regression suite | `@regression` |
| `calculatePremium` | Premium calculation tests | `@calculatePremium` |
| `policySearch` | Policy search tests | `@policySearch` |
| `detailedValidation` | Detailed validation tests | `@detailedValidation` |

## 🌍 Environments

- `LOGISTICS-DEV`: Development environment
- `LOGISTICS-TEST`: Test environment  
- `LOGISTICS-STAGING`: Staging environment

## 📋 Running Tests

### Via GitHub Actions UI:
1. Go to Actions tab in your repository
2. Select "QA Automation Tests" workflow
3. Click "Run workflow"
4. Choose test suite and environment
5. Click "Run workflow"

### Via Git Push:
```bash
git add .
git commit -m "feat: add new test scenarios"
git push origin main  # Triggers full test suite
```

### Via Pull Request:
```bash
git checkout -b feature/new-tests
git add .
git commit -m "feat: add new test scenarios"
git push origin feature/new-tests
# Create PR - triggers regression tests
```

## 📈 Reports and Notifications

### Slack Notifications Include:
- ✅ **Test Summary**: Pass/fail counts and percentages
- 📊 **Feature Breakdown**: Individual feature results
- 🔗 **Quick Links**: Direct links to HTML reports and GitHub Actions
- 🎯 **Environment Info**: Branch, build number, environment
- 🎨 **Color Coding**: Green (>90%), Yellow (70-90%), Red (<70%)

### HTML Reports:
- **Location**: `https://[username].github.io/[repository]/reports/[build-number]/`
- **Features**: Interactive charts, detailed scenario results, trends
- **Retention**: 30 days for artifacts, permanent for GitHub Pages

### Report Contents:
- Executive summary with pass/fail statistics
- Feature-wise breakdown
- Scenario details with step-by-step results
- Error messages and stack traces for failures
- Execution timeline and duration
- Environment and build information

## 🛠️ Local Testing

### Run Specific Test Suite:
```bash
# Calculate Premium tests
mvn clean test -Dtier=LOGISTICS-DEV -Dtest=CalculatePremiumTestRunner

# Policy Search tests  
mvn clean test -Dtier=LOGISTICS-DEV -Dtest=PolicySearchTestRunner

# All tests with tags
mvn clean test -Dtier=LOGISTICS-DEV -Dcucumber.filter.tags="@Logistics"
```

### Generate Local Reports:
```bash
# Run tests and generate reports
mvn clean test -Dtier=LOGISTICS-DEV
mvn verify  # Generates Cucumber reports

# Parse results locally
chmod +x scripts/parse-results.sh
./scripts/parse-results.sh -f target/cucumber.json -e LOGISTICS-DEV
```

### Send Test Results to Slack (Local):
```bash
# Install dependencies
pip3 install requests

# Send notification
python3 scripts/slack-notification.py \
  --webhook-url "YOUR_SLACK_WEBHOOK_URL" \
  --results-file target/cucumber.json \
  --environment LOGISTICS-DEV \
  --branch main \
  --build-number 1 \
  --test-suite all
```

## 🔧 Customization

### Modify Test Execution:
Edit `.github/workflows/qa-automation.yml`:
- Change test execution commands
- Add new test suites
- Modify environments
- Update notification format

### Customize Slack Messages:
Edit `scripts/slack-notification.py`:
- Modify message format
- Add custom fields
- Change color thresholds
- Add additional buttons/links

### Add New Environments:
1. Update workflow file with new environment option
2. Add environment-specific configuration
3. Update documentation

## 🐛 Troubleshooting

### Common Issues:

1. **Slack notifications not working**:
   - Check webhook URL is correct
   - Verify webhook URL is added to GitHub secrets
   - Check Slack app permissions

2. **Reports not generating**:
   - Ensure GitHub Pages is enabled
   - Check if cucumber.json file is created
   - Verify Maven configuration

3. **Tests failing in pipeline but passing locally**:
   - Check environment variables
   - Verify authentication tokens
   - Review network connectivity

### Debug Commands:
```bash
# Check if JSON results exist
ls -la target/cucumber.json

# Validate JSON format
python3 -m json.tool target/cucumber.json

# Check GitHub Actions logs
# Go to Actions tab → Select run → View logs
```

## 📞 Support

For issues with the CI/CD pipeline:
1. Check GitHub Actions logs
2. Review Slack webhook configuration  
3. Validate test execution locally
4. Check environment-specific settings

## 🔄 Pipeline Flow

```
1. Trigger (Push/PR/Schedule/Manual)
   ↓
2. Setup Environment (Java, Maven, Cache)
   ↓  
3. Run Tests (Based on suite selection)
   ↓
4. Generate Reports (HTML + JSON)
   ↓
5. Parse Results (Extract statistics)
   ↓
6. Upload Artifacts (Reports + Logs)
   ↓
7. Deploy to GitHub Pages (HTML reports)
   ↓
8. Send Slack Notification (Results summary)
```

This setup provides comprehensive test execution, reporting, and notification capabilities for your QA automation framework.
