# Policy Detailed Validation Framework

This document describes the comprehensive validation framework for detailed policy response structures based on the provided JSON response format.

## Overview

The Policy Detailed Validation framework provides extensive validation capabilities for complex policy responses that contain comprehensive insurance policy information including insured details, premium calculations, geographical scope, commodity types, terms and conditions, and more.

## Response Structure

The framework validates responses with the following top-level structure:

```json
{
  "data": { /* Comprehensive policy data */ },
  "success": true,
  "message": "Request Processed Successfully",
  "timestamp": "2025-06-09T11:29:00+0000"
}
```

## Validation Categories

### 1. Response Structure Validation
- **Top-level fields**: `data`, `success`, `message`, `timestamp`
- **Success indicators**: Validates `success` is true and message indicates success
- **Timestamp format**: Validates proper timestamp format

### 2. Core Policy Information
- **Identifiers**: `submissionId`, `clientId`, `proposalNumber`
- **Status and Type**: `status`, `businessType`, `class`, `lob`
- **Dates**: `createdAt`, `updatedAt`, `proposalDate`
- **Personnel**: `underwriterName`, `createdBy`, `updatedBy`

### 3. Insured Information Details
- **Basic Info**: `businessName`, `insuredId`, `insuredClientId`
- **Address**: `address`, `city`, `state`, `zipcode`
- **Company Details**: `companyName`, `companyAddress`
- **Policy Dates**: `effectiveDate`, `expiryDate`
- **Business Details**: `noOfEmployees`, `riskManager`, `yearsOnExperience`

### 4. Premium and Rating Information
- **Core Values**: `premium`, `rate`
- **Premium Summary**: All premium breakdown fields
- **Calculations**: Premium calculation components
- **Loss History**: Historical loss data and adjustments

### 5. Commodity Types
- **Structure**: `label`, `key`, `percentage` for each commodity
- **Completeness**: Validates all commodity types are present
- **Data Integrity**: Ensures required fields are populated

### 6. Geographical Information
- **Geographical Areas**: List of coverage areas with labels and keys
- **Geographical Scope**: Detailed coverage statements and exclusions
- **Worldwide Coverage**: Validates global coverage statements
- **Exclusions**: Validates excluded territories and sanctions compliance

### 7. Conveyance Information
- **Transport Types**: `ocean`, `aircraft`, `domesticTruckOrRail`, `internationTruckOrRail`
- **Configuration**: Validates enabled flags for each transport type
- **Structure**: Ensures proper nested object structure

### 8. Terms and Conditions
- **Class-based Terms**: Separate terms for `SI`, `CL`, `general`, etc.
- **Term Structure**: Each term has `value` and `id`
- **Completeness**: Validates all required term categories

### 9. Additional Information
- **Additional Insured**: List of additional insured parties
- **Broker Information**: Broker contact and record details
- **Valuation**: Policy valuation statements and rules
- **Loss History**: Comprehensive loss history data

## Test Scenarios

### Comprehensive Validation
```gherkin
Scenario: Validate comprehensive policy response structure
  Given I have a valid policy submission payload
  When I send POST request to create policy submission
  Then the status code should be 200
  And validate detailed policy response structure
  And validate policy core information
  And validate insured information details
  And validate premium and rating information
```

### Specific Component Validation
```gherkin
Scenario: Validate policy commodity and geographical information
  Given I have a valid policy submission payload
  When I send POST request to create policy submission
  Then the status code should be 200
  And validate commodity types information
  And validate geographical information
  And validate conveyance information
```

### Business Logic Validation
```gherkin
Scenario: Validate policy premium calculations
  Given I have a valid policy submission payload
  When I send POST request to create policy submission
  Then the status code should be 200
  And the premium should be greater than zero
  And the rate should be greater than zero
  And validate premium summary contains all required fields
```

## Key Validation Methods

### Core Structure Validation
- `validate_detailed_policy_response_structure()`: Top-level response validation
- `validate_policy_core_information()`: Essential policy fields
- `validate_insured_information_details()`: Comprehensive insured data

### Financial Validation
- `validate_premium_and_rating_information()`: Premium and rate validation
- `premium_should_be_greater_than_zero()`: Business rule validation
- `validate_premium_summary_contains_all_required_fields()`: Complete premium breakdown

### Business Data Validation
- `validate_commodity_types_information()`: Commodity structure and data
- `validate_geographical_information()`: Coverage areas and scope
- `validate_conveyance_information()`: Transport method configuration

### Compliance Validation
- `validate_terms_and_conditions_structure()`: Policy terms organization
- `geographical_scope_should_contain_exclusions()`: Sanctions compliance
- `all_conveyance_types_should_be_properly_configured()`: Transport compliance

## POJO Classes

### Main Response Classes
- `PolicySubmissionDetailedResponse`: Top-level response wrapper
- `PolicyData`: Main policy data container
- `DetailedInsuredInfo`: Comprehensive insured information

### Supporting Classes
- `GeographicalScope`: Coverage area definitions and exclusions
- `GoodsInsured`: Insured goods descriptions
- `TermsCondition`: Policy terms and conditions
- `Valuation`: Policy valuation rules

## Running Tests

### Run All Detailed Validation Tests
```bash
mvn test -Dtest=PolicyDetailedValidationTestRunner
```

### Run Specific Validation Categories
```bash
mvn test -Dcucumber.filter.tags="@detailedValidation"
```

### Run with Environment Configuration
```bash
mvn test -Dtier=LOGISTICS-DEV -Dtest=PolicyDetailedValidationTestRunner
```

## Validation Benefits

### Comprehensive Coverage
- Validates all aspects of complex policy responses
- Ensures data integrity across all policy components
- Verifies business rule compliance

### Maintainable Structure
- Modular validation methods for specific components
- Reusable validation logic across different scenarios
- Clear separation of concerns

### Business Rule Enforcement
- Premium calculation validation
- Geographical compliance checking
- Terms and conditions completeness
- Audit trail validation

## Best Practices

### Test Organization
1. **Group Related Validations**: Combine related validations in single scenarios
2. **Use Descriptive Names**: Clear method and scenario names
3. **Validate Business Rules**: Include business logic validation, not just structure

### Data Validation
1. **Check Null Values**: Always validate required fields are not null
2. **Validate Formats**: Check date formats, number formats, ID patterns
3. **Business Logic**: Validate premiums > 0, valid dates, proper relationships

### Error Handling
1. **Meaningful Messages**: Provide clear assertion messages
2. **Context Information**: Include actual values in error messages
3. **Granular Validation**: Break down complex validations into specific checks

## Troubleshooting

### Common Issues
1. **Missing Fields**: Check if API response structure has changed
2. **Null Values**: Verify test data includes all required fields
3. **Format Changes**: Update validation patterns if response formats change

### Debug Tips
- Use detailed logging to trace validation steps
- Check individual validation methods in isolation
- Verify test data matches expected response structure
- Compare actual vs expected response formats

This framework provides comprehensive validation for complex policy responses, ensuring data integrity, business rule compliance, and structural correctness across all policy components.
