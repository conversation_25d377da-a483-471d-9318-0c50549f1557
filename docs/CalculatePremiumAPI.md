# Calculate Premium API Automation

This document describes the automation framework for testing the Calculate Premium API endpoint.

## Overview

The Calculate Premium API (`/rater/quote-service/rate/calculate-premium`) is used to calculate insurance premiums based on submission data including LOB (Line of Business), class types, and various risk factors.

## Test Structure

### Feature File
- **Location**: `src/test/resources/features/logistics/CalculateRatesandPremium.feature`
- **Tags**: `@Logistics @calculatePremium`

### Step Definitions
- **Location**: `src/test/java/steps/logistics/CalculatePremiumSteps.java`
- **Helper Class**: `src/main/java/helpers/CalculatePremiumHelper.java`

### POJO Classes
- `src/main/java/pojo/logistics/CalculatePremiumRequest.java` - Main request object
- `src/main/java/pojo/logistics/CommodityType.java` - Commodity type data
- `src/main/java/pojo/logistics/GeographicalArea.java` - Geographical area data
- `src/main/java/pojo/logistics/LossHistory.java` - Loss history data
- `src/main/java/pojo/logistics/PremiumSummary.java` - Premium summary data

## Test Scenarios

### 1. Valid Premium Calculation
Tests the API with valid submission data and verifies:
- Status code 200
- Premium calculation fields are present
- Response time is acceptable
- Premium summary contains expected fields

### 2. LOB and Class Combinations
Tests different combinations of:
- LOB: CAR, WRH, CAR,WRH
- Class: SI

### 3. Invalid Data Handling
Tests API behavior with invalid submission data:
- Expects status code 400
- Validates error message structure

### 4. Authorization Testing
Tests API without proper authorization:
- Expects status code 401

## Running Tests

### Run All Calculate Premium Tests
```bash
mvn test -Dtest=CalculatePremiumTestRunner
```

### Run Specific Scenarios
```bash
mvn test -Dcucumber.filter.tags="@calculatePremium"
```

### Run with Specific Environment
```bash
mvn test -Dtier=LOGISTICS-DEV -Dtest=CalculatePremiumTestRunner
```

## Request Structure

The API expects a JSON payload with the following key fields:

```json
{
  "_id": {
    "timestamp": 1747404043,
    "date": "2025-05-16T14:00:43.000+00:00"
  },
  "insuredInfo": {
    "businessName": "Test Business",
    "insuredId": "eeb7d55b-6549-476c-a264-290b4819ed29",
    "clientId": "e6cb5a81-0822-4b20-86db-13e3934eaaea",
    "state": "AZ"
  },
  "class": ["SI"],
  "lob": ["CAR", "WRH"],
  "status": "REVIEW",
  "businessType": "New Business",
  "gfrLast12Months": "1000000",
  "gfrNext12Months": "120000000",
  "rate": 0.052,
  "premium": 62890
}
```

## Response Validation

The tests validate the following response fields:

### Required Fields
- `premium` - Calculated premium amount
- `rate` - Applied rate
- `rollOverPremium` - Roll over premium amount
- `rollOverRate` - Roll over rate
- `premiumSummary` - Object containing premium breakdown

### Premium Summary Fields
- `totalPremium` - Total calculated premium
- `brokerCommission` - Broker commission amount
- `warehouseLegalLiabilityPremiumSummary`
- `cargoLiabilitiesPremiumSummary`
- `shipperInterestPremiumSummary`

## Configuration

### Environment Configuration
The tests use the existing environment configuration from `config.properties`:
- `tier=LOGISTICS-DEV` (or other environments)
- Authentication via Keycloak
- API host configuration

### Test Data
- Valid test data: Generated via `CalculatePremiumHelper.createValidRequest()`
- Invalid test data: Minimal JSON with invalid fields
- Custom LOB/Class combinations: Generated dynamically

## Error Handling

The framework handles various error scenarios:

1. **Invalid Request Data**: Returns 400 with error details
2. **Authentication Failure**: Returns 401
3. **Server Errors**: Returns 5xx with appropriate error messages

## Extending Tests

### Adding New Test Scenarios
1. Add new scenario to the feature file
2. Implement step definitions if needed
3. Update helper classes for new data combinations

### Adding New Validations
1. Add validation methods to `CalculatePremiumSteps.java`
2. Create new assertion helpers if needed
3. Update POJO classes for new response fields

## Best Practices

1. **Use POJO Classes**: Always use the provided POJO classes for type safety
2. **Helper Methods**: Use `CalculatePremiumHelper` for creating test data
3. **Logging**: Use the logger for debugging and traceability
4. **Assertions**: Provide meaningful assertion messages
5. **Test Data**: Keep test data realistic and representative

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Check Keycloak configuration
   - Verify client ID and secrets
   - Ensure token is valid

2. **Invalid Request Format**
   - Validate JSON structure
   - Check required fields
   - Verify data types

3. **Environment Issues**
   - Confirm tier configuration
   - Check API host settings
   - Verify network connectivity

### Debug Mode
Enable debug logging by setting log level to DEBUG in your test configuration.

## Dependencies

The calculate premium automation requires:
- RestAssured for API testing
- Jackson for JSON processing
- Cucumber for BDD testing
- JUnit for assertions
- Existing authentication framework
