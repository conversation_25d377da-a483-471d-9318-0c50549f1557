# Create Policy Submission API Automation

This document describes the automation framework for testing the Create Policy Submission API endpoint.

## Overview

The Create Policy Submission API (`/core/policy-service/submissions`) is used to create new insurance policy submissions with basic information that can later be expanded into full policies.

## API Details

**Endpoint**: `POST /core/policy-service/submissions`

**Request Structure**:
```json
{
  "insuredInfo": {
    "name": "",
    "businessName": "Test Business Automation"
  },
  "class": ["SI"],
  "lob": ["CAR"],
  "status": "REVIEW"
}
```

**Response Structure**:
```json
{
  "data": {
    "_id": { "timestamp": 1749462971, "date": "2025-06-09T09:56:11.000+00:00" },
    "insuredInfo": { /* detailed insured information */ },
    "class": ["SI"],
    "lob": ["CAR"],
    "status": "REVIEW",
    "submissionId": "e5c5b825-e07a-48df-9a38-ac26371e450c",
    "proposalNumber": "001_L22216",
    /* ... comprehensive policy data ... */
  },
  "success": true,
  "message": "Request Processed Successfully",
  "timestamp": "2025-06-09T11:29:00+0000"
}
```

## Test Structure

### Feature Files
- **CreatePolicySubmission.feature**: Basic policy creation scenarios
- **PolicyDetailedValidation.feature**: Comprehensive response validation

### Step Definitions
- **PolicyDetailedValidationSteps.java**: Complete validation framework
- **Helper Class**: PolicySubmissionHelper.java for request creation

### POJO Classes
- **PolicySubmissionRequest.java**: Request structure
- **PolicySubmissionDetailedResponse.java**: Complete response structure
- Supporting classes for nested objects

## Test Scenarios

### 1. Basic Policy Creation
```gherkin
Scenario: Create a new policy submission with valid data
  Given I have a valid policy submission payload
  When I send POST request to create policy submission
  Then the status code should be 200
  And the response should contain submission details
  And the policy status should be "REVIEW"
```

### 2. LOB and Class Combinations
```gherkin
Scenario Outline: Create policy submission with different LOB and class combinations
  Given I have policy submission payload with LOB "<lob>" and class "<class>"
  When I send POST request to create policy submission
  Then the status code should be 200
  Examples:
    | lob | class |
    | CAR | SI    |
    | CAR | CL    |
    | WRH | SI    |
```

### 3. Comprehensive Validation
```gherkin
Scenario: Validate comprehensive policy submission response
  Given I have a valid policy submission payload
  When I send POST request to create policy submission
  Then the status code should be 200
  And validate detailed policy response structure
  And validate policy core information
  And validate insured information details
  And validate premium and rating information
```

## Key Step Definitions

### Request Preparation
- `I have a valid policy submission payload`: Creates standard submission request
- `I have policy submission payload with LOB {string} and class {string}`: Creates request with specific LOB/class

### API Interaction
- `I send POST request to create policy submission`: Makes authenticated POST request
- `I send POST request to create policy submission without authorization`: Tests unauthorized access

### Response Validation
- `the response should contain submission details`: Validates basic submission fields
- `the policy status should be {string}`: Validates specific status value
- `validate policy submission fields`: Validates core submission fields

### Comprehensive Validation
- `validate detailed policy response structure`: Top-level response validation
- `validate policy core information`: Essential policy fields
- `validate insured information details`: Complete insured data
- `validate premium and rating information`: Financial calculations
- `validate commodity types information`: Commodity structure
- `validate geographical information`: Coverage areas and scope
- `validate terms and conditions structure`: Policy terms

## Helper Methods

### PolicySubmissionHelper Class
```java
// Create basic request
PolicySubmissionRequest request = PolicySubmissionHelper.createValidSubmissionRequest();

// Create with specific LOB/class
PolicySubmissionRequest request = PolicySubmissionHelper.createSubmissionRequestWithLobAndClass("CAR", "SI");

// Convert to JSON
String json = PolicySubmissionHelper.toJsonString(request);

// Create simple JSON directly
String json = PolicySubmissionHelper.createSimpleSubmissionJson();
```

### Request Variations
- `createValidSubmissionRequest()`: Standard valid request
- `createSubmissionRequestWithLobAndClass()`: Custom LOB/class combinations
- `createSubmissionRequestWithBusinessName()`: Custom business name
- `createComprehensiveSubmissionRequest()`: Full request with all fields
- `createInvalidSubmissionRequest()`: Invalid request for negative testing

## Running Tests

### Run Basic Policy Creation Tests
```bash
mvn test -Dtest=CreatePolicySubmissionTestRunner
```

### Run Detailed Validation Tests
```bash
mvn test -Dtest=PolicyDetailedValidationTestRunner
```

### Run All Policy Tests
```bash
mvn test -Dcucumber.filter.tags="@createPolicy or @detailedValidation"
```

### Run with Environment
```bash
mvn test -Dtier=LOGISTICS-DEV -Dtest=CreatePolicySubmissionTestRunner
```

## Validation Categories

### Basic Validation
1. **Response Structure**: Success flag, message, timestamp
2. **Core Fields**: Submission ID, status, LOB, class
3. **Insured Info**: Business name, basic details

### Comprehensive Validation
1. **Policy Information**: Proposal number, underwriter, dates
2. **Financial Data**: Premium, rate, calculations
3. **Business Data**: Commodity types, geographical areas
4. **Compliance**: Terms and conditions, exclusions
5. **Audit Trail**: Creation/update timestamps, user tracking

## Best Practices

### Test Data Management
1. **Use Helper Classes**: Always use PolicySubmissionHelper for consistency
2. **Parameterized Tests**: Use scenario outlines for different combinations
3. **Realistic Data**: Use meaningful business names and realistic values

### Validation Strategy
1. **Layered Validation**: Start with basic structure, then detailed validation
2. **Business Rules**: Validate business logic, not just structure
3. **Error Scenarios**: Include negative testing for invalid requests

### Maintenance
1. **Modular Steps**: Keep step definitions focused and reusable
2. **Clear Naming**: Use descriptive step and method names
3. **Documentation**: Keep documentation updated with API changes

## Troubleshooting

### Common Issues
1. **Authentication Failures**: Check Keycloak token and client ID
2. **Invalid Request Format**: Verify JSON structure and required fields
3. **Missing Response Fields**: Check if API response structure changed

### Debug Tips
- Enable detailed logging for request/response inspection
- Use simple requests first to verify basic connectivity
- Check individual validation methods in isolation
- Compare actual vs expected response structures

This framework provides complete coverage for policy submission creation and validation, ensuring both basic functionality and comprehensive data integrity checking.
