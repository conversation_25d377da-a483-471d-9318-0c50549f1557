# Policy Search API Automation

This document describes the automation framework for testing the Policy Search API endpoint.

## Overview

The Policy Search API (`/core/policy-service/submissions/filter`) is used to search and filter insurance policies based on various criteria including product codes, status, underwriters, and free text search with pagination support.

## Test Structure

### Feature File
- **Location**: `src/test/resources/features/logistics/PolicySearch.feature`
- **Tags**: `@Logistics @policySearch`

### Step Definitions
- **Location**: `src/test/java/steps/logistics/PolicySearchSteps.java`
- **Helper Class**: `src/main/java/helpers/PolicySearchHelper.java`

### POJO Classes
- `src/main/java/pojo/logistics/PolicySearchRequest.java` - Search request object
- `src/main/java/pojo/logistics/PolicySearchResponse.java` - Search response object

## Test Scenarios

### 1. Basic Search Functionality
- **Valid Filter Criteria**: Tests API with standard search parameters
- **Empty Filters**: Tests API behavior with no filter criteria
- **Response Structure**: Validates response format and required fields

### 2. Product Code Filtering
Tests different combinations of product codes:
- Single product code (e.g., "CAR")
- Multiple product codes (e.g., "CAR,CL,FFE&O")
- All available product codes

### 3. Status Filtering
Tests different policy status combinations:
- Single status (e.g., "BOUND")
- Multiple statuses (e.g., "BOUND,QUOTED,REVIEW")
- All available statuses

### 4. Pagination Testing
Tests pagination functionality:
- Different page numbers (0, 1, 2)
- Different page sizes (10, 20, 30)
- Invalid pagination parameters
- Pagination metadata validation

### 5. Free Text Search
- Basic text search
- Special characters handling
- Empty text search

### 6. Underwriter Filtering
- Single underwriter
- Multiple underwriters
- Empty underwriter list

### 7. Combined Criteria
Tests multiple filters applied simultaneously:
- Free text + product codes + status
- All filter combinations

### 8. Error Handling
- Invalid pagination parameters
- Unauthorized access
- Malformed requests

## API Request Structure

The API expects a POST request with JSON payload and query parameters:

### Query Parameters
```
pageNumber=0&pageSize=30
```

### Request Body
```json
{
  "freeText": "",
  "productCode": ["CAR", "CL", "FFE&O", "FFLL", "IAC", "MTC", "NVOCC"],
  "underwriters": [],
  "logisticStatus": ["BOUND", "QUOTED", "REVIEW"]
}
```

### Request Fields
- **freeText**: String for text-based search across policy fields
- **productCode**: Array of product codes to filter by
- **underwriters**: Array of underwriter names to filter by
- **logisticStatus**: Array of policy statuses to filter by

## Response Structure

The API returns a paginated response:

```json
{
  "data": [
    {
      "submissionId": "string",
      "proposalNumber": "string",
      "insuredInfo": {
        "businessName": "string",
        "insuredId": "string",
        "state": "string",
        "companyName": "string"
      },
      "productCode": ["string"],
      "status": "string",
      "underwriter": "string",
      "effectiveDate": "string",
      "expiryDate": "string",
      "premium": 0.0,
      "businessType": "string"
    }
  ],
  "totalElements": 0,
  "totalPages": 0,
  "pageNumber": 0,
  "pageSize": 30,
  "first": true,
  "last": false,
  "numberOfElements": 0
}
```

## Running Tests

### Run All Policy Search Tests
```bash
mvn test -Dtest=PolicySearchTestRunner
```

### Run Specific Scenarios
```bash
mvn test -Dcucumber.filter.tags="@policySearch"
```

### Run with Specific Environment
```bash
mvn test -Dtier=LOGISTICS-DEV -Dtest=PolicySearchTestRunner
```

## Validation Points

### Response Validation
1. **Status Code**: Validates 200 for successful requests
2. **Response Structure**: Validates presence of required fields
3. **Data Integrity**: Validates policy data contains expected fields
4. **Pagination**: Validates pagination metadata accuracy

### Filter Validation
1. **Product Code Filtering**: Validates returned policies match requested product codes
2. **Status Filtering**: Validates returned policies have requested statuses
3. **Pagination**: Validates page size and number constraints
4. **Free Text**: Validates search functionality (basic validation)

### Error Validation
1. **Invalid Parameters**: Validates 400 responses for invalid input
2. **Authorization**: Validates 401 responses for unauthorized access
3. **Error Messages**: Validates error response structure

## Helper Methods

### PolicySearchHelper Class
- `createValidSearchRequest()`: Creates standard search request
- `createSearchRequestWithProductCodes(String)`: Creates request with specific product codes
- `createSearchRequestWithStatus(String)`: Creates request with specific statuses
- `createSearchRequestWithFreeText(String)`: Creates request with free text search
- `createSearchRequestWithUnderwriters(String)`: Creates request with underwriter filter
- `createEmptySearchRequest()`: Creates request with no filters
- `createSearchRequestWithMultipleCriteria()`: Creates request with combined filters

### Pagination Helpers
- `createPaginationParams(int, int)`: Creates valid pagination parameters
- `createInvalidPaginationParams()`: Creates invalid pagination for testing

## Configuration

### Environment Settings
Uses existing framework configuration:
- Authentication via Keycloak
- API host from decoder configuration
- Client ID from properties

### Test Data
- Default search criteria in helper methods
- Sample request data in `testdata/policy-search-request.json`
- Dynamic test data generation for different scenarios

## Best Practices

1. **Use Helper Methods**: Always use PolicySearchHelper for creating test data
2. **Validate Pagination**: Always check pagination metadata in responses
3. **Error Handling**: Test both positive and negative scenarios
4. **Performance**: Monitor response times for large result sets
5. **Data Validation**: Validate both structure and content of responses

## Troubleshooting

### Common Issues

1. **Empty Results**
   - Check filter criteria match existing data
   - Verify environment has test data
   - Check product codes and statuses are valid

2. **Pagination Issues**
   - Verify page numbers start from 0
   - Check page size limits
   - Validate total elements calculation

3. **Authentication Failures**
   - Check Keycloak token validity
   - Verify client ID configuration
   - Ensure proper authorization headers

### Debug Tips
- Enable debug logging to see request/response details
- Use smaller page sizes for easier debugging
- Test with empty filters first to verify basic connectivity
- Check API documentation for latest field requirements

## Dependencies

The policy search automation requires:
- RestAssured for API testing
- Jackson for JSON processing
- Cucumber for BDD testing
- JUnit for assertions
- Existing authentication framework
- Pagination support utilities
